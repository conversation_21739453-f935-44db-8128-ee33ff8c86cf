{"configurations": [{"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:InspireApp}", "name": "Debug TempPackage", "program": "${workspaceFolder:InspireApp}/.build/debug/TempPackage", "preLaunchTask": "swift: Build Debug TempPackage"}, {"type": "swift", "request": "launch", "args": [], "cwd": "${workspaceFolder:InspireApp}", "name": "Release TempPackage", "program": "${workspaceFolder:InspireApp}/.build/release/TempPackage", "preLaunchTask": "swift: Build Release TempPackage"}]}