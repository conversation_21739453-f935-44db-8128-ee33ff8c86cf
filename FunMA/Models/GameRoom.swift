import Foundation

// Model for a game room
struct GameRoom: Identifiable, Codable {
    let id: String
    let pinCode: String
    let hostId: String
    let hostName: String
    let grade: Grade
    let topic: Topic
    var game: Game  // Changed to var to allow updates to game properties
    let createdAt: Date
    var status: GameRoomStatus
    var players: [Player]
    var isGameStarted: Bool  // New flag to track if game has started
    var chargedStudentIds: Set<String> = Set<String>()  // Track students who have been charged for credits
    
    // Generate a random 6-digit pin code
    static func generatePinCode() -> String {
        String(format: "%06d", Int.random(in: 0...999999))
    }
}

// Model for a player in the game room
struct Player: Identifiable, Codable {
    let id: String
    let username: String
    let role: PlayerRole
    var joinedAt: Date
}

// Model for a game
struct Game: Identifiable, Codable {
    let id: String
    let name: String
    let type: GameType
    var url: String?  // For webview games - changed to var to allow updates
    let description: String
    let grade: Grade  // Add grade field to match MongoDB structure
}

// Model for a topic
struct Topic: Identifiable, Codable, Hashable {
    let id: String
    let name: String
    let description: String
    let grade: Grade
    var games: [Game]     // Multiple games can be associated with a topic
    
    // Implement Hashable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)  // Use id for hashing since it's unique
    }
    
    // Implement Equatable
    static func == (lhs: Topic, rhs: Topic) -> Bool {
        lhs.id == rhs.id  // Topics are equal if they have the same id
    }
}

// Enums
enum Grade: String, Codable, CaseIterable {
    case form1 = "form1"
    case form2 = "form2"
    case form3 = "form3"
    
    // Add a display name for UI purposes
    var displayName: String {
        switch self {
        case .form1: return "Form 1"
        case .form2: return "Form 2"
        case .form3: return "Form 3"
        }
    }
}

enum GameType: String, Codable, CaseIterable {
    case scratch = "Scratch"
    case geogebra = "GeoGebra"
    case quiz = "Quiz"  // Keeping quiz as an option for assessment
    case native = "Native"  // New type for native games
}

enum GameRoomStatus: String, Codable {
    case waiting = "Waiting for players"
    case active = "Game in progress"
    case completed = "Game completed"
    case cancelled = "Cancelled"
}

enum PlayerRole: String, Codable {
    case host = "Host"
    case player = "Player"
}

// Game room error
enum GameError: Error {
    case invalidGameRoomData
    case userNotAuthenticated
    case gameRoomNotFound
    case gameRoomFull
    case gameAlreadyStarted
    case gameAlreadyEnded
    case networkError
    case unknownError
    
    var localizedDescription: String {
        switch self {
        case .invalidGameRoomData:
            return "Invalid game room data"
        case .userNotAuthenticated:
            return "User not authenticated"
        case .gameRoomNotFound:
            return "Game room not found"
        case .gameRoomFull:
            return "Game room is full"
        case .gameAlreadyStarted:
            return "Game has already started"
        case .gameAlreadyEnded:
            return "Game has already ended"
        case .networkError:
            return "Network error occurred"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}

// ViewModel for managing game rooms with WebSocket support
class GameRoomManager: ObservableObject, GameServiceDelegate {
    static let shared = GameRoomManager()
    private let gameService = GameService.shared
    
    @Published var availableGrades: [Grade] = []
    @Published var availableTopics: [Topic] = []
    @Published var availableGames: [Game] = []
    @Published var selectedGrade: Grade? {
        didSet {
            print("GameRoomManager: selectedGrade changed to: \(selectedGrade?.displayName ?? "nil")")
            if let grade = selectedGrade {
                // Prevent infinite loop by checking if we're already loading
                guard !isLoading else {
                    print("GameRoomManager: Already loading, skipping fetchTopics")
                    return
                }
                Task {
                    print("GameRoomManager: Starting fetchTopics for grade: \(grade.displayName)")
                    await fetchTopics(for: grade)
                }
            } else {
                print("GameRoomManager: Clearing topics and games")
                availableTopics = []
                availableGames = []
                clearGamesCache() // Clear cache when grade changes
            }
        }
    }
    @Published var selectedTopic: Topic?
    @Published var selectedGame: Game?
    @Published var currentGameRoom: GameRoom?

    // Separate tracking for charged students to avoid race conditions
    private var chargedStudentIds: Set<String> = Set<String>()
    @Published var isLoading = false
    @Published var error: Error?
    @Published var isHosting = false
    @Published var isJoining = false
    @Published var players: [Player] = []
    @Published var gameStarted = false  // Track if game has been started to prevent multiple attempts
    
    // Cache for games by grade and topic
    private var gamesCache: [String: [Game]] = [:]
    
    private init() {
        gameService.delegate = self
    }
    
    // Helper to generate cache key
    private func cacheKey(grade: Grade, topic: Topic) -> String {
        return "\(grade.rawValue)_\(topic.id)"
    }
    
    // Helper to get cached games
    private func getCachedGames(grade: Grade, topic: Topic) -> [Game]? {
        return gamesCache[cacheKey(grade: grade, topic: topic)]
    }
    
    // Helper to set cached games
    private func setCachedGames(grade: Grade, topic: Topic, games: [Game]) {
        gamesCache[cacheKey(grade: grade, topic: topic)] = games
    }
    
    // Clear cache when grade changes
    private func clearGamesCache() {
        gamesCache.removeAll()
    }
    
    // Clear error state and retry
    func clearError() {
        error = nil
    }
    
    // Retry fetching data
    func retryFetchData() async {
        clearError()
        if let grade = selectedGrade {
            await fetchTopics(for: grade)
        } else {
            await fetchGrades()
        }
    }

    func fetchGrades() async {
        print("GameRoomManager: fetchGrades called")
        
        // Check if grades are already cached
        if let cachedGrades = gameService.getCachedGrades() {
            print("GameRoomManager: Using cached grades")
            await MainActor.run {
                self.availableGrades = cachedGrades
                self.isLoading = false
                self.error = nil
            }
            return
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            let grades = try await gameService.fetchGrades()
            await MainActor.run {
                self.availableGrades = grades
                self.isLoading = false
                self.error = nil
                print("GameRoomManager: Successfully fetched \(grades.count) grades")
            }
        } catch {
            await MainActor.run {
                // Check if it's a network error and provide a helpful message
                if error is URLError {
                    self.error = NSError(domain: "GameRoomManager", code: -1, userInfo: [
                        NSLocalizedDescriptionKey: "Network connection issue. Please check your internet connection and try again."
                    ])
                } else {
                    self.error = error
                }
                self.isLoading = false
                print("GameRoomManager: Error fetching grades: \(error)")
            }
        }
    }
    
    func fetchTopics(for grade: Grade) async {
        print("GameRoomManager: fetchTopics called for grade: \(grade.displayName)")
        
        // Check if topics are already cached
        if let cachedTopics = gameService.getCachedTopics(for: grade) {
            print("GameRoomManager: Using cached topics for grade: \(grade.displayName)")
            await MainActor.run {
                self.availableTopics = cachedTopics
                self.isLoading = false
                self.error = nil
                
                // Auto-select first topic when grade changes
                if !cachedTopics.isEmpty {
                    self.selectedTopic = cachedTopics.first
                    print("GameRoomManager: Auto-selected first topic: \(cachedTopics.first?.name ?? "None")")
                    
                    // Fetch games for the auto-selected topic
                    if let firstTopic = cachedTopics.first {
                        Task {
                            await self.fetchGames(grade: grade, topic: firstTopic)
                        }
                    }
                } else {
                    self.selectedTopic = nil
                }
            }
            return
        }
        
        await MainActor.run {
            isLoading = true
            error = nil
            selectedTopic = nil
        }
        
        do {
            let topics = try await gameService.fetchTopics(for: grade)
            await MainActor.run {
                self.availableTopics = topics
                self.isLoading = false
                self.error = nil
                print("GameRoomManager: Successfully fetched \(topics.count) topics for grade: \(grade.displayName)")
                
                // Auto-select first topic when grade changes
                if !topics.isEmpty {
                    self.selectedTopic = topics.first
                    print("GameRoomManager: Auto-selected first topic: \(topics.first?.name ?? "None")")
                    
                    // Fetch games for the auto-selected topic
                    if let firstTopic = topics.first {
                        Task {
                            await self.fetchGames(grade: grade, topic: firstTopic)
                        }
                    }
                } else {
                    self.selectedTopic = nil
                }
            }
        } catch {
            await MainActor.run {
                // Check if it's a network error and provide a helpful message
                if error is URLError {
                    self.error = NSError(domain: "GameRoomManager", code: -1, userInfo: [
                        NSLocalizedDescriptionKey: "Network connection issue. Please check your internet connection and try again."
                    ])
                } else {
                    self.error = error
                }
                self.isLoading = false
                print("GameRoomManager: Error fetching topics for grade \(grade.displayName): \(error)")
            }
        }
    }
    
    func selectTopic(_ topic: Topic) {
        // Only update if selecting a different topic
        guard selectedTopic?.id != topic.id else { return }
        
        print("GameRoomManager: Selecting topic: \(topic.name)")
        
        // Update the selected topic
        selectedTopic = topic
        
        // Always fetch fresh games for the selected topic since cached topics have empty games
        print("GameRoomManager: Fetching games for topic: \(topic.name)")
        Task {
            await fetchGames(grade: selectedGrade!, topic: topic)
        }
    }
    
    private func fetchGames(grade: Grade, topic: Topic) async {
        print("GameRoomManager: fetchGames called for grade: \(grade.rawValue), topic: \(topic.name)")
        
        do {
            let games = try await gameService.fetchGames(for: grade, topic: topic)
            print("GameRoomManager: Successfully fetched \(games.count) games for topic: \(topic.name)")
            
            await MainActor.run {
                self.availableGames = games
                self.error = nil
                print("GameRoomManager: Updated availableGames with \(games.count) games")
            }
        } catch {
            print("GameRoomManager: Error fetching games: \(error.localizedDescription)")
            await MainActor.run {
                self.availableGames = []
                // Check if it's a network error and provide a helpful message
                if error is URLError {
                    self.error = NSError(domain: "GameRoomManager", code: -1, userInfo: [
                        NSLocalizedDescriptionKey: "Network connection issue. Please check your internet connection and try again."
                    ])
                } else {
                    self.error = error
                }
            }
        }
    }
    
    // MARK: - Game Room Management
    
    func createGameRoom(selectedGame: Game) async throws {
        guard let grade = selectedGrade,
              let topic = selectedTopic else {
            throw GameError.invalidGameRoomData
        }
        
        print("GameRoomManager: Creating game room with grade: \(grade.displayName), topic: \(topic.name), game: \(selectedGame.name)")
        
        // Reset any existing game room state
        await MainActor.run {
            self.currentGameRoom = nil
            self.isHosting = false
            self.isJoining = false
            self.players = []
            self.error = nil
        }
        
        // Check if WebSocket server is reachable first
        let isServerReachable = await gameService.checkWebSocketServerReachability()
        if !isServerReachable {
            print("GameRoomManager: WebSocket server is not reachable")
            throw GameError.networkError
        }
        
        // Connect to WebSocket first
        gameService.connectToGameRoom(gameRoomId: "create")
        
        // Send create room message to server with MongoDB data
        let teacherId = UserManager.shared.currentUser.id
        let teacherName = UserManager.shared.currentUser.username
        
        // Generate gameId based on grade and topic (you can modify this logic)
        let gameId = "\(grade.rawValue)-\(topic.id)-\(selectedGame.id)"
        
        // Ensure the game URL is properly formatted
        let gameUrl = selectedGame.url ?? ""
        
        print("GameRoomManager: Sending createRoom message with teacher ID: \(teacherId), name: \(teacherName), gameId: \(gameId), gameUrl: \(gameUrl)")
        gameService.sendMessage(.createRoom(
            grade: grade.displayName,
            topic: topic.name,
            game: selectedGame.name,
            teacherId: teacherId,
            teacherName: teacherName,
            gameId: gameId,
            gameUrl: gameUrl
        ))
        
        // Update state
        await MainActor.run {
            self.isHosting = true
            self.error = nil
        }
    }
    
    func joinGameRoom(pinCode: String, playerId: String, playerName: String) async throws {
        print("GameRoomManager: Starting joinGameRoom with PIN: \(pinCode), Player: \(playerName)")
        
        // Validate inputs according to WebSocket guide
        guard pinCode.count == 6 && pinCode.allSatisfy({ $0.isNumber }) else {
            throw NSError(domain: "GameRoomManager", code: 400, userInfo: [NSLocalizedDescriptionKey: "PIN code must be exactly 6 digits."])
        }
        
        guard !playerName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw NSError(domain: "GameRoomManager", code: 400, userInfo: [NSLocalizedDescriptionKey: "Student name is required."])
        }
        
        // Check if WebSocket server is reachable first
        let isServerReachable = await gameService.checkWebSocketServerReachability()
        if !isServerReachable {
            print("GameRoomManager: WebSocket server is not reachable")
            throw NSError(domain: "GameRoomManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "WebSocket server is not reachable. Please check your connection."])
        }
        
        // Connect to WebSocket first
        gameService.connectToGameRoom(gameRoomId: pinCode)
        
        // Update state
        await MainActor.run {
            self.isJoining = true
            self.error = nil
        }
        
        // Send join room message to server following WebSocket guide format
        print("GameRoomManager: Sending joinRoom message with format:")
        print("  - type: joinRoom")
        print("  - pin: \(pinCode)")
        print("  - studentName: \(playerName)")
        print("  - studentId: \(playerId)")
        
        gameService.sendMessage(.joinRoom(
            pin: pinCode,
            studentName: playerName.trimmingCharacters(in: .whitespacesAndNewlines),
            studentId: playerId
        ))
        
        print("GameRoomManager: joinRoom message sent, waiting for roomJoined response...")
        
        // Note: The actual room joining is handled by the WebSocket response
        // The roomJoined message will trigger the gameService(_:joinedRoom:) delegate method
        // which will update the currentGameRoom and set isJoining to false
    }
    
    func startGame() {
        guard isHosting && !gameStarted else { 
            print("GameRoomManager: Cannot start game - hosting: \(isHosting), already started: \(gameStarted)")
            return 
        }
        
        print("GameRoomManager: Starting game")
        gameService.sendMessage(.startGame)
        gameStarted = true  // Mark as started to prevent multiple attempts
    }
    
    func endGame() {
        guard isHosting else { return }
        gameService.sendMessage(.endGame)
    }
    
    func cancelGame() {
        guard isHosting else { return }
        gameService.sendMessage(.endGame)
        leaveGameRoom()
    }
    
    func leaveGameRoom() {
        print("GameRoomManager: Leaving game room and cleaning up...")

        // Send leave message if connected
        if gameService.isWebSocketAvailable() {
            gameService.sendMessage(.leaveRoom)
        }

        // Disconnect from WebSocket
        gameService.disconnectFromGameRoom()

        // Reset state on main thread
        DispatchQueue.main.async {
            self.currentGameRoom = nil
            self.isHosting = false
            self.isJoining = false
            self.players = []
            self.error = nil
            self.gameStarted = false  // Reset game started flag
            self.chargedStudentIds.removeAll()  // Clear charged students tracking
            print("GameRoomManager: Game room state reset")
        }
    }

    // MARK: - Credit Management

    /// Add a student ID to the charged students list
    func addChargedStudent(_ studentId: String) {
        addChargedStudents([studentId])
    }

    /// Add multiple student IDs to the charged students list (batch operation)
    func addChargedStudents(_ studentIds: [String]) {
        print("GameRoomManager: Before adding - Charged students: \(chargedStudentIds)")
        print("GameRoomManager: Adding student IDs: \(studentIds)")

        // Update the separate charged students tracking (no game room modification)
        DispatchQueue.main.async {
            for studentId in studentIds {
                self.chargedStudentIds.insert(studentId)
            }
            print("GameRoomManager: After adding - Charged students: \(self.chargedStudentIds)")
            print("GameRoomManager: ✅ Added \(studentIds.count) student(s) to charged list. Total charged: \(self.chargedStudentIds.count)")

            // Update the game room's charged students for persistence, but don't trigger a full update
            if var updatedRoom = self.currentGameRoom {
                updatedRoom.chargedStudentIds = self.chargedStudentIds
                self.currentGameRoom = updatedRoom
                print("GameRoomManager: Updated game room charged students for persistence")
            }
        }
    }

    /// Get the set of charged student IDs
    func getChargedStudentIds() -> Set<String> {
        return chargedStudentIds
    }

    /// Check if a student has been charged
    func isStudentCharged(_ studentId: String) -> Bool {
        return chargedStudentIds.contains(studentId)
    }

    /// Get the count of charged students
    func getChargedStudentCount() -> Int {
        return chargedStudentIds.count
    }
    
    // MARK: - GameServiceDelegate
    
    func gameService(_ service: GameService, createdRoom room: GameRoom) {
        print("GameRoomManager: Received createdRoom delegate call")
        print("GameRoomManager: Room hostId: \(room.hostId)")
        print("GameRoomManager: Current user ID: \(UserManager.shared.currentUser.id)")
        print("GameRoomManager: Is current user host: \(room.hostId == UserManager.shared.currentUser.id)")

        DispatchQueue.main.async {
            // Preserve charged students from separate tracking
            var updatedRoom = room
            updatedRoom.chargedStudentIds = self.chargedStudentIds
            print("GameRoomManager: Preserved \(self.chargedStudentIds.count) charged students from tracking")

            self.currentGameRoom = updatedRoom
            self.players = updatedRoom.players
        }
    }
    
    func gameService(_ service: GameService, joinedRoom room: GameRoom) {
        print("GameRoomManager: Received joinedRoom delegate call with room: \(room.id)")
        print("GameRoomManager: Room isGameStarted: \(room.isGameStarted)")
        print("GameRoomManager: Room status: \(room.status)")

        DispatchQueue.main.async {
            // Preserve charged students from separate tracking
            var updatedRoom = room
            updatedRoom.chargedStudentIds = self.chargedStudentIds
            print("GameRoomManager: Preserved \(self.chargedStudentIds.count) charged students from tracking")

            self.currentGameRoom = updatedRoom
            self.players = updatedRoom.players
            self.isJoining = false
            print("GameRoomManager: Updated currentGameRoom and players")

            // If the game has already started, update the status to active
            if updatedRoom.isGameStarted {
                print("GameRoomManager: Game already started, updating status to active")
                if var gameRoom = self.currentGameRoom {
                    gameRoom.status = .active
                    self.currentGameRoom = gameRoom
                }
            }
        }
    }
    
    func gameService(_ service: GameService, playerJoined player: Player) {
        print("GameRoomManager: Player joined: \(player.username)")
        print("GameRoomManager: Player ID: \(player.id)")
        print("GameRoomManager: Current user ID: \(UserManager.shared.currentUser.id)")

        DispatchQueue.main.async {
            // Update the players array
            if !self.players.contains(where: { $0.id == player.id }) {
                self.players.append(player)
                print("GameRoomManager: Added player to players array: \(player.username)")
            }

            // Check if this is the current user joining
            let isCurrentUser = player.id == UserManager.shared.currentUser.id
            print("GameRoomManager: Is current user joining: \(isCurrentUser)")

            // Update the currentGameRoom with the new players list
            if var updatedGameRoom = self.currentGameRoom {
                if !updatedGameRoom.players.contains(where: { $0.id == player.id }) {
                    updatedGameRoom.players.append(player)
                    print("GameRoomManager: Added player to game room players: \(player.username)")
                    print("GameRoomManager: Total players in room: \(updatedGameRoom.players.count)")

                    // Force UI update by triggering objectWillChange
                    self.objectWillChange.send()
                    self.currentGameRoom = updatedGameRoom

                    print("GameRoomManager: Updated currentGameRoom with new player: \(player.username)")
                    print("GameRoomManager: Current charged students: \(updatedGameRoom.chargedStudentIds.count)")

                    // Show notification for new player joining
                    if updatedGameRoom.isGameStarted {
                        print("GameRoomManager: \(player.username) joined the active game")
                    } else {
                        print("GameRoomManager: \(player.username) joined the waiting room")
                    }
                } else {
                    print("GameRoomManager: Player \(player.username) already exists in game room")
                }

                // If this is the current user joining an active game, ensure they get the full room state
                if isCurrentUser && updatedGameRoom.isGameStarted && !self.isHosting {
                    print("GameRoomManager: Current user joined active game, ensuring proper state")
                    // Force another UI update for current user joining active game
                    self.objectWillChange.send()
                    self.currentGameRoom = updatedGameRoom
                }
            } else {
                print("GameRoomManager: ❌ No current game room available for player join")
            }
        }
    }
    
    func gameService(_ service: GameService, playerLeft playerId: String) {
        DispatchQueue.main.async {
            self.players.removeAll { $0.id == playerId }
            
            // Update the currentGameRoom with the updated players list
            if var updatedGameRoom = self.currentGameRoom {
                updatedGameRoom.players.removeAll { $0.id == playerId }
                // Preserve charged students when updating room
                // (chargedStudentIds is already preserved since we're modifying the existing room)
                self.currentGameRoom = updatedGameRoom
                print("GameRoomManager: Updated currentGameRoom - player left: \(playerId)")
                print("GameRoomManager: Current charged students: \(updatedGameRoom.chargedStudentIds.count)")
            }
            
            // If host left, end the game room
            if self.isHosting && playerId == self.currentGameRoom?.hostId {
                self.leaveGameRoom()
            }
        }
    }
    
    func gameServiceDidStartGame(_ service: GameService) {
        DispatchQueue.main.async {
            self.gameStarted = true  // Mark game as started
            if var updatedGameRoom = self.currentGameRoom {
                updatedGameRoom.status = .active
                updatedGameRoom.isGameStarted = true
                // Preserve charged students when updating room
                // (chargedStudentIds is already preserved since we're modifying the existing room)
                self.currentGameRoom = updatedGameRoom
                print("GameRoomManager: Game started, charged students preserved: \(updatedGameRoom.chargedStudentIds.count)")
            }
        }
    }
    
    func gameServiceDidEndGame(_ service: GameService) {
        DispatchQueue.main.async {
            if var updatedGameRoom = self.currentGameRoom {
                updatedGameRoom.status = .completed
                // Preserve charged students when updating room
                // (chargedStudentIds is already preserved since we're modifying the existing room)
                self.currentGameRoom = updatedGameRoom
                print("GameRoomManager: Game ended, charged students preserved: \(updatedGameRoom.chargedStudentIds.count)")
            }
        }
    }
    
    func gameServiceDidLeaveRoom(_ service: GameService) {
        DispatchQueue.main.async {
            self.currentGameRoom = nil
            self.isHosting = false
            self.isJoining = false
            self.players = []
            self.error = nil
            self.chargedStudentIds.removeAll()  // Clear charged students tracking
        }
    }
    
    func gameService(_ service: GameService, didReceiveError error: Error) {
        print("GameRoomManager: Received error: \(error)")
        DispatchQueue.main.async {
            // Handle specific error types with appropriate user messages
            if error.localizedDescription.contains("Invalid PIN code") {
                self.error = NSError(domain: "GameRoomManager", code: 404, userInfo: [NSLocalizedDescriptionKey: "Invalid PIN code. Please check the 6-digit code from your teacher."])
            } else if error.localizedDescription.contains("Student name is required") {
                self.error = NSError(domain: "GameRoomManager", code: 400, userInfo: [NSLocalizedDescriptionKey: "Please enter your name to join the game."])
            } else if error.localizedDescription.contains("Room is full") {
                self.error = NSError(domain: "GameRoomManager", code: 429, userInfo: [NSLocalizedDescriptionKey: "This game room is full. Maximum 50 players allowed."])
            } else if error.localizedDescription.contains("no longer available") {
                self.error = NSError(domain: "GameRoomManager", code: 410, userInfo: [NSLocalizedDescriptionKey: "This game room is no longer available."])
            } else if error.localizedDescription.contains("Game has already been started") {
                self.error = NSError(domain: "GameRoomManager", code: 409, userInfo: [NSLocalizedDescriptionKey: "Game has already been started."])
                self.gameStarted = true  // Mark as started to prevent further attempts
            } else if error.localizedDescription.contains("WebSocket") || error.localizedDescription.contains("network") {
                self.error = NSError(domain: "GameRoomManager", code: -1, userInfo: [NSLocalizedDescriptionKey: "Connection failed. Please check your internet connection and try again."])
            } else {
                self.error = error
            }
            
            // Reset joining state on error
            self.isJoining = false
        }
    }
    
    func gameService(_ service: GameService, receivedGameUrl url: String) {
        print("GameRoomManager: Received game URL: \(url)")
        DispatchQueue.main.async {
            // Store the game URL for use in the WebView
            if var updatedGameRoom = self.currentGameRoom {
                updatedGameRoom.game.url = url
                // Preserve charged students when updating room
                // (chargedStudentIds is already preserved since we're modifying the existing room)
                self.currentGameRoom = updatedGameRoom
                print("GameRoomManager: Updated game room with URL: \(url)")
                print("GameRoomManager: Charged students preserved: \(updatedGameRoom.chargedStudentIds.count)")
            }
        }
    }

    // MARK: - Late Joiner Support
    // Note: Server automatically sends gameStarted message with URL when student joins active game
    // No need to request URL separately
} 
