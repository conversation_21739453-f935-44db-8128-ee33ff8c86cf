//
//  Message.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation

// Enhanced message model for supporting advanced formatting
struct Message: Identifiable, Equatable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date = Date()
    var status: MessageStatus = .sent
    
    // Message status for tracking message delivery
    enum MessageStatus {
        case sending
        case sent
        case error
    }
    
    // Convert from existing ChatMessage
    static func from(chatMessage: ChatMessage) -> Message {
        return Message(
            content: chatMessage.content,
            isFromUser: chatMessage.isUser
        )
    }
    
    // Convert to ChatMessage for compatibility with existing code
    func toChatMessage() -> ChatMessage {
        return ChatMessage(
            content: content,
            isUser: isFromUser,
            role: isFromUser ? .user : .assistant
        )
    }
} 