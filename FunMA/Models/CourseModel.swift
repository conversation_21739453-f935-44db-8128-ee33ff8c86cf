//
//  TestModel.swift
//  Luminous Education
//
//  Created by <PERSON> on 10/9/2024.
//

import Foundation

// Lesson model representing a lesson in a course
struct Lesson: Identifiable, Codable, Hashable {
    var id: String { lessonID }
    let lessonID: String
    let name: String
    let description: String
    let duration: Int
    let videoLink: String
    let image: String
    
    // Implement hash(into:) for Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(lessonID)
    }
    
    // Implement == for Hashable conformance
    static func == (lhs: Lesson, rhs: Lesson) -> Bool {
        return lhs.lessonID == rhs.lessonID
    }
    
    enum CodingKeys: String, CodingKey {
        case lessonID = "lessonId"
        case name = "name"
        case description = "description"
        case duration = "duration"
        case videoLink = "videoLink"
        case image = "image"
    }
}

struct Course: Identifiable, Codable, Hashable {
    var id: String { courseID }
    let courseID: String
    let name: String
    let description: String
    let difficulty: Int
    let totalLessons: Int
    let credit: Int
    let image: String
    let lessons: [Lesson]
    
    // Additional properties not in JSON but needed for the app
    var total: Int = 100
    
    // Implement hash(into:) for Hashable conformance
    func hash(into hasher: inout Hasher) {
        hasher.combine(courseID)
    }
    
    // Implement == for Hashable conformance
    static func == (lhs: Course, rhs: Course) -> Bool {
        return lhs.courseID == rhs.courseID
    }
    
    // Add CourseLevel enum - only what's needed to fix the error
    enum CourseLevel: String, CaseIterable, Identifiable, Codable {
        case beginner = "Beginner"
        case intermediate = "Intermediate"
        case advanced = "Advanced"
        
        var id: String { self.rawValue }
    }
    
    // Computed property to convert difficulty to CourseLevel
    var level: CourseLevel {
        switch difficulty {
        case 1:
            return .beginner
        case 2:
            return .intermediate
        case 3:
            return .advanced
        default:
            return .beginner
        }
    }
    
    // Minimal required bridge properties
    var title: String { name }
    var category: String {
        let lowercaseName = name.lowercased()
        
        if lowercaseName.contains("math") {
            return "Mathematics"
        } else if lowercaseName.contains("english") {
            return "English"
        } else if lowercaseName.contains("science") {
            return "Science"
        } else if lowercaseName.contains("chinese") {
            return "Chinese"
        } else {
            return "General"
        }
    }
    var imageURL: String { image }
    var isEnrolled: Bool = false
    
    enum CodingKeys: String, CodingKey {
        case courseID = "courseId"
        case name = "name"
        case description = "description"
        case difficulty = "difficulty"
        case totalLessons = "totalLessons"
        case credit = "credit"
        case image = "image"
        case lessons = "lessons"
        case total = "total"
    }
    
    // Basic initializer for creating courses directly
    init(courseId: String, name: String, description: String, difficulty: Int, totalLessons: Int, credit: Int, image: String, lessons: [Lesson], total: Int = 100) {
        self.courseID = courseId
        self.name = name
        self.description = description
        self.difficulty = difficulty
        self.totalLessons = totalLessons
        self.credit = credit
        self.image = image
        self.lessons = lessons
        self.total = total
    }
    
    // Custom decoding to handle optional fields
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        // Required fields
        courseID = try container.decode(String.self, forKey: .courseID)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        difficulty = try container.decode(Int.self, forKey: .difficulty)
        totalLessons = try container.decode(Int.self, forKey: .totalLessons)
        credit = try container.decode(Int.self, forKey: .credit)
        image = try container.decode(String.self, forKey: .image)
        lessons = try container.decode([Lesson].self, forKey: .lessons)
        
        // Handle total field with default
        if let totalValue = try? container.decode(Int.self, forKey: .total) {
            total = totalValue
        } else {
            total = 100
        }
    }
}

// Type alias for LessonCourse to support existing code referring to this type
typealias LessonCourse = Course

// Model for a lesson's progress within a course
struct LessonProgress: Identifiable, Codable {
    var id: String { lessonId }
    let lessonNumber: Int
    let lessonId: String
    let score: Int
    let status: String
    
    enum CodingKeys: String, CodingKey {
        case lessonNumber = "lessonNumber"
        case lessonId = "lessonId"
        case score = "score"
        case status = "status"
    }
}

// Model for user progress in a course
struct UserProgress: Identifiable, Codable {
    let id: String
    let username: String
    let courseId: String
    let progress: Int
    let score: Int
    let lessonsProgress: [LessonProgress]
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case username = "username"
        case courseId = "courseId"
        case progress = "progress"
        case score = "score"
        case lessonsProgress = "lessonsProgress"
    }
}
