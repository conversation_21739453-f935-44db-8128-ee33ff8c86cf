import Foundation
import SwiftUI
import UniformTypeIdentifiers
import PhotosUI

// Supported file types for AnythingLLM
enum SupportedFileType: String, CaseIterable {
    case image = "image"
    case document = "document"
    case pdf = "pdf"
    case text = "text"
    
    var mimeTypes: [String] {
        switch self {
        case .image:
            return ["image/jpeg", "image/png", "image/gif", "image/webp", "image/heic"]
        case .document:
            return ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", 
                   "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                   "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation"]
        case .pdf:
            return ["application/pdf"]
        case .text:
            return ["text/plain", "text/csv", "text/markdown"]
        }
    }
    
    var fileExtensions: [String] {
        switch self {
        case .image:
            return ["jpg", "jpeg", "png", "gif", "webp", "heic"]
        case .document:
            return ["doc", "docx", "xls", "xlsx", "ppt", "pptx"]
        case .pdf:
            return ["pdf"]
        case .text:
            return ["txt", "csv", "md"]
        }
    }
    
    var displayName: String {
        switch self {
        case .image:
            return "Images"
        case .document:
            return "Documents"
        case .pdf:
            return "PDFs"
        case .text:
            return "Text Files"
        }
    }
}

// File attachment model
struct FileAttachment: Identifiable, Equatable {
    let id = UUID()
    let name: String
    let mimeType: String
    let contentString: String // Base64 encoded content
    let fileSize: Int
    let originalURL: URL?
    
    var displayName: String {
        return name.isEmpty ? "Untitled" : name
    }
    
    var formattedFileSize: String {
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useKB, .useMB, .useGB]
        formatter.countStyle = .file
        return formatter.string(fromByteCount: Int64(fileSize))
    }
    
    static func == (lhs: FileAttachment, rhs: FileAttachment) -> Bool {
        return lhs.id == rhs.id
    }
}

// File upload error types
enum FileUploadError: Error, LocalizedError {
    case fileTooLarge(Int64)
    case unsupportedFileType(String)
    case failedToReadFile
    case failedToConvertToBase64
    case noFileSelected
    
    var errorDescription: String? {
        switch self {
        case .fileTooLarge(let size):
            let formatter = ByteCountFormatter()
            formatter.allowedUnits = [.useMB]
            formatter.countStyle = .file
            let sizeString = formatter.string(fromByteCount: size)
            return "File is too large (\(sizeString)). Maximum size is 10MB."
        case .unsupportedFileType(let type):
            return "Unsupported file type: \(type)"
        case .failedToReadFile:
            return "Failed to read file"
        case .failedToConvertToBase64:
            return "Failed to convert file to base64"
        case .noFileSelected:
            return "No file selected"
        }
    }
}

@MainActor
class FileUploadManager: NSObject, ObservableObject {
    @Published var selectedFiles: [FileAttachment] = []
    @Published var isProcessing = false
    @Published var error: String? = nil
    
    // Maximum file size (10MB)
    private let maxFileSize: Int64 = 10 * 1024 * 1024
    
    override init() {
        super.init()
    }
    
    // MARK: - File Processing
    
    func processFile(at url: URL) async throws -> FileAttachment {
        // Check if file exists and is accessible
        guard FileManager.default.fileExists(atPath: url.path) else {
            throw FileUploadError.failedToReadFile
        }
        
        let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
        let fileSize = fileAttributes[.size] as? Int64 ?? 0
        
        // Check file size
        guard fileSize <= maxFileSize else {
            throw FileUploadError.fileTooLarge(fileSize)
        }
        
        // Get file data with better error handling
        let fileData: Data
        do {
            fileData = try Data(contentsOf: url)
        } catch {
            print("DEBUG: ❌ FileUploadManager: Failed to read file data from \(url.path): \(error)")
            throw FileUploadError.failedToReadFile
        }
        
        // Get MIME type
        let mimeType = getMimeType(for: url)
        
        // Convert to base64 with data URL format
        let base64String = fileData.base64EncodedString()
        let contentString = "data:\(mimeType);base64,\(base64String)"
        
        return FileAttachment(
            name: url.lastPathComponent,
            mimeType: mimeType,
            contentString: contentString,
            fileSize: Int(fileSize),
            originalURL: url
        )
    }
    
    private func getMimeType(for url: URL) -> String {
        let pathExtension = url.pathExtension.lowercased()
        
        switch pathExtension {
        case "jpg", "jpeg":
            return "image/jpeg"
        case "png":
            return "image/png"
        case "gif":
            return "image/gif"
        case "webp":
            return "image/webp"
        case "heic":
            return "image/heic"
        case "pdf":
            return "application/pdf"
        case "txt":
            return "text/plain"
        case "csv":
            return "text/csv"
        case "md":
            return "text/markdown"
        case "doc":
            return "application/msword"
        case "docx":
            return "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        case "xls":
            return "application/vnd.ms-excel"
        case "xlsx":
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        case "ppt":
            return "application/vnd.ms-powerpoint"
        case "pptx":
            return "application/vnd.openxmlformats-officedocument.presentationml.presentation"
        default:
            return "application/octet-stream"
        }
    }
    
    // MARK: - File Management
    
    func addFiles(_ files: [FileAttachment]) {
        selectedFiles.append(contentsOf: files)
    }
    
    func removeFile(_ file: FileAttachment) {
        selectedFiles.removeAll { $0.id == file.id }
    }
    
    func clearAllFiles() {
        selectedFiles.removeAll()
    }
} 