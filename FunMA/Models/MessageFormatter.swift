//
//  MessageFormatter.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation
import SwiftUI

class MessageFormatter {
    
    // Check if text contains code block with triple backticks
    static func containsCodeBlock(_ text: String) -> <PERSON>ol {
        return text.range(of: "```[\\s\\S]*?```", options: .regularExpression) != nil
    }
    
    // Check if text contains math expressions with $ symbols
    static func containsMathExpression(_ text: String) -> Bool {
        return text.range(of: "\\$[^$]+?\\$", options: .regularExpression) != nil
    }
    
    // Extract code blocks from text
    static func extractCodeBlocks(from text: String) -> [(String, range: Range<String.Index>, language: String?)] {
        var codeBlocks: [(String, range: Range<String.Index>, language: String?)] = []
        
        // Pattern for code blocks with backticks: ```language\ncode\n```
        let codeBlockPattern = "```([a-zA-Z0-9]*)\\s*\\n?([\\s\\S]*?)```"
        
        if let regex = try? NSRegularExpression(pattern: codeBlockPattern, options: []) {
            let nsString = text as NSString
            let matches = regex.matches(in: text, options: [], range: NSRange(location: 0, length: nsString.length))
            
            for match in matches {
                let languageRange = match.range(at: 1)
                let codeRange = match.range(at: 2)
                let fullRange = match.range(at: 0)
                
                if languageRange.location != NSNotFound && codeRange.location != NSNotFound {
                    let language = languageRange.length > 0 ? nsString.substring(with: languageRange) : nil
                    let code = nsString.substring(with: codeRange)
                    
                    let startIndex = text.index(text.startIndex, offsetBy: fullRange.location)
                    let endIndex = text.index(startIndex, offsetBy: fullRange.length)
                    
                    codeBlocks.append((code, range: startIndex..<endIndex, language: language))
                }
            }
        }
        
        return codeBlocks
    }
    
    // Extract math expressions from text
    static func extractMathExpressions(from text: String) -> [(String, range: Range<String.Index>)] {
        var mathExpressions: [(String, range: Range<String.Index>)] = []
        
        // Patterns for math expressions:
        // 1. Inline math with single $ on each side: $expression$
        // 2. Inline math with double $$ on each side: $$expression$$
        // 3. Block math with \[ and \]: \[expression\]
        
        // Pattern 1: $expression$
        let inlinePattern = "\\$([^$]+?)\\$"
        extractWithPattern(inlinePattern, from: text, into: &mathExpressions)
        
        // Pattern 2: $$expression$$
        let blockPattern = "\\$\\$([^$]+?)\\$\\$"
        extractWithPattern(blockPattern, from: text, into: &mathExpressions)
        
        // Pattern 3: \[expression\]
        let latexBlockPattern = "\\\\\\[([^\\]]+?)\\\\\\]"
        extractWithPattern(latexBlockPattern, from: text, into: &mathExpressions)
        
        return mathExpressions
    }
    
    // Helper method to extract expressions with a given pattern
    private static func extractWithPattern(_ pattern: String, from text: String, into expressions: inout [(String, range: Range<String.Index>)]) {
        if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
            let nsString = text as NSString
            let matches = regex.matches(in: text, options: [], range: NSRange(location: 0, length: nsString.length))
            
            for match in matches {
                let expressionRange = match.range(at: 1)
                let fullRange = match.range(at: 0)
                
                if expressionRange.location != NSNotFound {
                    let expression = nsString.substring(with: expressionRange)
                    
                    let startIndex = text.index(text.startIndex, offsetBy: fullRange.location)
                    let endIndex = text.index(startIndex, offsetBy: fullRange.length)
                    
                    expressions.append((expression, range: startIndex..<endIndex))
                }
            }
        }
    }
    
    // Parse markdown into attributed string with styling
    static func parseMarkdown(_ text: String, isFromUser: Bool, colorScheme: ColorScheme) -> AttributedString {
        // Default parsing options 
        var markdownOptions = AttributedString.MarkdownParsingOptions()
        markdownOptions.interpretedSyntax = .inlineOnlyPreservingWhitespace
        
        do {
            var attributedString = try AttributedString(markdown: text, options: markdownOptions)
            
            // Apply custom styling for different elements
            attributedString.foregroundColor = isFromUser ? .white : (colorScheme == .dark ? .white : .black)
            attributedString.font = .body
            
            return attributedString
        } catch {
            return AttributedString(text)
        }
    }
} 