//
//  InteractiveToolsModel.swift
//  Luminous Education
//
//  Created by <PERSON> on 6/11/2024.
//

import Foundation
import AVKit
import SwiftUI

// Tool types
enum InteractiveToolType: String, Identifiable {
    case arExperience = "AR Experience"
    case modelViewer = "3D Model Viewer"
    case aiAssistant = "AI Assistant"
    case interactiveDiagram = "Interactive Diagram"
    
    var id: String { self.rawValue }
    
    var iconName: String {
        switch self {
        case .arExperience: return "arkit"
        case .modelViewer: return "cube.transparent"
        case .aiAssistant: return "brain"
        case .interactiveDiagram: return "chart.bar.doc.horizontal"
        }
    }
    
    var color: Color {
        switch self {
        case .arExperience: return .blue
        case .modelViewer: return .purple
        case .aiAssistant: return .green
        case .interactiveDiagram: return .orange
        }
    }
}

// Timestamp with associated tool type
struct InteractiveToolEvent: Identifiable {
    let id = UUID()
    let timestamp: TimeInterval
    let toolType: InteractiveToolType
    let context: String // Additional context for the tool (e.g., which 3D model to load)
    
    init(timestamp: TimeInterval, toolType: InteractiveToolType, context: String = "") {
        self.timestamp = timestamp
        self.toolType = toolType
        self.context = context
    }
}

// Class to manage interactive tools display and behavior
class InteractiveToolsManager: ObservableObject {
    // Published properties
    @Published var showInteractiveTools = false
    @Published var currentToolEvent: InteractiveToolEvent?
    
    // Tool content state
    @Published var isToolContentActive = false  // If true, show tool content instead of selection
    
    // Interactive tools timestamps
    private var toolEvents: [InteractiveToolEvent]
    private var shownTools: Set<UUID> = []
    private var currentVideoTime: TimeInterval = 0
    
    // Reference to the video player
    private weak var player: AVPlayer?
    
    init(toolEvents: [InteractiveToolEvent] = [
        InteractiveToolEvent(timestamp: 20.0, toolType: .arExperience, context: "solar_system"),
        InteractiveToolEvent(timestamp: 70.0, toolType: .modelViewer, context: "dna_model")
    ]) {
        self.toolEvents = toolEvents.sorted { $0.timestamp < $1.timestamp }
    }
    
    // Public getter for tool timestamps
    var interactiveToolsTimestamps: [TimeInterval] {
        toolEvents.map { $0.timestamp }
    }
    
    // Set the player reference
    func setPlayer(_ player: AVPlayer) {
        self.player = player
    }
    
    // Update current video time and check if we should show tools
    func updateTime(_ time: TimeInterval) {
        currentVideoTime = time
        checkForInteractiveTools()
    }
    
    // Check if we should show interactive tools at current time
    private func checkForInteractiveTools() {
        guard !showInteractiveTools else { return }
        
        if let nextToolEvent = toolEvents.first(where: { event in
            event.timestamp <= currentVideoTime && !shownTools.contains(event.id)
        }) {
            // Pause the video when showing interactive tools
            player?.pause()
            showTools(event: nextToolEvent)
        }
    }
    
    // Display interactive tools
    private func showTools(event: InteractiveToolEvent) {
        currentToolEvent = event
        showInteractiveTools = true
        shownTools.insert(event.id)
    }
    
    // Launch the specified tool (can be called by UI)
    func launchCurrentTool() {
        guard currentToolEvent != nil else { return }
        
        // Show the tool content in the overlay
        isToolContentActive = true
    }
    
    // Close tool content and return to video
    func closeToolContent() {
        isToolContentActive = false
        closeInteractiveTools()
    }
    
    // Close interactive tools
    func closeInteractiveTools() {
        showInteractiveTools = false
        isToolContentActive = false
        currentToolEvent = nil
        // Resume video playback
        player?.play()
    }
    
    // Reset tools state
    func reset() {
        showInteractiveTools = false
        isToolContentActive = false
        currentToolEvent = nil
        shownTools.removeAll()
    }
} 