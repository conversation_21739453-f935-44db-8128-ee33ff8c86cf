import Foundation

struct CurriculumItem: Identifiable, Codable {
    let id: Int
    let grade: String
    let topic: String
    let subtopic: String
}

class CurriculumManager: ObservableObject {
    @Published var curriculumItems: [CurriculumItem] = []
    @Published var isLoading = false
    @Published var error: Error?
    
    static let shared = CurriculumManager()
    
    private init() {
        loadCurriculum()
    }
    
    private func loadCurriculum() {
        guard let url = Bundle.main.url(forResource: "curriculum", withExtension: "json") else {
            print("Error: curriculum.json not found")
            return
        }
        
        do {
            let data = try Data(contentsOf: url)
            let items = try JSONDecoder().decode([CurriculumItem].self, from: data)
            print("CurriculumManager: Loaded \(items.count) curriculum items")
            
            // Debug: Print first few items to see the data structure
            let firstFewItems = Array(items.prefix(5))
            for item in firstFewItems {
                print("CurriculumManager: Item - Grade: '\(item.grade)', Topic: '\(item.topic)', Subtopic: '\(item.subtopic)'")
            }
            
            DispatchQueue.main.async {
                self.curriculumItems = items
                print("CurriculumManager: Set curriculumItems with \(self.curriculumItems.count) items")
            }
        } catch {
            print("Error loading curriculum: \(error)")
            DispatchQueue.main.async {
                self.error = error
            }
        }
    }
    
    func getTopics(for grade: Grade) -> [String] {
        let gradeString = grade.rawValue
        print("CurriculumManager: Getting topics for grade: \(gradeString)")
        let itemsForGrade = curriculumItems.filter { $0.grade == gradeString }
        print("CurriculumManager: Found \(itemsForGrade.count) items for grade \(gradeString)")
        let topics = Set(itemsForGrade.map { $0.topic })
        let sortedTopics = Array(topics).sorted()
        print("CurriculumManager: Available topics: \(sortedTopics)")
        return sortedTopics
    }
    
    func getSubtopics(for grade: Grade, topic: String) -> [String] {
        let gradeString = grade.rawValue
        let itemsForGradeAndTopic = curriculumItems.filter { 
            $0.grade == gradeString && $0.topic == topic 
        }
        let subtopics = Set(itemsForGradeAndTopic.map { $0.subtopic })
        return Array(subtopics).sorted()
    }
    
    func searchTopics(for grade: Grade, searchText: String) -> [String] {
        print("CurriculumManager: searchTopics called with grade: \(grade.rawValue), searchText: '\(searchText)'")
        print("CurriculumManager: Total curriculum items: \(curriculumItems.count)")
        
        let topics = getTopics(for: grade)
        print("CurriculumManager: Available topics for grade \(grade.rawValue): \(topics)")
        
        if searchText.isEmpty {
            print("CurriculumManager: Search text is empty, returning all topics")
            return topics
        }
        
        let filteredTopics = topics.filter { topic in
            let contains = topic.localizedCaseInsensitiveContains(searchText)
            print("CurriculumManager: Checking topic '\(topic)' against search '\(searchText)': \(contains)")
            return contains
        }
        
        print("CurriculumManager: Search for '\(searchText)' returned \(filteredTopics.count) matches: \(filteredTopics)")
        return filteredTopics
    }
} 