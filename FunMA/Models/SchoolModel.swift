import Foundation

// MARK: - School Model
struct School: Identifiable, Codable {
    let id: String
    let schoolName: String
    let address: String?
    let region: String?
    let createdAt: Date?

    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case schoolName
        case address
        case region
        case createdAt
    }

    // For preview and testing
    static let preview = School(
        id: "507f1f77bcf86cd799439011",
        schoolName: "Sample School",
        address: "123 Sample Street, Sample District",
        region: "Hong Kong",
        createdAt: Date()
    )
}

// MARK: - School Creation Request
struct SchoolCreationRequest: Codable {
    let schoolName: String
    let address: String
    let region: String
}

// MARK: - Region Options
enum SchoolRegion: String, CaseIterable, Identifiable {
    case hongKong = "Hong Kong"
    case china = "China"
    case others = "Others"

    var id: String { self.rawValue }
}
