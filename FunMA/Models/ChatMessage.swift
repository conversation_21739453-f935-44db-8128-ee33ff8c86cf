//
//  ChatMessage.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation

// Message model for chat interface
struct ChatMessage: Identifiable, Equatable {
    let id = UUID()
    let content: String
    let isUser: Bool
    let timestamp: Date = Date()
    let role: MessageRole
    
    // Enum for message roles
    enum MessageRole: String {
        case system
        case user
        case assistant
    }
    
    // Default initializer with backward compatibility
    init(content: String, isUser: Bool, role: MessageRole? = nil) {
        self.content = content
        self.isUser = isUser
        // Determine role from isUser if not explicitly provided
        if let role = role {
            self.role = role
        } else {
            self.role = isUser ? .user : .assistant
        }
    }
    
    // Implement Equatable
    static func == (lhs: ChatMessage, rhs: ChatMessage) -> Bool {
        return lhs.id == rhs.id &&
               lhs.content == rhs.content &&
               lhs.isUser == rhs.isUser &&
               lhs.role == rhs.role
    }
} 