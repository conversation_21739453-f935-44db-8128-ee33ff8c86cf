//
//  UserManager.swift
//  Luminous Education
//
//  Created by <PERSON> on 10/9/2024.
//

import Foundation
import SwiftUI

// Extension to store raw data in the decoder
extension CodingUserInfoKey {
    static let rawData = CodingUserInfoKey(rawValue: "rawData")!
}

// Simple user model 
struct User: Identifiable, Codable {
    let id: String // MongoDB _id from the database
    let username: String
    var name: String = "" // Optional, may not be returned by the API
    let role: String
    let email: String?
    let firstName: String?
    let lastName: String?
    let credit: Int
    let school_id: String?
    
    // For mock data and previews
    static let preview = User(id: "507f1f77bcf86cd799439011", username: "U1", name: "<PERSON><PERSON>", role: "Student", email: "<EMAIL>", firstName: "Bet<PERSON>", lastName: "Smith", credit: 100, school_id: nil)
    
    // Role constants
    static let roleStudent = "Student"
    static let roleTeacher = "Teacher"
    static let roleGuest = "Guest"
    static let roleDeveloper = "Developer"
    
    // Helper properties
    var isTeacher: Bool { role == User.roleTeacher }
    var isStudent: Bool { role == User.roleStudent }
    var isGuest: Bool { role == User.roleGuest }
    var isDeveloper: Bool { role == User.roleDeveloper }
    
    // Convenience property for school ID
    var schoolId: String? { school_id }
    
    // Coding keys to map between JSON and Swift properties
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case username
        case name
        case role
        case email
        case firstName
        case lastName
        case credit
        case school_id
    }
    
    // Initialize with minimal data
    init(id: String, username: String, name: String = "", role: String, email: String? = nil, firstName: String? = nil, lastName: String? = nil, credit: Int = 0, school_id: String? = nil) {
        self.id = id
        self.username = username
        self.name = name
        self.role = role
        self.email = email
        self.firstName = firstName
        self.lastName = lastName
        self.credit = credit
        self.school_id = school_id
    }
    
    // Custom decoder to print debugging information
    init(from decoder: Decoder) throws {
        print("Attempting to decode User...")
        
        let container = try decoder.container(keyedBy: CodingKeys.self)
        print("Available keys in User: \(container.allKeys.map { $0.stringValue })")
        
        // Decode required fields
        id = try container.decode(String.self, forKey: .id)
        print("Decoded id: \(id)")
        
        username = try container.decode(String.self, forKey: .username)
        print("Decoded username: \(username)")
        
        role = try container.decode(String.self, forKey: .role)
        print("Decoded role: \(role)")
        
        // Decode credit with default value
        credit = try container.decodeIfPresent(Int.self, forKey: .credit) ?? 0
        print("Decoded credit: \(credit)")
        
        // Optional fields
        email = try container.decodeIfPresent(String.self, forKey: .email)
        print("Decoded email: \(email ?? "nil")")
        
        firstName = try container.decodeIfPresent(String.self, forKey: .firstName)
        print("Decoded firstName: \(firstName ?? "nil")")
        
        lastName = try container.decodeIfPresent(String.self, forKey: .lastName)
        print("Decoded lastName: \(lastName ?? "nil")")
        
        school_id = try container.decodeIfPresent(String.self, forKey: .school_id)
        print("Decoded school_id: \(school_id ?? "nil")")
        
        // Name is optional - construct from firstName/lastName if available, otherwise use empty string
        if container.contains(.name) {
            name = try container.decode(String.self, forKey: .name)
            print("Decoded name: \(name)")
        } else if let first = firstName, let last = lastName {
            name = "\(first) \(last)"
            print("Constructed name from firstName/lastName: \(name)")
        } else {
            name = ""
            print("Name field not present in JSON, using empty string")
        }
    }
}

// Authentication response model
struct AuthResponse: Codable {
    // Standard fields for successful response
    let access_token: String?
    let refresh_token: String?
    let user: User?
    
    // Error field
    let error: String?
    let message: String?
    
    // Check if this is an error response
    var isError: Bool {
        return error != nil
    }
    
    // Get the error message from either error or message field
    var errorMessage: String {
        return error ?? message ?? "Unknown error"
    }
    
    // Custom decoder to print debugging information
    init(from decoder: Decoder) throws {
        print("Attempting to decode AuthResponse...")
        
        // Print the raw data if possible
        if let jsonData = decoder.userInfo[.rawData] as? Data,
           let jsonString = String(data: jsonData, encoding: .utf8) {
            print("Raw JSON data: \(jsonString)")
        }
        
        let container = try decoder.container(keyedBy: CodingKeys.self)
        print("Available keys in AuthResponse: \(container.allKeys.map { $0.stringValue })")
        
        // Decode fields
        access_token = try container.decodeIfPresent(String.self, forKey: .access_token)
        print("Decoded access_token: \(access_token ?? "nil")")
        
        refresh_token = try container.decodeIfPresent(String.self, forKey: .refresh_token)
        print("Decoded refresh_token: \(refresh_token ?? "nil")")
        
        // Try to decode user
        do {
            user = try container.decodeIfPresent(User.self, forKey: .user)
            print("Decoded user: \(user?.username ?? "nil")")
        } catch {
            print("Error decoding user: \(error)")
            user = nil
        }
        
        error = try container.decodeIfPresent(String.self, forKey: .error)
        message = try container.decodeIfPresent(String.self, forKey: .message)
    }
    
    enum CodingKeys: String, CodingKey {
        case access_token
        case refresh_token
        case user
        case error
        case message
    }
}

// Singleton class to manage the current user
class UserManager: ObservableObject {
    // Shared instance
    static let shared = UserManager()
    
    // Published property for the current user
    @Published private(set) var currentUser: User
    
    // Published property to track login state
    @Published var isLoggedIn: Bool = false
    
    // Published property to track loading state
    @Published var isLoading: Bool = false
    
    // Published property for error messages
    @Published var errorMessage: String? = " "
    
    // JWT tokens
    private var accessToken: String = ""
    private var refreshToken: String = ""
    
    // Convenience property to access current user's school ID
    var currentUserSchoolId: String? {
        return currentUser.schoolId
    }

    // Method to update the current user (for credit updates, etc.)
    func updateCurrentUser(_ user: User) {
        currentUser = user

        // Save updated user data to UserDefaults
        if let userData = try? JSONEncoder().encode(user) {
            UserDefaults.standard.set(userData, forKey: "userData")
        }

        print("✅ UserManager: Updated current user - Credit: \(user.credit)")
    }

    // Convenience method to update just the credit
    func updateUserCredit(_ newCredit: Int) {
        let updatedUser = User(
            id: currentUser.id,
            username: currentUser.username,
            name: currentUser.name,
            role: currentUser.role,
            email: currentUser.email,
            firstName: currentUser.firstName,
            lastName: currentUser.lastName,
            credit: newCredit,
            school_id: currentUser.school_id
        )
        updateCurrentUser(updatedUser)
    }

    // Function to search for a user by username or email (admin/developer only)
    func searchUser(by identifier: String) async -> Result<User?, Error> {
        // Check if current user has admin/developer privileges
        guard currentUser.isDeveloper else {
            return .failure(NSError(domain: "UserManager", code: 403,
                                   userInfo: [NSLocalizedDescriptionKey: "Only developers can search users"]))
        }

        guard let authHeader = getAuthorizationHeader() else {
            return .failure(NSError(domain: "UserManager", code: 401,
                                   userInfo: [NSLocalizedDescriptionKey: "No authorization header available"]))
        }

        // Determine if the identifier is an email or username
        let isEmail = identifier.contains("@")
        let queryParam = isEmail ? "email" : "username"
        let encodedIdentifier = identifier.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

        guard let url = URL(string: "\(APIConfig.userSearchEndpoint)?\(queryParam)=\(encodedIdentifier)") else {
            return .failure(NSError(domain: "UserManager", code: 400,
                                   userInfo: [NSLocalizedDescriptionKey: "Invalid URL"]))
        }

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "GET"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }

        do {
            print("UserManager: Making user search request for \(queryParam): \(identifier)")

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                return .failure(NSError(domain: "UserManager", code: 400,
                                       userInfo: [NSLocalizedDescriptionKey: "Invalid response"]))
            }

            // Debug: Print raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("UserManager: Raw user search response: \(jsonString)")
            }

            if httpResponse.statusCode == 200 {
                do {
                    let user = try JSONDecoder().decode(User.self, from: data)
                    print("UserManager: ✅ Successfully found user: \(user.username)")
                    return .success(user)
                } catch {
                    print("UserManager: ❌ Failed to decode user search response: \(error)")
                    return .failure(error)
                }
            } else if httpResponse.statusCode == 404 {
                // User not found - return nil instead of error
                print("UserManager: ℹ️ User not found for \(queryParam): \(identifier)")
                return .success(nil)
            } else {
                let errorMessage = "User search failed with status: \(httpResponse.statusCode)"
                print("UserManager: ❌ \(errorMessage)")
                return .failure(NSError(domain: "UserManager", code: httpResponse.statusCode,
                                       userInfo: [NSLocalizedDescriptionKey: errorMessage]))
            }
        } catch {
            print("UserManager: ❌ User search error: \(error)")
            return .failure(error)
        }
    }

    // Function to adjust any user's credit (admin/developer only)
    func adjustUserCredit(userId: String, amount: Int, description: String? = nil) async -> Result<AdminCreditAdjustResponse, Error> {
        // Check if current user has admin/developer privileges
        guard currentUser.isDeveloper else {
            return .failure(NSError(domain: "UserManager", code: 403,
                                   userInfo: [NSLocalizedDescriptionKey: "Only developers can adjust user credits"]))
        }

        guard let authHeader = getAuthorizationHeader() else {
            return .failure(NSError(domain: "UserManager", code: 401,
                                   userInfo: [NSLocalizedDescriptionKey: "No authorization header available"]))
        }

        guard let url = URL(string: APIConfig.adminCreditAdjustEndpoint) else {
            return .failure(NSError(domain: "UserManager", code: 400,
                                   userInfo: [NSLocalizedDescriptionKey: "Invalid URL"]))
        }

        let request = AdminCreditAdjustRequest(
            userId: userId,
            amount: amount,
            description: description
        )

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
            print("UserManager: Making admin credit adjustment request: \(amount) credits for user \(userId)")

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                return .failure(NSError(domain: "UserManager", code: 400,
                                       userInfo: [NSLocalizedDescriptionKey: "Invalid response"]))
            }

            // Debug: Print raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("UserManager: Raw credit adjustment response: \(jsonString)")
            }

            if httpResponse.statusCode == 200 {
                do {
                    let adjustResponse = try JSONDecoder().decode(AdminCreditAdjustResponse.self, from: data)
                    print("UserManager: ✅ Successfully adjusted \(amount) credits for user \(userId). New balance: \(adjustResponse.actualBalance ?? 0)")
                    return .success(adjustResponse)
                } catch {
                    print("UserManager: ❌ Failed to decode response: \(error)")
                    // If decoding fails, still consider it successful if status is 200
                    let fallbackResponse = AdminCreditAdjustResponse(success: true, message: "Credit adjusted successfully", newBalance: nil, credit: nil)
                    return .success(fallbackResponse)
                }
            } else {
                let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
                print("UserManager: ❌ Failed to adjust credits (HTTP \(httpResponse.statusCode)): \(errorMessage)")
                return .failure(NSError(domain: "UserManager", code: httpResponse.statusCode,
                                       userInfo: [NSLocalizedDescriptionKey: errorMessage]))
            }
        } catch {
            print("UserManager: ❌ Error adjusting credits: \(error)")
            return .failure(error)
        }
    }
    
    // Convenience property to access the current access token
    var token: String? {
        return isLoggedIn ? accessToken : nil
    }
    
    // Private initializer for singleton
    private init() {
        // Initialize with a default user
        self.currentUser = User(id: "", username: "", role: "")

        print("🔄 UserManager: Initializing - checking for saved tokens")

        // Move token restoration to background to avoid blocking main thread
        Task {
            await restoreSessionFromStorage()
        }
    }

    // Restore session from UserDefaults on background thread
    private func restoreSessionFromStorage() async {
        // Check if we have saved tokens and try to restore the session
        if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String],
           let accessToken = savedTokens["accessToken"],
           let refreshToken = savedTokens["refreshToken"],
           let userData = UserDefaults.standard.data(forKey: "userData"),
           let user = try? JSONDecoder().decode(User.self, from: userData) {

            print("✅ UserManager: Found saved tokens and user data")
            print("   - accessToken length: \(accessToken.count)")
            print("   - refreshToken length: \(refreshToken.count)")
            print("   - User: \(user.username) (\(user.role))")

            // Update on main thread
            await MainActor.run {
                self.accessToken = accessToken
                self.refreshToken = refreshToken
                self.currentUser = user
                self.isLoggedIn = true
            }

            print("✅ UserManager: Session restored successfully")
        } else {
            print("❌ UserManager: No saved session found")
        }
    }
    
    // Function to authenticate with the server
    func login(username: String, password: String) async -> Bool {
        // Update UI properties on the main thread
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        do {
            // Create the request
            guard let url = URL(string: APIConfig.loginEndpoint) else {
                await MainActor.run {
                    errorMessage = "Invalid URL"
                    isLoading = false
                }
                return false
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Create the request body
            let body: [String: Any] = [
                "username": username,
                "password": password
            ]
            
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
            
            // Make the request
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // Debug: Print the raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("Raw login response: \(jsonString)")
            }
            
            // Check the response status
            guard let httpResponse = response as? HTTPURLResponse else {
                await MainActor.run {
                    errorMessage = "Invalid response"
                    isLoading = false
                }
                return false
            }
            
            if httpResponse.statusCode == 200 {
                // Try to decode the response as AuthResponse
                do {
                    // Create a decoder with the raw data
                    let decoder = JSONDecoder()
                    var userInfo = [CodingUserInfoKey: Any]()
                    userInfo[.rawData] = data
                    decoder.userInfo = userInfo
                    
                    let authResponse = try decoder.decode(AuthResponse.self, from: data)
                    
                    // Check if this is an error response
                    if authResponse.isError {
                        await MainActor.run {
                            self.errorMessage = authResponse.errorMessage
                            self.isLoading = false
                        }
                        return false
                    }
                    
                    // Ensure we have the required data
                    guard let accessToken = authResponse.access_token,
                          let refreshToken = authResponse.refresh_token,
                          let user = authResponse.user else {
                        await MainActor.run {
                            self.errorMessage = "Invalid response data"
                            self.isLoading = false
                        }
                        return false
                    }
                    
                    // Save the tokens and user data
                    print("🔑 UserManager: Saving tokens after successful login")
                    print("   - accessToken length: \(accessToken.count)")
                    print("   - refreshToken length: \(refreshToken.count)")
                    print("   - accessToken prefix: \(accessToken.prefix(30))...")
                    
                    self.accessToken = accessToken
                    self.refreshToken = refreshToken
                    
                    // Save to UserDefaults
                    let tokens = ["accessToken": accessToken, "refreshToken": refreshToken]
                    UserDefaults.standard.set(tokens, forKey: "authTokens")
                    print("✅ UserManager: Tokens saved to UserDefaults")
                    
                    if let userData = try? JSONEncoder().encode(user) {
                        UserDefaults.standard.set(userData, forKey: "userData")
                    }
                    
                    // Update the UI on the main thread
                    await MainActor.run {
                        self.currentUser = user
                        self.isLoggedIn = true
                        self.isLoading = false
                        self.errorMessage = nil
                    }

                    // If school_id is missing from login response, fetch complete profile in background
                    // This prevents blocking the login flow
                    if user.school_id == nil || user.school_id?.isEmpty == true {
                        print("⚠️ UserManager: school_id missing from login response, fetching complete profile in background...")
                        Task {
                            let profileSuccess = await fetchUserProfile()
                            if !profileSuccess {
                                print("⚠️ UserManager: Failed to fetch complete profile, but login was successful")
                            }
                        }
                    }

                    return true
                } catch {
                    // If we can't decode as AuthResponse, try to extract error message from raw JSON
                    print("Failed to decode AuthResponse: \(error.localizedDescription)")
                    
                    if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        print("Successfully parsed raw JSON: \(json)")
                        
                        // Try to manually extract the data we need
                        let accessToken = json["access_token"] as? String
                        let refreshToken = json["refresh_token"] as? String
                        
                        if let userDict = json["user"] as? [String: Any],
                           let userId = userDict["id"] as? String,
                           let username = userDict["username"] as? String,
                           let role = userDict["role"] as? String {
                            
                            print("Successfully extracted user data from JSON")
                            
                            // Create user object manually, explicitly including the name field
                            let userName = userDict["name"] as? String ?? ""
                            print("Extracted user name: \(userName)")
                            
                            let user = User(id: userId, 
                                           username: username, 
                                           name: userName, 
                                           role: role)
                            
                            // Save the tokens and user data
                            if let accessToken = accessToken, let refreshToken = refreshToken {
                                self.accessToken = accessToken
                                self.refreshToken = refreshToken
                                
                                // Save to UserDefaults
                                let tokens = ["accessToken": accessToken, "refreshToken": refreshToken]
                                UserDefaults.standard.set(tokens, forKey: "authTokens")
                                
                                if let userData = try? JSONEncoder().encode(user) {
                                    UserDefaults.standard.set(userData, forKey: "userData")
                                }
                                
                                // Update the UI on the main thread
                                await MainActor.run {
                                    self.currentUser = user
                                    self.isLoggedIn = true
                                    self.isLoading = false
                                    self.errorMessage = nil
                                }
                                
                                // If school_id is missing from login response, fetch complete profile in background
                                // This prevents blocking the login flow
                                if user.school_id == nil || user.school_id?.isEmpty == true {
                                    print("⚠️ UserManager: school_id missing from login response, fetching complete profile in background...")
                                    Task {
                                        let profileSuccess = await fetchUserProfile()
                                        if !profileSuccess {
                                            print("⚠️ UserManager: Failed to fetch complete profile, but login was successful")
                                        }
                                    }
                                }

                                return true
                            }
                        }
                        
                        // If we couldn't extract all the data we need, show an error
                        let errorMessage = json["error"] as? String ?? 
                                          json["message"] as? String ?? 
                                          "Unknown error format"
                        
                        await MainActor.run {
                            self.errorMessage = errorMessage
                            self.isLoading = false
                        }
                    } else {
                        await MainActor.run {
                            self.errorMessage = "Failed to parse response: \(error.localizedDescription)"
                            self.isLoading = false
                        }
                    }
                    return false
                }
            } else {
                // Failed login due to non-200 status code
                // Try to decode error message from response
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    let errorMessage = json["error"] as? String ?? 
                                      json["message"] as? String ?? 
                                      "Login failed: \(httpResponse.statusCode)"
                    
                    await MainActor.run {
                        self.errorMessage = errorMessage
                        self.isLoading = false
                    }
                } else {
                    await MainActor.run {
                        self.errorMessage = "Login failed: \(httpResponse.statusCode)"
                        self.isLoading = false
                    }
                }
                
                return false
            }
        } catch {
            // Handle network or parsing errors
            await MainActor.run {
                self.errorMessage = "Error: \(error.localizedDescription)"
                self.isLoading = false
            }
            
            return false
        }
    }
    
    // Function to logout the current user
    func logout() {
        // Clear tokens and user data
        self.accessToken = ""
        self.refreshToken = ""

        // Remove from UserDefaults
        UserDefaults.standard.removeObject(forKey: "authTokens")
        UserDefaults.standard.removeObject(forKey: "userData")

        // Clear any user-specific caches and state to ensure fresh start for next user
        clearUserSpecificState()

        // Update the UI
        DispatchQueue.main.async {
            self.currentUser = User(id: "", username: "", name: "", role: "")
            self.isLoggedIn = false
        }
    }

    // Helper function to clear user-specific state and caches
    private func clearUserSpecificState() {
        // Clear any navigation state (though we now use @State instead of @SceneStorage)
        UserDefaults.standard.removeObject(forKey: "selectedSidebarItem")

        // Clear any other user-specific cached data that might persist between sessions
        // This ensures each login session starts completely fresh

        // Note: We intentionally don't clear UI preferences like "isSidebarCollapsed"
        // as these are general UI preferences, not user-specific data
    }
    
    // Function to get the authorization header for API requests
    func getAuthorizationHeader() -> [String: String]? {
        // Quick return if we already have a token
        if !accessToken.isEmpty {
            return ["Authorization": "Bearer \(accessToken)"]
        }

        // Only check UserDefaults if we're logged in but missing token
        if isLoggedIn {
            if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String],
               let savedAccessToken = savedTokens["accessToken"],
               !savedAccessToken.isEmpty {
                self.accessToken = savedAccessToken
                if let savedRefreshToken = savedTokens["refreshToken"] {
                    self.refreshToken = savedRefreshToken
                }
                return ["Authorization": "Bearer \(savedAccessToken)"]
            }
        }

        return nil
    }
    
    // Function to check if user is authenticated and throw error if not
    func requireAuthentication() throws {
        // Check if user is logged in
        guard isLoggedIn else {
            throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                NSLocalizedDescriptionKey: "User is not logged in. Please log in to continue."
            ])
        }
        
        // Check if we have a valid access token
        guard !accessToken.isEmpty else {
            throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                NSLocalizedDescriptionKey: "No valid authentication token. Please log in again."
            ])
        }
        
        // Check if the current user has a valid ID
        guard !currentUser.id.isEmpty else {
            throw NSError(domain: "AuthenticationError", code: 401, userInfo: [
                NSLocalizedDescriptionKey: "Invalid user session. Please log in again."
            ])
        }
    }
    
    // Function to force a token refresh and authentication check
    func ensureValidAuthentication() async -> Bool {
        // First check if we're logged in
        guard isLoggedIn else {
            return false
        }

        // If access token is empty, try to restore from UserDefaults
        if accessToken.isEmpty {
            if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String],
               let savedAccessToken = savedTokens["accessToken"],
               !savedAccessToken.isEmpty {
                self.accessToken = savedAccessToken
                if let savedRefreshToken = savedTokens["refreshToken"] {
                    self.refreshToken = savedRefreshToken
                }
            }
        }

        // If still empty, try to refresh token
        if accessToken.isEmpty {
            let refreshSuccess = await refreshToken()
            if !refreshSuccess {
                return false
            }
        }

        // Final verification that we have a valid token
        return !accessToken.isEmpty && isLoggedIn && !currentUser.id.isEmpty
    }
    
    // Function to refresh the access token using the refresh token
    func refreshToken() async -> Bool {
        print("DEBUG: 🔄 Attempting to refresh access token...")
        
        // Check if we have a refresh token
        guard !refreshToken.isEmpty else {
            print("DEBUG: ❌ No refresh token available")
            return false
        }
        
        do {
            // Create the request
            guard let url = URL(string: APIConfig.refreshTokenEndpoint) else {
                print("DEBUG: ❌ Invalid refresh token URL")
                return false
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            // Add Authorization header with refresh token
            request.addValue("Bearer \(refreshToken)", forHTTPHeaderField: "Authorization")
            
            // Create the request body with refresh token
            let body: [String: Any] = [
                "refresh_token": refreshToken
            ]
            
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
            
            // Make the request
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // Debug: Print the raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("DEBUG: 📥 Raw refresh response: \(jsonString)")
            }
            
            // Check the response status
            guard let httpResponse = response as? HTTPURLResponse else {
                print("DEBUG: ❌ Invalid response")
                return false
            }
            
            if httpResponse.statusCode == 200 {
                // Try to decode the response
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    let newAccessToken = json["access_token"] as? String
                    let newRefreshToken = json["refresh_token"] as? String
                    
                    if let newAccessToken = newAccessToken {
                        print("DEBUG: ✅ Successfully refreshed access token")
                        print("DEBUG:    - New token length: \(newAccessToken.count)")
                        print("DEBUG:    - New token prefix: \(newAccessToken.prefix(30))...")
                        
                        // Update tokens
                        await MainActor.run {
                            self.accessToken = newAccessToken
                            if let newRefreshToken = newRefreshToken {
                                self.refreshToken = newRefreshToken
                                print("DEBUG:    - Also updated refresh token")
                            }
                        }
                        
                        // Save to UserDefaults
                        let tokens = ["accessToken": newAccessToken, "refreshToken": self.refreshToken]
                        UserDefaults.standard.set(tokens, forKey: "authTokens")
                        print("DEBUG: ✅ Tokens saved to UserDefaults after refresh")
                        
                        return true
                    } else {
                        print("DEBUG: ❌ No new access token in response")
                        return false
                    }
                } else {
                    print("DEBUG: ❌ Failed to parse refresh response")
                    return false
                }
            } else {
                print("DEBUG: ❌ Refresh failed with status: \(httpResponse.statusCode)")
                // If refresh fails, user needs to login again
                await MainActor.run {
                    self.logout()
                }
                return false
            }
        } catch {
            print("DEBUG: ❌ Refresh error: \(error)")
            return false
        }
    }
    
    // Function to get user progress records for the current user
    func filterProgressForCurrentUser(from allProgress: [UserProgress]) -> [UserProgress] {
        return allProgress.filter { $0.username == currentUser.username }
    }
    
    // Function to register a new user
    func register(username: String, password: String, firstName: String, lastName: String,
                 email: String, phone: String, gender: String, grade: String) async -> Result<Bool, Error> {
        return await register(username: username, password: password, firstName: firstName, lastName: lastName, email: email, phone: phone, gender: gender, grade: grade, schoolId: nil)
    }

    // Function to register a new user with school ID
    func register(username: String, password: String, firstName: String, lastName: String,
                 email: String, phone: String, gender: String, grade: String, schoolId: String?) async -> Result<Bool, Error> {
        return await register(username: username, password: password, firstName: firstName, lastName: lastName, email: email, phone: phone, gender: gender, grade: grade, schoolId: schoolId, role: "Student")
    }

    // Function to register a new user with school ID and role
    func register(username: String, password: String, firstName: String, lastName: String,
                 email: String, phone: String, gender: String, grade: String, schoolId: String?, role: String) async -> Result<Bool, Error> {
        // Update UI properties on the main thread
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        do {
            // Create the request
            guard let url = URL(string: APIConfig.registerEndpoint) else {
                await MainActor.run {
                    errorMessage = "Invalid URL"
                    isLoading = false
                }
                return .failure(NSError(domain: "UserManager", code: 400, 
                                       userInfo: [NSLocalizedDescriptionKey: "Invalid URL"]))
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Create the request body
            var body: [String: Any] = [
                "username": username,
                "password": password,
                "firstName": firstName,
                "lastName": lastName,
                "email": email,
                "phone": phone,
                "gender": gender,
                "grade": grade,
                "role": role
            ]

            // Add school_id if provided
            if let schoolId = schoolId {
                body["school_id"] = schoolId
            }
            
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
            
            // Make the request
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // Debug: Print the raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("Raw register response: \(jsonString)")
            }
            
            // Check the response status
            guard let httpResponse = response as? HTTPURLResponse else {
                await MainActor.run {
                    errorMessage = "Invalid response"
                    isLoading = false
                }
                return .failure(NSError(domain: "UserManager", code: 400, 
                                       userInfo: [NSLocalizedDescriptionKey: "Invalid response"]))
            }
            
            // Process the response
            if httpResponse.statusCode == 200 || httpResponse.statusCode == 201 {
                // Registration successful
                await MainActor.run {
                    isLoading = false
                }
                return .success(true)
            } else {
                // Registration failed
                if let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                    let errorMessage = json["error"] as? String ?? 
                                      json["message"] as? String ?? 
                                      "Registration failed: \(httpResponse.statusCode)"
                    
                    await MainActor.run {
                        self.errorMessage = errorMessage
                        isLoading = false
                    }
                    
                    return .failure(NSError(domain: "UserManager", code: httpResponse.statusCode, 
                                          userInfo: [NSLocalizedDescriptionKey: errorMessage]))
                } else {
                    let errorMessage = "Registration failed: \(httpResponse.statusCode)"
                    
                    await MainActor.run {
                        self.errorMessage = errorMessage
                        isLoading = false
                    }
                    
                    return .failure(NSError(domain: "UserManager", code: httpResponse.statusCode, 
                                          userInfo: [NSLocalizedDescriptionKey: errorMessage]))
                }
            }
        } catch {
            // Handle network or parsing errors
            await MainActor.run {
                self.errorMessage = "Error: \(error.localizedDescription)"
                isLoading = false
            }
            
            return .failure(error)
        }
    }
    
    // Function to login as guest
    func loginAsGuest() async -> Bool {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }

        // Small delay to ensure smooth UI transitions
        try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds

        // Create a guest user
        let guestUser = User(
            id: UUID().uuidString,
            username: "guest_\(Int.random(in: 1000...9999))",
            name: "Guest Student",
            role: User.roleGuest
        )

        // Update the UI on the main thread
        await MainActor.run {
            self.currentUser = guestUser
            self.isLoggedIn = true
            self.isLoading = false
            self.errorMessage = nil
        }

        return true
    }
    
    // Function to check if a feature is available for the current user
    func isFeatureAvailable(_ feature: Feature) -> Bool {
        switch feature {
        case .myCourses:
            return !currentUser.isGuest
        case .myPerformance:
            return !currentUser.isGuest
        case .recommendations:
            return !currentUser.isGuest
        case .teacherDashboard:
            return currentUser.isTeacher
        }
    }
    
    // Available features in the app
    enum Feature {
        case myCourses
        case myPerformance
        case recommendations
        case teacherDashboard
    }
    
    // Debug method to set the current user for testing
    func setDebugUser(id: String, username: String, role: String, name: String = "") {
        self.currentUser = User(id: id, username: username, name: name, role: role)
    }
    
    // Function to fetch and update user profile to get missing fields like school_id
    func fetchUserProfile() async -> Bool {
        await MainActor.run {
            isLoading = true
            errorMessage = nil
        }
        
        print("🔍 UserManager: Fetching user profile for user: \(currentUser.id)")
        
        // Create the API request
        guard let url = URL(string: APIConfig.userProfileEndpoint) else {
            await MainActor.run {
                errorMessage = "Invalid profile URL"
                isLoading = false
            }
            return false
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add authorization header
        if let authHeaders = getAuthorizationHeader() {
            for (key, value) in authHeaders {
                request.addValue(value, forHTTPHeaderField: key)
                print("🔑 UserManager: Added auth header: \(key): \(value.prefix(30))...")
            }
        }
        
        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                await MainActor.run {
                    errorMessage = "Invalid response format"
                    isLoading = false
                }
                return false
            }
            
            print("🌐 UserManager: Profile response status: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode == 200 {
                // Debug: Print the raw response to understand the format
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 UserManager: Raw profile response: \(responseString)")
                }

                // Try to parse as JSON first to see the structure
                do {
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        print("🔍 UserManager: Profile JSON keys: \(json.keys.sorted())")

                        // Try to create a User object with the available data
                        let id = json["_id"] as? String ??
                                json["id"] as? String ??
                                currentUser.id // Fallback to current user ID

                        let username = json["username"] as? String ?? currentUser.username
                        let name = json["name"] as? String ?? currentUser.name
                        let role = json["role"] as? String ?? currentUser.role
                        let email = json["email"] as? String ?? currentUser.email
                        let firstName = json["firstName"] as? String ?? currentUser.firstName
                        let lastName = json["lastName"] as? String ?? currentUser.lastName
                        let credit = json["credit"] as? Int ?? currentUser.credit
                        let school_id = json["school_id"] as? String ?? currentUser.school_id

                        let updatedUser = User(
                            id: id,
                            username: username,
                            name: name,
                            role: role,
                            email: email,
                            firstName: firstName,
                            lastName: lastName,
                            credit: credit,
                            school_id: school_id
                        )

                        await MainActor.run {
                            self.currentUser = updatedUser
                            self.isLoading = false

                            // Save updated user data
                            if let userData = try? JSONEncoder().encode(updatedUser) {
                                UserDefaults.standard.set(userData, forKey: "userData")
                            }
                        }

                        print("✅ UserManager: Successfully updated user profile")
                        print("💰 UserManager: Updated credit: \(credit)")
                        print("🏫 UserManager: School ID: \(school_id ?? "nil")")
                        return true
                    }
                } catch {
                    print("❌ UserManager: Failed to parse profile JSON: \(error)")
                }

                // If manual parsing fails, fall back to the original method
                do {
                    let decoder = JSONDecoder()
                    let updatedUser = try decoder.decode(User.self, from: data)

                    await MainActor.run {
                        self.currentUser = updatedUser
                        self.isLoading = false

                        // Save updated user data
                        if let userData = try? JSONEncoder().encode(updatedUser) {
                            UserDefaults.standard.set(userData, forKey: "userData")
                        }
                    }

                    print("✅ UserManager: Successfully updated user profile via decoder")
                    print("🏫 UserManager: School ID: \(updatedUser.school_id ?? "nil")")
                    return true
                } catch {
                    print("❌ UserManager: Failed to decode User from profile response: \(error)")
                    await MainActor.run {
                        errorMessage = "Failed to parse profile data: \(error.localizedDescription)"
                        isLoading = false
                    }
                    return false
                }
            } else {
                let errorData = String(data: data, encoding: .utf8) ?? "Unknown error"
                await MainActor.run {
                    errorMessage = "Failed to fetch profile: \(errorData)"
                    isLoading = false
                }
                print("❌ UserManager: Profile fetch failed: \(errorData)")
                return false
            }
        } catch {
            await MainActor.run {
                errorMessage = "Network error: \(error.localizedDescription)"
                isLoading = false
            }
            print("❌ UserManager: Profile fetch error: \(error)")
            return false
        }
    }
    
    // MARK: - Debug Methods
    
    // Debug method to test complete authentication flow
    @MainActor
    func debugCompleteAuthFlow() {
        print("🔍 === COMPLETE AUTHENTICATION DEBUG ===")
        print("1. User Session Status:")
        print("   - isLoggedIn: \(isLoggedIn)")
        print("   - User ID: '\(currentUser.id)'")
        print("   - Username: '\(currentUser.username)'")
        print("   - Role: '\(currentUser.role)'")
        print("   - School ID: '\(currentUser.school_id ?? "nil")'")
        
        print("2. Token Status:")
        print("   - accessToken length: \(accessToken.count)")
        print("   - accessToken empty: \(accessToken.isEmpty)")
        print("   - refreshToken length: \(refreshToken.count)")
        
        print("3. UserDefaults Check:")
        if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String] {
            print("   - Saved accessToken length: \(savedTokens["accessToken"]?.count ?? 0)")
            print("   - Saved refreshToken length: \(savedTokens["refreshToken"]?.count ?? 0)")
        } else {
            print("   - ❌ No tokens found in UserDefaults")
        }
        
        print("4. Authorization Header Test:")
        if let authHeaders = getAuthorizationHeader() {
            for (key, value) in authHeaders {
                print("   - \(key): \(value.prefix(50))...")
            }
        } else {
            print("   - ❌ No authorization header available")
        }
        
        print("=== END AUTHENTICATION DEBUG ===")
    }
    
    // Method to validate current session and provide detailed feedback
    @MainActor
    func validateSession() -> String {
        var status = "🔍 SESSION VALIDATION:\n"
        
        status += "1. Login Status: \(isLoggedIn ? "✅ Logged In" : "❌ Not Logged In")\n"
        status += "2. User Info:\n"
        status += "   - ID: '\(currentUser.id)' \(currentUser.id.isEmpty ? "❌ Empty" : "✅ Valid")\n"
        status += "   - Username: '\(currentUser.username)'\n"
        status += "   - Role: '\(currentUser.role)'\n"
        status += "   - School ID: '\(currentUser.school_id ?? "nil")'\n"
        
        status += "3. Token Status:\n"
        status += "   - Access Token: \(accessToken.isEmpty ? "❌ Empty" : "✅ Present (\(accessToken.count) chars)")\n"
        status += "   - Refresh Token: \(refreshToken.isEmpty ? "❌ Empty" : "✅ Present (\(refreshToken.count) chars)")\n"
        
        status += "4. UserDefaults Check:\n"
        if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String] {
            let savedAccessToken = savedTokens["accessToken"] ?? ""
            let savedRefreshToken = savedTokens["refreshToken"] ?? ""
            status += "   - Saved Access Token: \(savedAccessToken.isEmpty ? "❌ Empty" : "✅ Present (\(savedAccessToken.count) chars)")\n"
            status += "   - Saved Refresh Token: \(savedRefreshToken.isEmpty ? "❌ Empty" : "✅ Present (\(savedRefreshToken.count) chars)")\n"
        } else {
            status += "   - ❌ No tokens in UserDefaults\n"
        }
        
        status += "5. Authorization Header: \(getAuthorizationHeader() != nil ? "✅ Available" : "❌ Missing")\n"
        
        return status
    }
    
    // MARK: - Classroom Integration
    
    /// Get the classroom that the current student belongs to (returns first classroom if multiple)
    @MainActor
    func getStudentClassroom() async throws -> Classroom? {
        guard isLoggedIn && currentUser.isStudent else {
            throw NSError(domain: "UserError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Only students can get their classroom"])
        }
        
        do {
            print("📚 UserManager: Fetching student classrooms from: \(APIConfig.studentClassroomEndpoint)")
            print("🔍 UserManager: About to call api.get() with endpoint: '\(APIConfig.studentClassroomEndpoint)'")
            
            // Extract just the path from the full URL since APIService adds baseURL automatically
            let endpointPath = "student/myClassroom"
            print("🔍 UserManager: Using endpoint path: '\(endpointPath)'")
            
            // Use StudentClassroomSummary model for the /api/student/myClassroom endpoint
            let classroomSummaries: [StudentClassroomSummary] = try await api.get(endpointPath)
            print("📚 UserManager: Successfully decoded \(classroomSummaries.count) classroom summaries")
            
            // Convert to Classroom objects for compatibility
            let classrooms = classroomSummaries.map { $0.toClassroom() }
            print("📚 UserManager: Successfully found \(classrooms.count) student classrooms")
            
            if let firstClassroom = classrooms.first {
                print("📚 UserManager: Returning first classroom: \(firstClassroom.name)")
                return firstClassroom
            } else {
                print("📚 UserManager: Student is not assigned to any classroom")
                return nil
            }
            
        } catch {
            print("❌ UserManager: Failed to get student classroom: \(error.localizedDescription)")
            if let apiError = error as? APIError {
                switch apiError {
                case .serverError(let message) where message.contains("404"):
                    // Student is not assigned to any classroom
                    return nil
                default:
                    throw error
                }
            }
            throw error
        }
    }
    
    /// Get all classrooms that a student belongs to (uses the myClassroom endpoint for students)
    @MainActor
    func getStudentClassrooms() async throws -> [Classroom] {
        guard isLoggedIn && currentUser.isStudent else {
            throw NSError(domain: "UserError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Only students can get their classrooms"])
        }
        
        do {
            print("📚 UserManager: Fetching student classrooms from: \(APIConfig.studentClassroomEndpoint)")
            
            // Extract just the path from the full URL since APIService adds baseURL automatically
            let endpointPath = "student/myClassroom"
            print("🔍 UserManager: Using endpoint path: '\(endpointPath)'")
            
            // Use StudentClassroomSummary model for the /api/student/myClassroom endpoint
            let classroomSummaries: [StudentClassroomSummary] = try await api.get(endpointPath)
            print("📚 UserManager: Successfully decoded \(classroomSummaries.count) classroom summaries")
            
            // Convert to Classroom objects for compatibility
            let classrooms = classroomSummaries.map { $0.toClassroom() }
            print("📚 UserManager: Successfully found \(classrooms.count) student classrooms")
            return classrooms
            
        } catch {
            print("❌ UserManager: Failed to get student classrooms: \(error.localizedDescription)")
            throw error
        }
    }
} 

// MARK: - Authenticated API Service Extension

extension UserManager {
    /// Provides easy access to authenticated API calls
    var api: AuthenticatedAPIService {
        return AuthenticatedAPIService()
    }
}

/// Convenience wrapper that ensures user is authenticated before making API calls
struct AuthenticatedAPIService {
    
    /// Makes a GET request with automatic authentication and error handling
    func get<T: Decodable>(_ endpoint: String, responseType: T.Type = T.self) async throws -> T {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.fetch(endpoint: endpoint, responseType: responseType)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
    
    /// Makes a POST request with automatic authentication and error handling
    func post<T: Encodable, U: Decodable>(_ endpoint: String, body: T, responseType: U.Type = U.self) async throws -> U {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.post(endpoint: endpoint, body: body, responseType: responseType)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
    
    /// Makes a PUT request with automatic authentication and error handling
    func put<T: Encodable, U: Decodable>(_ endpoint: String, body: T, responseType: U.Type = U.self) async throws -> U {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.put(endpoint: endpoint, body: body, responseType: responseType)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
    
    /// Makes a DELETE request with automatic authentication and error handling
    func delete<T: Decodable>(_ endpoint: String, responseType: T.Type = T.self) async throws -> T {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.delete(endpoint: endpoint, responseType: responseType)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
    
    /// Makes a POST request without expecting a response body
    func post<T: Encodable>(_ endpoint: String, body: T) async throws {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.post(endpoint: endpoint, body: body)
        
        switch result {
        case .success(_):
            return
        case .failure(let error):
            throw error
        }
    }
    
    /// Makes a PUT request without expecting a response body
    func put<T: Encodable>(_ endpoint: String, body: T) async throws {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.put(endpoint: endpoint, body: body)
        
        switch result {
        case .success(_):
            return
        case .failure(let error):
            throw error
        }
    }
    
    /// Makes a DELETE request without expecting a response body
    func delete(_ endpoint: String) async throws {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.delete(endpoint: endpoint)
        
        switch result {
        case .success(_):
            return
        case .failure(let error):
            throw error
        }
    }
    
    /// Fetches raw data (like files, images) with authentication
    func fetchData(_ endpoint: String) async throws -> Data {
        try UserManager.shared.requireAuthentication()
        
        let result = await APIService.shared.fetchData(endpoint: endpoint)
        
        switch result {
        case .success(let data):
            return data
        case .failure(let error):
            throw error
        }
    }
} 
