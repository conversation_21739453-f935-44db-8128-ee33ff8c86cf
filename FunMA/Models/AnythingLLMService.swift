//
//  AnythingLLMService.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation
import Combine

// Token metrics structure for UI display
struct TokenMetrics {
    let inputTokens: Int
    let outputTokens: Int
    let totalTokens: Int
}

// AnythingLLM API request structure
struct AnythingLLMRequest: Encodable {
    let message: String
    let mode: String // "query" or "chat"
    let sessionId: String
    let attachments: [Attachment]?
    let reset: Bool
    
    struct Attachment: Encodable {
        let name: String
        let mime: String
        let contentString: String
    }
}

// AnythingLLM API response structure
struct AnythingLLMResponse: Decodable {
    let id: String?
    let uuid: String?
    let type: String?
    let close: Bool?
    let error: ErrorField?
    let chatId: Int?
    let textResponse: String?
    let sources: [Source]?
    let metrics: Metrics?
    
    // Computed property to get the actual response text
    var response: String {
        return textResponse ?? ""
    }
    
    // Custom error field that can be either boolean or string
    enum ErrorField: Codable {
        case string(String)
        case boolean(Bool)
        
        init(from decoder: Decoder) throws {
            let container = try decoder.singleValueContainer()
            if let stringValue = try? container.decode(String.self) {
                self = .string(stringValue)
            } else if let boolValue = try? container.decode(Bool.self) {
                self = .boolean(boolValue)
            } else {
                throw DecodingError.typeMismatch(ErrorField.self, DecodingError.Context(codingPath: decoder.codingPath, debugDescription: "Expected String or Bool"))
            }
        }
        
        func encode(to encoder: Encoder) throws {
            var container = encoder.singleValueContainer()
            switch self {
            case .string(let value):
                try container.encode(value)
            case .boolean(let value):
                try container.encode(value)
            }
        }
        
        var isError: Bool {
            switch self {
            case .string(let value):
                return !value.isEmpty
            case .boolean(let value):
                return value
            }
        }
        
        var errorMessage: String? {
            switch self {
            case .string(let value):
                return value.isEmpty ? nil : value
            case .boolean:
                return nil
            }
        }
    }
    
    struct Metrics: Decodable {
        let completion_tokens: Int?
        let prompt_tokens: Int?
        let total_tokens: Int?
        let outputTps: Double?
        let duration: Double?
    }
    
    struct Source: Decodable {
        let id: String?
        let url: String?
        let title: String?
        let docAuthor: String?
        let description: String?
        let docSource: String?
        let chunkSource: String?
        let published: String?
        let wordCount: Int?
        let token_count_estimate: Int?
        let text: String?
        let _distance: Double?
        let score: Double?
    }
}

class AnythingLLMService: NSObject, ObservableObject, URLSessionDataDelegate {
    private let apiURL = URL(string: APIConfig.llmHintEndpoint)!
    private let creditDeductionURL = URL(string: APIConfig.creditDeductionEndpoint)!
    private var sessionId: String = UUID().uuidString
    
    @Published var isLoading = false
    @Published var error: String? = nil
    
    // Stream response publisher
    private var streamSubject = PassthroughSubject<String, Error>()
    
    // Token metrics publisher
    private var tokenMetricsSubject = PassthroughSubject<TokenMetrics, Never>()
    
    // Store accumulated data for streaming
    private var accumulatedData = Data()
    private var currentTask: URLSessionDataTask?
    private var currentMessages: [ChatMessage] = []
    private var currentAttachments: [FileAttachment] = []
    
    // Credit deduction settings
    private let tokensPerCredit: Int = 1000 // 1 credit = 1000 tokens (adjust as needed)
    
    override init() {
        super.init()
    }
    
    // Set session ID for chat continuity
    func setSessionId(_ id: String) {
        self.sessionId = id
    }
    
    // Reset session (starts a new conversation)
    func resetSession() {
        self.sessionId = UUID().uuidString
    }
    
    // Get token metrics publisher
    func getTokenMetricsPublisher() -> AnyPublisher<TokenMetrics, Never> {
        return tokenMetricsSubject.eraseToAnyPublisher()
    }
    
    // Send a message to the AnythingLLM API and get a streaming response
    func sendStreamingMessage(messages: [ChatMessage], attachments: [FileAttachment] = []) -> AnyPublisher<String, Error> {
        // Get authorization header from UserManager
        guard let authHeader = UserManager.shared.getAuthorizationHeader() else {
            print("DEBUG: ❌ No authorization header available - user not authenticated")
            return Fail(error: NSError(domain: "AnythingLLMService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not authenticated"]))
                .eraseToAnyPublisher()
        }
        
        // Debug: Print the authorization header
        print("DEBUG: 🔑 Authorization header: \(authHeader)")
        
        // Get the last user message (the current query)
        guard let lastUserMessage = messages.last else {
            print("DEBUG: ❌ No user message found")
            return Fail(error: NSError(domain: "AnythingLLMService", code: 400, userInfo: [NSLocalizedDescriptionKey: "No user message found"]))
                .eraseToAnyPublisher()
        }
        
        print("DEBUG: 📝 Sending message: \(lastUserMessage.content)")
        print("DEBUG: 📎 Attachments: \(attachments.count) files")
        
        // Store messages for retry
        self.currentMessages = messages
        self.currentAttachments = attachments
        
        // Convert all attachments to AnythingLLM format (including images)
        let anythingLLMAttachments = attachments.map { file in
            AnythingLLMRequest.Attachment(
                name: file.name,
                mime: file.mimeType,
                contentString: file.contentString
            )
        }
        
        // Create the request with the current user message and attachments
        let request = AnythingLLMRequest(
            message: lastUserMessage.content,
            mode: "chat", // Use chat mode for conversation continuity
            sessionId: sessionId,
            attachments: anythingLLMAttachments.isEmpty ? nil : anythingLLMAttachments,
            reset: false
        )
        
        // Create and configure the request
        var urlRequest = URLRequest(url: apiURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("text/event-stream", forHTTPHeaderField: "Accept")
        
        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
            print("DEBUG: 🔐 Setting header \(key): \(value)")
        }
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
            print("DEBUG: 📤 Request body size: \(urlRequest.httpBody?.count ?? 0) bytes")
            
            // Log attachment info
            if let attachments = request.attachments {
                for attachment in attachments {
                    print("DEBUG: 📎 Attachment: \(attachment.name) (\(attachment.mime)) - \(attachment.contentString.count) chars")
                    // Log the first 100 characters of the contentString to verify format
                    let preview = String(attachment.contentString.prefix(100))
                    print("DEBUG: 📎 Content preview: \(preview)...")
                }
            }
            
            // Log the full request JSON for debugging
            if let requestData = try? JSONEncoder().encode(request),
               let requestString = String(data: requestData, encoding: .utf8) {
                print("DEBUG: 📤 Full request JSON: \(requestString)")
            }
        } catch {
            print("DEBUG: ❌ Failed to encode request: \(error)")
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        print("DEBUG: 🌐 Making streaming request to: \(apiURL)")
        
        self.isLoading = true
        self.streamSubject = PassthroughSubject<String, Error>()
        self.accumulatedData = Data()
        
        // Create a custom session with delegate for streaming
        let session = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
        self.currentTask = session.dataTask(with: urlRequest)
        self.currentTask?.resume()
        
        return self.streamSubject
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    // MARK: - URLSessionDataDelegate
    
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
        // Check for HTTP errors
        if let httpResponse = response as? HTTPURLResponse {
            print("DEBUG: 📡 HTTP Status: \(httpResponse.statusCode)")
            if httpResponse.statusCode == 401 {
                print("DEBUG: 🔄 Token expired, attempting to refresh...")
                
                // Try to refresh the token
                Task {
                    let refreshSuccess = await UserManager.shared.refreshToken()
                    
                    if refreshSuccess {
                        print("DEBUG: ✅ Token refreshed successfully, retrying request...")
                        // Retry the request with the new token
                        await self.retryStreamingRequest()
                    } else {
                        print("DEBUG: ❌ Token refresh failed")
                        DispatchQueue.main.async {
                            self.error = "Authentication failed. Please login again."
                            self.isLoading = false
                            self.streamSubject.send(completion: .failure(NSError(domain: "AnythingLLMService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Authentication failed"])))
                        }
                    }
                }
                completionHandler(.cancel)
                return
            } else if httpResponse.statusCode >= 400 {
                let error = NSError(domain: "AnythingLLMService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server error: \(httpResponse.statusCode)"])
                print("DEBUG: ❌ HTTP Error: \(httpResponse.statusCode)")
                
                // Try to read the response body for more details
                if !self.accumulatedData.isEmpty,
                   let responseString = String(data: self.accumulatedData, encoding: .utf8) {
                    print("DEBUG: ❌ Error response body: \(responseString)")
                }
                
                DispatchQueue.main.async {
                    self.error = error.localizedDescription
                    self.isLoading = false
                    self.streamSubject.send(completion: .failure(error))
                }
                completionHandler(.cancel)
                return
            }
        }
        
        completionHandler(.allow)
    }
    
    func urlSession(_ session: URLSession, dataTask: URLSessionDataTask, didReceive data: Data) {
        // Append new data to accumulated data
        self.accumulatedData.append(data)
        
        // Debug: Print raw response data
        if let responseString = String(data: data, encoding: .utf8) {
            print("DEBUG: 📥 Raw streaming chunk: \(responseString)")
        }
        
        // Process the streaming response
        self.processStreamingResponseChunk(data)
    }
    
    func urlSession(_ session: URLSession, task: URLSessionTask, didCompleteWithError error: Error?) {
        if let error = error {
            print("DEBUG: ❌ Session error: \(error)")
            DispatchQueue.main.async {
                self.error = error.localizedDescription
                self.isLoading = false
                self.streamSubject.send(completion: .failure(error))
            }
        } else {
            print("DEBUG: ✅ Session completed successfully")
            DispatchQueue.main.async {
                self.isLoading = false
                self.streamSubject.send(completion: .finished)
            }
        }
    }
    
    // Process streaming response chunk from AnythingLLM
    private func processStreamingResponseChunk(_ data: Data) {
        guard let responseString = String(data: data, encoding: .utf8) else {
            print("DEBUG: ❌ Failed to decode response chunk")
            return
        }
        
        // Split the response into lines to handle SSE format
        let lines = responseString.components(separatedBy: .newlines)
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // Skip empty lines
            guard !trimmedLine.isEmpty else { continue }
            
            // Check if this is a data line (SSE format: "data: {...}")
            if trimmedLine.hasPrefix("data: ") {
                let jsonString = String(trimmedLine.dropFirst(6)) // Remove "data: " prefix
                
                // Handle special SSE messages
                if jsonString == "[DONE]" {
                    print("DEBUG: ✅ Streaming completed")
                    DispatchQueue.main.async {
                        self.isLoading = false
                        self.streamSubject.send(completion: .finished)
                    }
                    return
                }
                
                // Try to parse the JSON response
                do {
                    let jsonData = jsonString.data(using: .utf8)!
                    let response = try JSONDecoder().decode(AnythingLLMResponse.self, from: jsonData)
                    
                    // Check for errors using the new ErrorField structure
                    if let errorField = response.error, errorField.isError {
                        let errorMessage = errorField.errorMessage ?? "Unknown error"
                        print("DEBUG: ❌ API Error in stream: \(errorMessage)")
                        DispatchQueue.main.async {
                            self.error = errorMessage
                            self.isLoading = false
                            self.streamSubject.send(completion: .failure(NSError(domain: "AnythingLLMService", code: 0, userInfo: [NSLocalizedDescriptionKey: errorMessage])))
                        }
                        return
                    }
                    
                    // Send the response text chunk immediately
                    if let textResponse = response.textResponse, !textResponse.isEmpty {
                        DispatchQueue.main.async {
                            self.streamSubject.send(textResponse)
                        }
                    }
                    
                    // Check for metrics in intermediate responses too
                    if let metrics = response.metrics {
                        print("DEBUG: 📊 Found metrics in intermediate response - Input: \(metrics.prompt_tokens ?? 0), Output: \(metrics.completion_tokens ?? 0), Total: \(metrics.total_tokens ?? 0)")
                        let tokenMetrics = TokenMetrics(
                            inputTokens: metrics.prompt_tokens ?? 0,
                            outputTokens: metrics.completion_tokens ?? 0,
                            totalTokens: metrics.total_tokens ?? 0
                        )
                        
                        // Publish token metrics from intermediate response
                        DispatchQueue.main.async {
                            print("DEBUG: 📊 Publishing intermediate token metrics to UI")
                            self.tokenMetricsSubject.send(tokenMetrics)
                        }
                    }
                    
                    // Check if this is the final message
                    if response.close == true {
                        print("DEBUG: ✅ Streaming completed (close: true)")
                        if let metrics = response.metrics {
                            let tokenMetrics = TokenMetrics(
                                inputTokens: metrics.prompt_tokens ?? 0,
                                outputTokens: metrics.completion_tokens ?? 0,
                                totalTokens: metrics.total_tokens ?? 0
                            )
                            print("DEBUG: 📊 Token usage - Input: \(tokenMetrics.inputTokens), Output: \(tokenMetrics.outputTokens), Total: \(tokenMetrics.totalTokens)")
                            
                            // Publish token metrics
                            DispatchQueue.main.async {
                                print("DEBUG: 📊 Publishing token metrics to UI")
                                self.tokenMetricsSubject.send(tokenMetrics)
                            }
                            
                            // Deduct credits asynchronously for streaming response
                            Task {
                                await self.deductCreditsForTokens(metrics.total_tokens ?? 0)
                            }
                        } else {
                            print("DEBUG: ⚠️ No metrics found in final response")
                        }
                        DispatchQueue.main.async {
                            self.isLoading = false
                            self.streamSubject.send(completion: .finished)
                        }
                        return
                    }
                    
                } catch {
                    print("DEBUG: ⚠️ Failed to parse JSON chunk: \(error)")
                    print("DEBUG: 📄 Raw JSON: \(jsonString)")
                    // Continue processing other chunks instead of failing completely
                }
            }
        }
    }
    
    // Helper method to retry the streaming request after token refresh
    private func retryStreamingRequest() async {
        // Get the new authorization header
        guard let authHeader = UserManager.shared.getAuthorizationHeader() else {
            print("DEBUG: ❌ Still no authorization header after refresh")
            DispatchQueue.main.async {
                self.error = "Authentication failed after token refresh"
                self.isLoading = false
                self.streamSubject.send(completion: .failure(NSError(domain: "AnythingLLMService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Authentication failed"])))
            }
            return
        }
        
        print("DEBUG: 🔑 New authorization header after refresh: \(authHeader)")
        
        // Get the last user message
        guard let lastUserMessage = self.currentMessages.last else {
            print("DEBUG: ❌ No user message found for retry")
            return
        }
        
        // Create the request again
        let request = AnythingLLMRequest(
            message: lastUserMessage.content,
            mode: "chat",
            sessionId: sessionId,
            attachments: nil,
            reset: false
        )
        
        // Create and configure the retry request
        var urlRequest = URLRequest(url: apiURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("text/event-stream", forHTTPHeaderField: "Accept")
        
        // Add the new authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
            print("DEBUG: 🔐 Setting new header \(key): \(value)")
        }
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            print("DEBUG: ❌ Failed to encode retry request: \(error)")
            DispatchQueue.main.async {
                self.error = "Failed to retry request after token refresh"
                self.isLoading = false
                self.streamSubject.send(completion: .failure(error))
            }
            return
        }
        
        print("DEBUG: 🔄 Retrying streaming request to: \(apiURL)")
        
        // Reset for retry
        self.accumulatedData = Data()
        
        // Create a new session and task for retry
        let session = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
        self.currentTask = session.dataTask(with: urlRequest)
        self.currentTask?.resume()
    }
    
    // Process the response from AnythingLLM
    private func processResponse(_ data: Data) {
        do {
            let response = try JSONDecoder().decode(AnythingLLMResponse.self, from: data)
            
            if let error = response.error {
                DispatchQueue.main.async {
                    self.error = error.errorMessage
                    self.streamSubject.send(completion: .failure(NSError(domain: "AnythingLLMService", code: 0, userInfo: [NSLocalizedDescriptionKey: error.errorMessage ?? "Unknown error"])))
                }
                return
            }
            
            // Send the response content as a stream (simulating streaming by sending the full response)
            DispatchQueue.main.async {
                self.streamSubject.send(response.response)
            }
            
        } catch {
            DispatchQueue.main.async {
                self.error = "Error decoding response: \(error.localizedDescription)"
                self.streamSubject.send(completion: .failure(error))
            }
        }
    }
    
    // Send a message to the AnythingLLM API and get a complete response (non-streaming)
    func sendMessage(messages: [ChatMessage], attachments: [FileAttachment] = []) -> AnyPublisher<ChatMessage, Error> {
        return Future { promise in
            Task {
                do {
                    let result = try await self.sendMessageAsync(messages: messages, attachments: attachments)
                    promise(.success(result))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
    }
    
    // Async version of sendMessage that handles token refresh
    private func sendMessageAsync(messages: [ChatMessage], attachments: [FileAttachment] = []) async throws -> ChatMessage {
        // Get authorization header from UserManager
        guard let authHeader = UserManager.shared.getAuthorizationHeader() else {
            print("DEBUG: ❌ No authorization header available - user not authenticated")
            throw NSError(domain: "AnythingLLMService", code: 401, userInfo: [NSLocalizedDescriptionKey: "User not authenticated"])
        }
        
        // Debug: Print the authorization header
        print("DEBUG: 🔑 Authorization header: \(authHeader)")
        
        // Get the last user message (the current query)
        guard let lastUserMessage = messages.last else {
            print("DEBUG: ❌ No user message found")
            throw NSError(domain: "AnythingLLMService", code: 400, userInfo: [NSLocalizedDescriptionKey: "No user message found"])
        }
        
        print("DEBUG: 📝 Sending message: \(lastUserMessage.content)")
        print("DEBUG: 📎 Attachments: \(attachments.count) files")
        
        // Store messages for retry
        self.currentMessages = messages
        self.currentAttachments = attachments
        
        // Convert all attachments to AnythingLLM format (including images)
        let anythingLLMAttachments = attachments.map { file in
            AnythingLLMRequest.Attachment(
                name: file.name,
                mime: file.mimeType,
                contentString: file.contentString
            )
        }
        
        // Create the request with the current user message and attachments
        let request = AnythingLLMRequest(
            message: lastUserMessage.content,
            mode: "chat",
            sessionId: sessionId,
            attachments: anythingLLMAttachments.isEmpty ? nil : anythingLLMAttachments,
            reset: false
        )
        
        // Create and configure the request
        var urlRequest = URLRequest(url: apiURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
            print("DEBUG: 🔐 Setting header \(key): \(value)")
        }
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
            print("DEBUG: 📤 Request body size: \(urlRequest.httpBody?.count ?? 0) bytes")
            
            // Log attachment info
            if let attachments = request.attachments {
                for attachment in attachments {
                    print("DEBUG: 📎 Attachment: \(attachment.name) (\(attachment.mime)) - \(attachment.contentString.count) chars")
                    // Log the first 100 characters of the contentString to verify format
                    let preview = String(attachment.contentString.prefix(100))
                    print("DEBUG: 📎 Content preview: \(preview)...")
                }
            }
            
            // Log the full request JSON for debugging
            if let requestData = try? JSONEncoder().encode(request),
               let requestString = String(data: requestData, encoding: .utf8) {
                print("DEBUG: 📤 Full request JSON: \(requestString)")
            }
        } catch {
            print("DEBUG: ❌ Failed to encode request: \(error)")
            throw error
        }
        
        print("DEBUG: 🌐 Making request to: \(apiURL)")
        
        self.isLoading = true
        
        do {
            // Make the request
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            // Check for 401 error and handle token refresh
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 401 {
                print("DEBUG: 🔄 Token expired in non-streaming request, attempting to refresh...")
                
                let refreshSuccess = await UserManager.shared.refreshToken()
                
                if refreshSuccess {
                    print("DEBUG: ✅ Token refreshed successfully, retrying non-streaming request...")
                    // Retry the request with the new token
                    let retryData = try await self.retryNonStreamingRequest(messages: messages, attachments: attachments)
                    return try self.processNonStreamingResponse(retryData)
                } else {
                    print("DEBUG: ❌ Token refresh failed in non-streaming request")
                    throw NSError(domain: "AnythingLLMService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Authentication failed"])
                }
            }
            
            // Check for other HTTP errors
            if let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode >= 400 {
                let error = NSError(domain: "AnythingLLMService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server error: \(httpResponse.statusCode)"])
                print("DEBUG: ❌ HTTP Error: \(httpResponse.statusCode)")
                throw error
            }
            
            // Debug: Print response data
            if let responseString = String(data: data, encoding: .utf8) {
                print("DEBUG: 📥 Response data: \(responseString)")
            }
            
            self.isLoading = false
            return try self.processNonStreamingResponse(data)
            
        } catch {
            self.isLoading = false
            print("DEBUG: ❌ Request failed: \(error)")
            self.error = error.localizedDescription
            throw error
        }
    }
    
    // Helper method to process non-streaming response
    private func processNonStreamingResponse(_ data: Data) throws -> ChatMessage {
        let response = try JSONDecoder().decode(AnythingLLMResponse.self, from: data)
        
        if let error = response.error {
            print("DEBUG: ❌ API Error: \(error)")
            return ChatMessage(content: "Error: \(error.errorMessage ?? "Unknown error")", isUser: false)
        } else {
            print("DEBUG: ✅ API Response: \(response.response)")
            
            // Deduct credits for non-streaming response if we have metrics
            if let metrics = response.metrics {
                print("DEBUG: 📊 Non-streaming metrics - Tokens: \(metrics.total_tokens ?? 0), Duration: \(metrics.duration ?? 0)s")
                Task {
                    await self.deductCreditsForTokens(metrics.total_tokens ?? 0)
                }
            }
            
            return ChatMessage(content: response.response, isUser: false)
        }
    }
    
    // Helper method to retry the non-streaming request after token refresh
    private func retryNonStreamingRequest(messages: [ChatMessage], attachments: [FileAttachment] = []) async throws -> Data {
        // Get the new authorization header
        guard let authHeader = UserManager.shared.getAuthorizationHeader() else {
            print("DEBUG: ❌ Still no authorization header after refresh")
            throw NSError(domain: "AnythingLLMService", code: 401, userInfo: [NSLocalizedDescriptionKey: "Authentication failed"])
        }
        
        print("DEBUG: 🔑 New authorization header after refresh: \(authHeader)")
        
        // Get the last user message
        guard let lastUserMessage = messages.last else {
            print("DEBUG: ❌ No user message found for retry")
            throw NSError(domain: "AnythingLLMService", code: 400, userInfo: [NSLocalizedDescriptionKey: "No user message found"])
        }
        
        // Convert all attachments to AnythingLLM format (including images)
        let anythingLLMAttachments = attachments.map { file in
            AnythingLLMRequest.Attachment(
                name: file.name,
                mime: file.mimeType,
                contentString: file.contentString
            )
        }
        
        // Create the request again
        let request = AnythingLLMRequest(
            message: lastUserMessage.content,
            mode: "chat",
            sessionId: sessionId,
            attachments: anythingLLMAttachments.isEmpty ? nil : anythingLLMAttachments,
            reset: false
        )
        
        // Create and configure the retry request
        var urlRequest = URLRequest(url: apiURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add the new authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
            print("DEBUG: 🔐 Setting new header \(key): \(value)")
        }
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            print("DEBUG: ❌ Failed to encode retry request: \(error)")
            throw error
        }
        // Debug: Print request details
        print("DEBUG: 📤 Request URL: \(urlRequest.url?.absoluteString ?? "nil")")
        print("DEBUG: 📤 Request Method: \(urlRequest.httpMethod ?? "nil")")
        print("DEBUG: 📤 Request Headers: \(urlRequest.allHTTPHeaderFields ?? [:])")
        if let bodyData = urlRequest.httpBody, let bodyString = String(data: bodyData, encoding: .utf8) {
            print("DEBUG: 📤 Request Body: \(bodyString)")
        }
        print("DEBUG: 🔄 Retrying non-streaming request to: \(apiURL)")
        
        // Make the retry request
        let (data, response) = try await URLSession.shared.data(for: urlRequest)
        
        // Check for HTTP errors
        if let httpResponse = response as? HTTPURLResponse {
            print("DEBUG: 📡 Retry HTTP Status: \(httpResponse.statusCode)")
            if httpResponse.statusCode >= 400 {
                throw NSError(domain: "AnythingLLMService", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Retry failed with status: \(httpResponse.statusCode)"])
            }
        }
        
        // Debug: Print retry response data
        if let responseString = String(data: data, encoding: .utf8) {
            print("DEBUG: 📥 Retry response data: \(responseString)")
        }
        
        return data
    }
    
    // MARK: - Credit Deduction
    
    /// Deduct credits based on token usage
    /// - Parameters:
    ///   - tokens: Number of tokens used
    ///   - description: Optional description for the deduction
    private func deductCreditsForTokens(_ tokens: Int, description: String? = nil) async {
        guard tokens > 0 else {
            print("DEBUG: 💰 No tokens to deduct credits for")
            return
        }
        
        // Calculate credits to deduct (round up to ensure we always deduct at least 1 credit for any usage)
        let creditsToDeduct = max(1, Int(ceil(Double(tokens) / Double(tokensPerCredit))))
        
        print("DEBUG: 💰 Deducting \(creditsToDeduct) credits for \(tokens) tokens")
        
        await deductCredits(amount: creditsToDeduct, description: description ?? "AI Assistant usage (\(tokens) tokens)")
    }
    
    /// Deduct credits from user's account
    /// - Parameters:
    ///   - amount: Number of credits to deduct
    ///   - description: Optional description for the deduction
    private func deductCredits(amount: Int, description: String? = nil) async {
        guard let authHeader = UserManager.shared.getAuthorizationHeader() else {
            print("DEBUG: 💰 ❌ No authorization header available for credit deduction")
            return
        }
        
        let request = CreditDeductionRequest(
            amount: amount,
            description: description
        )
        
        var urlRequest = URLRequest(url: creditDeductionURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
            print("DEBUG: 💰 Making credit deduction request: \(amount) credits")
            
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            // Check for HTTP errors
            if let httpResponse = response as? HTTPURLResponse {
                print("DEBUG: 💰 Credit deduction HTTP Status: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 401 {
                    print("DEBUG: 💰 Credit deduction failed - authentication error")
                    return
                } else if httpResponse.statusCode >= 400 {
                    print("DEBUG: 💰 Credit deduction failed - server error: \(httpResponse.statusCode)")
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("DEBUG: 💰 Error response: \(responseString)")
                    }
                    return
                }
            }
            
            // Parse the response
            let deductionResponse = try JSONDecoder().decode(CreditDeductionResponse.self, from: data)
            
            if deductionResponse.success {
                print("DEBUG: 💰 ✅ Successfully deducted \(amount) credits. Remaining balance: \(deductionResponse.newBalance ?? 0)")
                if let message = deductionResponse.message {
                    print("DEBUG: 💰 Server message: \(message)")
                }

                // Update the user's credit in UserManager if we have the new balance
                if let newBalance = deductionResponse.newBalance {
                    DispatchQueue.main.async {
                        // Use the new convenience method to update just the credit
                        UserManager.shared.updateUserCredit(newBalance)

                        print("DEBUG: 💰 Updated user credit in UserManager: \(newBalance)")

                        // Post notification that credit was updated
                        NotificationCenter.default.post(
                            name: NSNotification.Name("UserCreditUpdated"),
                            object: nil,
                            userInfo: ["newCredit": newBalance]
                        )
                    }
                }
            } else {
                print("DEBUG: 💰 ❌ Credit deduction failed: \(deductionResponse.message ?? "Unknown error")")

                // Update the error on main thread for UI
                DispatchQueue.main.async {
                    self.error = deductionResponse.message ?? "Credit deduction failed"
                }
            }
            
        } catch {
            print("DEBUG: 💰 ❌ Credit deduction request failed: \(error)")
        }
    }
} 
