import Foundation

// Simple configuration for API endpoints
struct APIConfig {
    // Base URL for the API
    #if DEBUG
    //    static let baseURL = "http://ec2-52-76-76-177.ap-southeast-1.compute.amazonaws.com:5001/api"
        static let baseURL = "http://localhost:5001/api"
    #else
        static let baseURL = "http://ec2-52-76-76-177.ap-southeast-1.compute.amazonaws.com:5001/api"
    #endif
    
    // WebSocket server configuration
    #if DEBUG
        static let webSocketGameEndpoint = "ws://ec2-52-76-76-177.ap-southeast-1.compute.amazonaws.com:8765"
    #else
        static let webSocketGameEndpoint = "ws://ec2-52-76-76-177.ap-southeast-1.compute.amazonaws.com:8765"
    #endif
    
    // Authentication endpoints
    static let loginEndpoint = "\(baseURL)/auth/login"
    static let registerEndpoint = "\(baseURL)/auth/register"
    static let refreshTokenEndpoint = "\(baseURL)/auth/refresh"
    static let userInfoEndpoint = "\(baseURL)/auth/me"
    
    // User profile endpoint
    static let userProfileEndpoint = "\(baseURL)/user/profile"
    
    // Course endpoints
    static let courseEndpoint = "\(baseURL)/course"
    static let userCourseEndpoint = "\(baseURL)/userCourse"
    static let userProgressEndpoint = "\(baseURL)/userProgress"
    
    // *** UPDATED EXERCISE ENDPOINTS FOR NEW BACKEND ***
    // Teacher/Developer exercise management endpoints
    static let exerciseEndpoint = "\(baseURL)/exercise"  // GET, POST, PUT, DELETE
    
    // Student exercise endpoints
    static let studentExercisesEndpoint = "\(baseURL)/student/exercises"  // GET student exercises
    
    // Submission endpoints
    static let submissionEndpoint = "\(baseURL)/submission"  // GET, POST (corrected to match backend)
    
    // Game endpoints
    static let gameEndpoint = "\(baseURL)/game"  // GET with optional query params: ?grade=form1 or ?topic=...&grade=...
    
    // Classroom endpoints
    static let classroomEndpoint = "\(baseURL)/classroom"  // GET, POST, PUT, DELETE
    static let classroomStudentsEndpoint = "\(baseURL)/classroom"  // POST /classroom/{id}/students, DELETE /classroom/{id}/students/{studentId}
    static let classroomStatsEndpoint = "\(baseURL)/classroom"  // GET /classroom/{id}/stats

    // School endpoints
    static let schoolEndpoint = "\(baseURL)/school"  // GET, POST
    
    // Student classroom endpoints
    static let studentClassroomEndpoint = "\(baseURL)/student/myClassroom"  // GET student's own classrooms
    static let studentClassroomsEndpoint = "\(baseURL)/student/classrooms"  // GET student's classrooms (with student_id param)

    
    // Credit endpoints
    static let creditDeductionEndpoint = "\(baseURL)/credit/deduct"
    static let adminCreditAdjustEndpoint = "\(baseURL)/admin/credit/user/adjust"

    // User search endpoints
    static let userSearchEndpoint = "\(baseURL)/admin/search-user"
    
    // AnythingLLM endpoints
    static let llmHintEndpoint = "\(baseURL)/llm/hint"
    static let llmGenerateMCQuestionsEndpoint = "\(baseURL)/llm/generate-mc-questions"
    static let llmGenerateLongQuestionsEndpoint = "\(baseURL)/llm/generate-long-questions"
    
    // Legacy endpoints (keeping for backward compatibility during migration)
    static let exercisesEndpoint = "\(baseURL)/exercises"  // Old endpoint
    
} 
