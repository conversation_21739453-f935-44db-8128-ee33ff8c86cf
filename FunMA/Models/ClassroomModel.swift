import Foundation
import SwiftUI

// MARK: - MongoDB Date Model
struct MongoDate: Codable {
    let date: Date
    
    enum CodingKeys: String, CodingKey {
        case date = "$date"
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let dateString = try container.decode(String.self, forKey: .date)
        
        // Parse MongoDB date string
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        
        if let date = formatter.date(from: dateString) {
            self.date = date
        } else {
            throw DecodingError.dataCorrupted(
                DecodingError.Context(
                    codingPath: decoder.codingPath,
                    debugDescription: "Unable to parse MongoDB date: \(dateString)"
                )
            )
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        try container.encode(formatter.string(from: date), forKey: .date)
    }
}

// MARK: - Classroom Model
struct Classroom: Identifiable, Codable {
    let id: String // MongoDB _id
    let name: String // e.g., "1A", "2B", etc.
    let grade: Grade
    let teacherId: String // Teacher who manages this classroom
    let teacherName: String
    var students: [ClassroomStudent] // Students in this classroom
    let createdAt: Date
    let updatedAt: Date
    let subject: String? // Optional: Math, Science, etc.
    let schoolYear: String // e.g., "2024-2025"
    
    // MongoDB ObjectId mapping
    var mongoId: String? { return id }
    
    init(
        id: String = UUID().uuidString,
        name: String,
        grade: Grade,
        teacherId: String,
        teacherName: String,
        students: [ClassroomStudent] = [],
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        subject: String? = nil,
        schoolYear: String
    ) {
        self.id = id
        self.name = name
        self.grade = grade
        self.teacherId = teacherId
        self.teacherName = teacherName
        self.students = students
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.subject = subject
        self.schoolYear = schoolYear
    }
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case name
        case grade
        case teacherId
        case teacherName
        case students
        case createdAt
        case updatedAt
        case subject
        case schoolYear
    }
    
    // Custom decoder to handle MongoDB date formats
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.id = try container.decode(String.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.grade = try container.decode(Grade.self, forKey: .grade)
        self.teacherId = try container.decode(String.self, forKey: .teacherId)
        self.teacherName = try container.decode(String.self, forKey: .teacherName)
        self.students = try container.decode([ClassroomStudent].self, forKey: .students)
        self.subject = try container.decodeIfPresent(String.self, forKey: .subject)
        self.schoolYear = try container.decode(String.self, forKey: .schoolYear)
        
        // Handle MongoDB date parsing - can be either string or MongoDB date object
        if let mongoDate = try? container.decode(MongoDate.self, forKey: .createdAt) {
            self.createdAt = mongoDate.date
        } else {
            let createdAtString = try container.decode(String.self, forKey: .createdAt)
            self.createdAt = try Classroom.parseDate(createdAtString)
        }
        
        if let mongoDate = try? container.decode(MongoDate.self, forKey: .updatedAt) {
            self.updatedAt = mongoDate.date
        } else {
            let updatedAtString = try container.decode(String.self, forKey: .updatedAt)
            self.updatedAt = try Classroom.parseDate(updatedAtString)
        }
    }
    
    // Custom encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(grade, forKey: .grade)
        try container.encode(teacherId, forKey: .teacherId)
        try container.encode(teacherName, forKey: .teacherName)
        try container.encode(students, forKey: .students)
        try container.encodeIfPresent(subject, forKey: .subject)
        try container.encode(schoolYear, forKey: .schoolYear)
        
        // Encode dates as ISO8601 strings
        let isoFormatter = ISO8601DateFormatter()
        try container.encode(isoFormatter.string(from: createdAt), forKey: .createdAt)
        try container.encode(isoFormatter.string(from: updatedAt), forKey: .updatedAt)
    }
    
    // Helper method to parse date strings
    static func parseDate(_ dateString: String) throws -> Date {
        let formatters: [DateFormatter] = [
            {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSSSSXXXXX"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }(),
            {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss'Z'"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }(),
            {
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssXXXXX"
                formatter.timeZone = TimeZone(abbreviation: "UTC")
                return formatter
            }()
        ]
        
        for formatter in formatters {
            if let date = formatter.date(from: dateString) {
                return date
            }
        }
        
        // Fallback: Try ISO8601DateFormatter
        let iso8601Formatter = ISO8601DateFormatter()
        if let date = iso8601Formatter.date(from: dateString) {
            return date
        }
        
        throw DecodingError.dataCorrupted(
            DecodingError.Context(
                codingPath: [],
                debugDescription: "Unable to parse date: \(dateString)"
            )
        )
    }
}

// MARK: - Student Classroom Summary Model
// Used for /api/student/myClassroom endpoint which excludes students array for performance
struct StudentClassroomSummary: Identifiable, Codable {
    let id: String // MongoDB _id
    let name: String // e.g., "1A", "2B", etc.
    let grade: Grade
    let teacherId: String // Teacher who manages this classroom
    let teacherName: String
    let subject: String? // Optional: Math, Science, etc.
    let schoolYear: String // e.g., "2024-2025"
    let joinedAt: Date // When the student joined this classroom
    let status: String // Student's status in this classroom
    let exerciseCount: Int? // Total exercises assigned to this classroom
    let completedExercises: Int? // Exercises completed by the student
    let pendingExercises: Int? // Exercises pending for the student
    
    // MongoDB ObjectId mapping
    var mongoId: String? { return id }
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case name
        case grade
        case teacherId
        case teacherName
        case subject
        case schoolYear
        case joinedAt
        case status
        case exerciseCount
        case completedExercises
        case pendingExercises
    }
    
    // Custom decoder to handle MongoDB date formats
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.id = try container.decode(String.self, forKey: .id)
        self.name = try container.decode(String.self, forKey: .name)
        self.grade = try container.decode(Grade.self, forKey: .grade)
        self.teacherId = try container.decode(String.self, forKey: .teacherId)
        self.teacherName = try container.decode(String.self, forKey: .teacherName)
        self.subject = try container.decodeIfPresent(String.self, forKey: .subject)
        self.schoolYear = try container.decode(String.self, forKey: .schoolYear)
        self.status = try container.decode(String.self, forKey: .status)
        self.exerciseCount = try container.decodeIfPresent(Int.self, forKey: .exerciseCount)
        self.completedExercises = try container.decodeIfPresent(Int.self, forKey: .completedExercises)
        self.pendingExercises = try container.decodeIfPresent(Int.self, forKey: .pendingExercises)
        
        // Handle MongoDB date parsing for joinedAt
        if let mongoDate = try? container.decode(MongoDate.self, forKey: .joinedAt) {
            self.joinedAt = mongoDate.date
        } else {
            let joinedAtString = try container.decode(String.self, forKey: .joinedAt)
            self.joinedAt = try Classroom.parseDate(joinedAtString)
        }
    }
    
    // Custom encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(grade, forKey: .grade)
        try container.encode(teacherId, forKey: .teacherId)
        try container.encode(teacherName, forKey: .teacherName)
        try container.encodeIfPresent(subject, forKey: .subject)
        try container.encode(schoolYear, forKey: .schoolYear)
        try container.encode(status, forKey: .status)
        try container.encodeIfPresent(exerciseCount, forKey: .exerciseCount)
        try container.encodeIfPresent(completedExercises, forKey: .completedExercises)
        try container.encodeIfPresent(pendingExercises, forKey: .pendingExercises)
        
        // Encode date as ISO8601 string
        let isoFormatter = ISO8601DateFormatter()
        try container.encode(isoFormatter.string(from: joinedAt), forKey: .joinedAt)
    }
    
    // Convert to Classroom model (with empty students array for compatibility)
    func toClassroom() -> Classroom {
        return Classroom(
            id: id,
            name: name,
            grade: grade,
            teacherId: teacherId,
            teacherName: teacherName,
            students: [], // Empty since this summary doesn't include students
            createdAt: Date(), // Not provided in summary, use current date
            updatedAt: Date(), // Not provided in summary, use current date
            subject: subject,
            schoolYear: schoolYear
        )
    }
}

// MARK: - Classroom Student Model
struct ClassroomStudent: Identifiable, Codable {
    let id: String // Student's user ID
    let username: String
    let name: String
    let joinedAt: Date
    var status: StudentStatus // Active, Inactive, etc.
    let schoolId: String? // School ID to identify students from same school (optional for backward compatibility)
    
    enum StudentStatus: String, Codable, CaseIterable {
        case active = "active"
        case inactive = "inactive"
        case transferred = "transferred"
        
        var displayName: String {
            switch self {
            case .active: return "Active"
            case .inactive: return "Inactive"
            case .transferred: return "Transferred"
            }
        }
        
        var color: Color {
            switch self {
            case .active: return .green
            case .inactive: return .orange
            case .transferred: return .gray
            }
        }
    }
    
    init(
        id: String,
        username: String,
        name: String,
        joinedAt: Date = Date(),
        status: StudentStatus = .active,
        schoolId: String? = nil
    ) {
        self.id = id
        self.username = username
        self.name = name
        self.joinedAt = joinedAt
        self.status = status
        self.schoolId = schoolId
    }
    
    enum CodingKeys: String, CodingKey {
        case id
        case username
        case name
        case joinedAt
        case status
        case schoolId
    }
    
    // Custom decoder to handle date formats
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        self.id = try container.decode(String.self, forKey: .id)
        self.username = try container.decode(String.self, forKey: .username)
        self.name = try container.decode(String.self, forKey: .name)
        self.status = try container.decode(StudentStatus.self, forKey: .status)
        self.schoolId = try container.decodeIfPresent(String.self, forKey: .schoolId)
        
        // Handle MongoDB date parsing - can be either string or MongoDB date object
        if let mongoDate = try? container.decode(MongoDate.self, forKey: .joinedAt) {
            self.joinedAt = mongoDate.date
        } else {
            let joinedAtString = try container.decode(String.self, forKey: .joinedAt)
            self.joinedAt = try Classroom.parseDate(joinedAtString)
        }
    }
    
    // Custom encoder
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encode(id, forKey: .id)
        try container.encode(username, forKey: .username)
        try container.encode(name, forKey: .name)
        try container.encode(status, forKey: .status)
        try container.encodeIfPresent(schoolId, forKey: .schoolId)
        
        // Encode date as ISO8601 string
        let isoFormatter = ISO8601DateFormatter()
        try container.encode(isoFormatter.string(from: joinedAt), forKey: .joinedAt)
    }
}

// MARK: - Student Search Model
struct StudentSearchResult: Identifiable, Codable {
    let id: String
    let username: String
    let name: String
    let schoolId: String
    let grade: Grade?
    let email: String?
    let isAlreadyInClassroom: Bool
    
    init(
        id: String,
        username: String,
        name: String,
        schoolId: String,
        grade: Grade? = nil,
        email: String? = nil,
        isAlreadyInClassroom: Bool = false
    ) {
        self.id = id
        self.username = username
        self.name = name
        self.schoolId = schoolId
        self.grade = grade
        self.email = email
        self.isAlreadyInClassroom = isAlreadyInClassroom
    }
    
    // Convert to ClassroomStudent
    func toClassroomStudent() -> ClassroomStudent {
        return ClassroomStudent(
            id: id,
            username: username,
            name: name,
            joinedAt: Date(),
            status: .active,
            schoolId: schoolId // Optional schoolId from StudentApiResponse.school_id
        )
    }
}

// MARK: - Add Students Request (matches backend API)
struct AddStudentsToClassroomRequest: Codable {
    let studentIds: [String]
}

// MARK: - Student API Response Wrapper (from GET /api/student/)
struct StudentSearchApiResponse: Codable {
    let school: SchoolInfo
    let students: [StudentApiResponse]
    let total: Int
}

// MARK: - School Info
struct SchoolInfo: Codable {
    let id: String
    let schoolName: String
}

// MARK: - Student API Response (individual student)
struct StudentApiResponse: Identifiable, Codable {
    let id: String
    let username: String
    let firstName: String?
    let lastName: String?
    let school_id: String
    let role: String
    let credit: Int?
    let classrooms: [StudentClassroomInfo]? // Classrooms the student is enrolled in
    let currentClasses: [CurrentClassInfo]? // Current class information
    
    // Computed property to get full name
    var fullName: String {
        let first = firstName ?? ""
        let last = lastName ?? ""
        return "\(first) \(last)".trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    enum CodingKeys: String, CodingKey {
        case id = "_id"
        case username
        case firstName
        case lastName
        case school_id
        case role
        case credit
        case classrooms
        case currentClasses
    }
    
    // Convert to StudentSearchResult for UI compatibility
    func toStudentSearchResult(excludeClassroomId: String? = nil) -> StudentSearchResult? {
        // Only include students, not teachers
        guard role.lowercased() == "student" else { 
            print("🔍 Filtering out non-student: \(username) (role: \(role))")
            return nil 
        }
        
        // Check if student is already in the excluded classroom
        let isAlreadyInClassroom = excludeClassroomId != nil && 
                                  (classrooms?.contains { $0.classroomId == excludeClassroomId } ?? false)
        
        if let excludeId = excludeClassroomId {
            let studentClassroomIds = classrooms?.map { $0.classroomId } ?? []
            print("🔍 Student \(username): checking if in classroom \(excludeId). Current classrooms: \(studentClassroomIds)")
            print("🔍 Student \(username): isAlreadyInClassroom = \(isAlreadyInClassroom)")
        }
        
        return StudentSearchResult(
            id: id,
            username: username,
            name: fullName.isEmpty ? username : fullName,
            schoolId: school_id,
            grade: nil, // Grade not provided in API response
            email: nil, // Email not provided in API response
            isAlreadyInClassroom: isAlreadyInClassroom
        )
    }
}

// MARK: - Student Classroom Info
struct StudentClassroomInfo: Codable {
    let classroomId: String
    let joinedAt: MongoDate
    let status: String
}

// MARK: - Current Class Info
struct CurrentClassInfo: Codable {
    let name: String
    let grade: String
    let teacherName: String
}

// MARK: - API Request/Response Models

// Request model for creating a classroom
struct CreateClassroomRequest: Codable {
    let name: String
    let grade: Grade
    let subject: String?
    let schoolYear: String
}

// Enhanced request model for creating a classroom with teacher info
struct EnhancedCreateClassroomRequest: Codable {
    let name: String
    let grade: Grade
    let subject: String?
    let schoolYear: String
    let teacherId: String
    let teacherName: String
}



// Response wrapper for classroom creation
struct ClassroomCreateResponse: Codable {
    let message: String
    let classroom: Classroom
}

// Request model for updating student status
struct UpdateStudentStatusRequest: Codable {
    let studentId: String
    let status: ClassroomStudent.StudentStatus
}

// MARK: - Classroom Statistics
struct ClassroomStats: Codable {
    let totalStudents: Int
    let activeStudents: Int
    let inactiveStudents: Int
    let recentlyJoined: Int // Students joined in last 30 days
    let averageExerciseScore: Double?
    let completedExercises: Int
    let pendingExercises: Int
}



 