import Foundation

// The APIResponse and EmptyResponse types are defined below

// MARK: - API Error Models
struct APIErrorResponse: Codable {
    let message: String
    let error: String?
    let statusCode: Int?
}

enum APIError: Error {
    case invalidURL
    case noData
    case decodingError(String)
    case serverError(String)
    case unauthorized
    case networkError(String)
    
    var localizedDescription: String {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .noData:
            return "No data received"
        case .decodingError(let message):
            return "Decoding error: \(message)"
        case .serverError(let message):
            return "Server error: \(message)"
        case .unauthorized:
            return "Unauthorized access"
        case .networkError(let message):
            return "Network error: \(message)"
        }
    }
}

class APIService {
    static let shared = APIService()

    // Base URL for the API using APIConfig
    private var baseURL: String {
        return APIConfig.baseURL
    }

    // Configure URLSession with proper timeout settings
    private lazy var urlSession: URLSession = {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30.0  // 30 seconds for request timeout
        config.timeoutIntervalForResource = 60.0 // 60 seconds for resource timeout
        config.waitsForConnectivity = true       // Wait for connectivity
        return URLSession(configuration: config)
    }()

    private init() {}
    
    // MARK: - Private Helper Methods
    
    /// Creates a URLRequest with automatic authentication headers
    private func createAuthenticatedRequest(url: URL, method: String, contentType: String = "application/json") -> URLRequest {
        var request = URLRequest(url: url)
        request.httpMethod = method
        
        if !contentType.isEmpty {
            request.setValue(contentType, forHTTPHeaderField: "Content-Type")
        }
        
        // Automatically add authorization header if available
        if let authHeaders = UserManager.shared.getAuthorizationHeader() {
            for (key, value) in authHeaders {
                request.addValue(value, forHTTPHeaderField: key)
                print("🔑 APIService: Auto-added auth header: \(key): \(value.prefix(20))...")
            }
        } else {
            print("⚠️ APIService: No authorization token available - user might not be logged in")
        }
        
        return request
    }
    
    /// Handles authentication errors and automatic token refresh
    private func handleAuthenticationError(_ statusCode: Int) async -> APIError {
        if statusCode == 401 {
            print("🚨 APIService: Authentication failed (401) - attempting token refresh...")
            
            // Try to refresh the token first
            let refreshSuccess = await UserManager.shared.refreshToken()
            if refreshSuccess {
                print("✅ APIService: Token refreshed successfully")
                // Don't logout if refresh was successful - the caller should retry
                return .unauthorized
            } else {
                print("❌ APIService: Token refresh failed - logging out user")
                await MainActor.run {
                    UserManager.shared.logout()
                }
                return .unauthorized
            }
        } else if statusCode == 403 {
            print("🚨 APIService: Access denied (403) - insufficient permissions")
            return .unauthorized
        }
        return .serverError("Server error with status code: \(statusCode)")
    }
    
    /// Executes a request with automatic retry on authentication failure
    private func executeRequestWithRetry<T: Decodable>(_ request: URLRequest, responseType: T.Type) async -> Result<T, APIError> {
        // First attempt
        let result = await executeRequest(request, responseType: responseType)
        
        // If authentication failed and we're logged in, try to refresh token and retry once
        if case .failure(.unauthorized) = result, UserManager.shared.isLoggedIn {
            print("🔄 APIService: Authentication failed, attempting token refresh and retry...")
            
            let refreshSuccess = await UserManager.shared.refreshToken()
            if refreshSuccess {
                print("✅ APIService: Token refreshed, retrying request...")
                
                // Create a new request with the refreshed token
                var retryRequest = request
                if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                    for (key, value) in authHeaders {
                        retryRequest.setValue(value, forHTTPHeaderField: key)
                    }
                }
                
                // Retry the request once
                return await executeRequest(retryRequest, responseType: responseType)
            }
        }
        
        return result
    }
    
    /// Core request execution method
    private func executeRequest<T: Decodable>(_ request: URLRequest, responseType: T.Type) async -> Result<T, APIError> {
        do {
            print("📡 APIService: Sending \(request.httpMethod ?? "UNKNOWN") request to \(request.url?.absoluteString ?? "unknown")")
            let (data, response) = try await urlSession.data(for: request)

            print("📊 APIService: Response received, data size: \(data.count) bytes")

            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ APIService: Invalid HTTP response")
                return .failure(.invalidURL)
            }

            print("📈 APIService: HTTP Status Code: \(httpResponse.statusCode)")

            if httpResponse.statusCode >= 400 {
                print("❌ APIService: Server error with status code: \(httpResponse.statusCode)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("🔍 APIService: Error response body: \(responseString)")
                }

                let error = await handleAuthenticationError(httpResponse.statusCode)
                return .failure(error)
            }

            // Check for empty data response
            if data.isEmpty {
                print("❌ APIService: Empty data received from server")
                return .failure(.noData)
            }

            // Check if the response is just whitespace or empty JSON
            if let responseString = String(data: data, encoding: .utf8) {
                let trimmedResponse = responseString.trimmingCharacters(in: .whitespacesAndNewlines)
                if trimmedResponse.isEmpty {
                    print("❌ APIService: Response contains only whitespace")
                    return .failure(.noData)
                }
                print("📝 APIService: Response body: \(responseString)")
            }

            do {
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601
                let decodedData = try decoder.decode(T.self, from: data)
                print("✅ APIService: Successfully decoded response")
                return .success(decodedData)
            } catch {
                print("❌ APIService: Decoding error: \(error)")
                return .failure(.decodingError(error.localizedDescription))
            }
        } catch {
            print("❌ APIService: Request failed: \(error)")

            // Handle specific error types
            if let urlError = error as? URLError {
                switch urlError.code {
                case .cancelled:
                    print("🔄 APIService: Request was cancelled - this might be due to a refresh or navigation")
                    return .failure(.networkError("Request was cancelled. Please try again."))
                case .timedOut:
                    print("⏰ APIService: Request timed out")
                    return .failure(.networkError("Request timed out. Please check your internet connection and try again."))
                case .notConnectedToInternet:
                    print("📶 APIService: No internet connection")
                    return .failure(.networkError("No internet connection. Please check your network settings."))
                case .networkConnectionLost:
                    print("📶 APIService: Network connection lost")
                    return .failure(.networkError("Network connection lost. Please try again."))
                default:
                    print("🌐 APIService: Network error: \(urlError.localizedDescription)")
                    return .failure(.networkError(urlError.localizedDescription))
                }
            }

            return .failure(.networkError(error.localizedDescription))
        }
    }
    
    // MARK: - Public API Methods
    
    /// Generic fetch function for GET requests with automatic authentication
    func fetch<T: Decodable>(endpoint: String, responseType: T.Type = T.self) async -> Result<T, APIError> {
        let fullURL = "\(baseURL)/\(endpoint)"
        print("🌐 APIService: GET \(fullURL)")
        
        guard let url = URL(string: fullURL) else {
            print("❌ APIService: Invalid URL: \(fullURL)")
            return .failure(.invalidURL)
        }
        
        let request = createAuthenticatedRequest(url: url, method: "GET", contentType: "")
        return await executeRequestWithRetry(request, responseType: responseType)
    }
    
    /// Generic POST function with automatic authentication
    func post<T: Encodable, U: Decodable>(endpoint: String, body: T, responseType: U.Type = U.self) async -> Result<U, APIError> {
        let fullURL = "\(baseURL)/\(endpoint)"
        print("🌐 APIService: POST \(fullURL)")
        
        guard let url = URL(string: fullURL) else {
            print("❌ APIService: Invalid URL: \(fullURL)")
            return .failure(.invalidURL)
        }
        
        var request = createAuthenticatedRequest(url: url, method: "POST")
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let jsonData = try encoder.encode(body)
            request.httpBody = jsonData
            
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📤 APIService: Request body: \(bodyString)")
            }
        } catch {
            print("❌ APIService: Failed to encode request body: \(error)")
            return .failure(.networkError(error.localizedDescription))
        }
        
        return await executeRequestWithRetry(request, responseType: responseType)
    }
    
    /// Generic PUT function with automatic authentication
    func put<T: Encodable, U: Decodable>(endpoint: String, body: T, responseType: U.Type = U.self) async -> Result<U, APIError> {
        let fullURL = "\(baseURL)/\(endpoint)"
        print("🌐 APIService: PUT \(fullURL)")
        
        guard let url = URL(string: fullURL) else {
            print("❌ APIService: Invalid URL: \(fullURL)")
            return .failure(.invalidURL)
        }
        
        var request = createAuthenticatedRequest(url: url, method: "PUT")
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let jsonData = try encoder.encode(body)
            request.httpBody = jsonData
            
            if let bodyString = String(data: jsonData, encoding: .utf8) {
                print("📤 APIService: Request body: \(bodyString)")
            }
        } catch {
            print("❌ APIService: Failed to encode request body: \(error)")
            return .failure(.networkError(error.localizedDescription))
        }
        
        return await executeRequestWithRetry(request, responseType: responseType)
    }
    
    /// Generic DELETE function with automatic authentication
    func delete<T: Decodable>(endpoint: String, responseType: T.Type = T.self) async -> Result<T, APIError> {
        let fullURL = "\(baseURL)/\(endpoint)"
        print("🌐 APIService: DELETE \(fullURL)")
        
        guard let url = URL(string: fullURL) else {
            print("❌ APIService: Invalid URL: \(fullURL)")
            return .failure(.invalidURL)
        }
        
        let request = createAuthenticatedRequest(url: url, method: "DELETE", contentType: "")
        return await executeRequestWithRetry(request, responseType: responseType)
    }
    
    /// POST request without response body (for operations that don't return data)
    func post<T: Encodable>(endpoint: String, body: T) async -> Result<EmptyResponse, APIError> {
        return await post(endpoint: endpoint, body: body, responseType: EmptyResponse.self)
    }
    
    /// PUT request without response body
    func put<T: Encodable>(endpoint: String, body: T) async -> Result<EmptyResponse, APIError> {
        return await put(endpoint: endpoint, body: body, responseType: EmptyResponse.self)
    }
    
    /// DELETE request without response body
    func delete(endpoint: String) async -> Result<EmptyResponse, APIError> {
        return await delete(endpoint: endpoint, responseType: EmptyResponse.self)
    }
    
    /// Simple GET request for fetching raw data (like files, images, etc.)
    func fetchData(endpoint: String) async -> Result<Data, APIError> {
        let fullURL = "\(baseURL)/\(endpoint)"
        print("🌐 APIService: GET DATA \(fullURL)")
        
        guard let url = URL(string: fullURL) else {
            print("❌ APIService: Invalid URL: \(fullURL)")
            return .failure(.invalidURL)
        }
        
        let request = createAuthenticatedRequest(url: url, method: "GET", contentType: "")
        
        do {
            print("📡 APIService: Sending GET DATA request...")
            let (data, response) = try await urlSession.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("❌ APIService: Invalid HTTP response")
                return .failure(.invalidURL)
            }
            
            print("📈 APIService: HTTP Status Code: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode >= 400 {
                let error = await handleAuthenticationError(httpResponse.statusCode)
                return .failure(error)
            }
            
            print("✅ APIService: Successfully fetched data (\(data.count) bytes)")
            return .success(data)
        } catch {
            print("❌ APIService: Request failed: \(error)")
            return .failure(.networkError(error.localizedDescription))
        }
    }
}

// MARK: - Response Types

/// Generic API response wrapper
struct APIResponse<T: Decodable>: Decodable {
    let success: Bool
    let data: T
    let message: String?
}

/// Empty response for operations that don't return data
struct EmptyResponse: Decodable {
    // This can be empty or include status fields if needed
    let success: Bool?
    let message: String?

    init() {
        self.success = true
        self.message = nil
    }
}

// MARK: - Credit Deduction Types

/// Credit deduction API request structure
struct CreditDeductionRequest: Encodable {
    let amount: Int
    let description: String?
}

/// Credit deduction API response structure
struct CreditDeductionResponse: Decodable {
    let success: Bool
    let message: String?
    let newBalance: Int?
}

// MARK: - Admin Credit Adjustment Types

/// Admin credit adjustment API request structure
struct AdminCreditAdjustRequest: Encodable {
    let userId: String
    let amount: Int
    let description: String?
}

/// Admin credit adjustment API response structure
struct AdminCreditAdjustResponse: Decodable {
    let success: Bool?
    let message: String?
    let newBalance: Int?
    let credit: Int? // Alternative field name for balance

    // Manual initializer for fallback cases
    init(success: Bool?, message: String?, newBalance: Int?, credit: Int?) {
        self.success = success
        self.message = message
        self.newBalance = newBalance
        self.credit = credit
    }

    // Custom init to handle different response formats
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // Try to decode success field, default to true if missing
        success = try container.decodeIfPresent(Bool.self, forKey: .success) ?? true
        message = try container.decodeIfPresent(String.self, forKey: .message)
        newBalance = try container.decodeIfPresent(Int.self, forKey: .newBalance)
        credit = try container.decodeIfPresent(Int.self, forKey: .credit)
    }

    enum CodingKeys: String, CodingKey {
        case success, message, newBalance, credit
    }

    // Computed property to get the actual balance
    var actualBalance: Int? {
        return newBalance ?? credit
    }
}