//
//  MarkdownMathRenderer.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation
import SwiftUI

// MARK: - Content Segment for Markdown + Math
struct MarkdownMathSegment: Identifiable {
    let id = UUID()
    let content: String
    let type: SegmentType
    
    enum SegmentType {
        case markdown
        case inlineMath
        case displayMath
    }
}

// MARK: - Markdown Math Parser
class MarkdownMathRenderer {
    static let shared = MarkdownMathRenderer()
    
    private init() {}
    
    /// Parse content into segments separating markdown from math expressions
    func parseContent(_ content: String) -> [MarkdownMathSegment] {
        let text = content.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !text.isEmpty else { return [] }
        
        // First, auto-detect and wrap geometry expressions that need delimiters
        let preprocessedText = autoDetectGeometryExpressions(text)
        
        var segments: [MarkdownMathSegment] = []
        var currentIndex = preprocessedText.startIndex
        
        // Regex patterns for math detection (ordered by priority)
        let patterns: [(pattern: String, type: MarkdownMathSegment.SegmentType)] = [
            (#"\$\$[^$]+?\$\$"#, .displayMath),  // Display math: $$...$$
            (#"\\?\[[^\]]+?\\?\]"#, .displayMath), // Display math: \[...\]
            (#"\\?\([^)]+?\\?\)"#, .inlineMath),   // Inline math: \(...\)
            (#"\$[^$\n]{1,100}\$"#, .inlineMath)   // Inline math: $...$
        ]
        
        var allMatches: [(range: Range<String.Index>, content: String, type: MarkdownMathSegment.SegmentType)] = []
        
        // Find all math expressions
        for (patternString, type) in patterns {
            do {
                let regex = try NSRegularExpression(pattern: patternString, options: [])
                let nsRange = NSRange(preprocessedText.startIndex..<preprocessedText.endIndex, in: preprocessedText)
                let matches = regex.matches(in: preprocessedText, options: [], range: nsRange)
                
                for match in matches {
                    if let range = Range(match.range, in: preprocessedText) {
                        let content = String(preprocessedText[range])
                        if isValidMathExpression(content, type: type) {
                            allMatches.append((range: range, content: content, type: type))
                        }
                    }
                }
            } catch {
                print("Regex error for pattern \(patternString): \(error)")
            }
        }
        
        // Sort matches by position and remove overlaps
        allMatches.sort { $0.range.lowerBound < $1.range.lowerBound }
        allMatches = removeOverlappingMatches(allMatches)
        
        // Build segments
        for match in allMatches {
            // Add markdown content before this math expression
            if currentIndex < match.range.lowerBound {
                let markdownContent = String(preprocessedText[currentIndex..<match.range.lowerBound])
                if !markdownContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                    segments.append(MarkdownMathSegment(
                        content: markdownContent,
                        type: .markdown
                    ))
                }
            }
            
            // Add the math expression
            segments.append(MarkdownMathSegment(
                content: match.content,
                type: match.type
            ))
            
            currentIndex = match.range.upperBound
        }
        
        // Add remaining markdown content
        if currentIndex < preprocessedText.endIndex {
            let remainingContent = String(preprocessedText[currentIndex...])
            if !remainingContent.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                segments.append(MarkdownMathSegment(
                    content: remainingContent,
                    type: .markdown
                ))
            }
        }
        
        // If no math found, treat entire content as markdown
        if segments.isEmpty {
            segments.append(MarkdownMathSegment(
                content: preprocessedText,
                type: .markdown
            ))
        }
        
        return segments
    }
    
    /// Clean LaTeX expression by removing delimiters
    func cleanMathExpression(_ expression: String) -> String {
        var cleaned = expression.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove delimiters
        if cleaned.hasPrefix("$$") && cleaned.hasSuffix("$$") && cleaned.count > 4 {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        } else if cleaned.hasPrefix("$") && cleaned.hasSuffix("$") && cleaned.count > 2 {
            cleaned = String(cleaned.dropFirst().dropLast())
        } else if cleaned.hasPrefix("\\[") && cleaned.hasSuffix("\\]") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        } else if cleaned.hasPrefix("\\(") && cleaned.hasSuffix("\\)") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        }
        
        // Normalize common mathematical notations for better rendering
        let mathNormalizations = [
            // Degree symbols - ensure proper LaTeX format
            ("^\\\\circ", "^\\circ"),     // Fix over-escaped degree symbols
            ("^\\\\deg", "^\\deg"),       // Fix over-escaped degree abbreviations
            ("\\\\circ", "\\circ"),       // Fix over-escaped degree symbols
            ("\\\\deg", "\\deg"),         // Fix over-escaped degree abbreviations
            
            // Common math operators - normalize escaping
            ("\\\\times", "\\times"),
            ("\\\\cdot", "\\cdot"),
            ("\\\\div", "\\div"),
            ("\\\\pm", "\\pm"),
            ("\\\\mp", "\\mp"),
            ("\\\\frac", "\\frac"),
            ("\\\\sqrt", "\\sqrt"),
            ("\\\\text", "\\text"),
            ("\\\\sin", "\\sin"),
            ("\\\\cos", "\\cos"),
            ("\\\\tan", "\\tan"),
            ("\\\\log", "\\log"),
            ("\\\\ln", "\\ln"),
            
            // Greek letters - normalize escaping
            ("\\\\alpha", "\\alpha"),
            ("\\\\beta", "\\beta"),
            ("\\\\gamma", "\\gamma"),
            ("\\\\delta", "\\delta"),
            ("\\\\theta", "\\theta"),
            ("\\\\lambda", "\\lambda"),
            ("\\\\mu", "\\mu"),
            ("\\\\pi", "\\pi"),
            ("\\\\sigma", "\\sigma"),
            ("\\\\phi", "\\phi"),
            ("\\\\omega", "\\omega"),
            
            // Comparison operators
            ("\\\\leq", "\\leq"),
            ("\\\\geq", "\\geq"),
            ("\\\\neq", "\\neq"),
            ("\\\\approx", "\\approx"),
            ("\\\\equiv", "\\equiv"),
            
            // Special symbols
            ("\\\\infty", "\\infty"),
            ("\\\\partial", "\\partial"),
            ("\\\\nabla", "\\nabla"),
            ("\\\\angle", "\\angle"),
            ("\\\\triangle", "\\triangle"),
        ]
        
        for (pattern, replacement) in mathNormalizations {
            cleaned = cleaned.replacingOccurrences(of: pattern, with: replacement)
        }
        
        return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // MARK: - Private Helper Methods
    
    private func isValidMathExpression(_ expression: String, type: MarkdownMathSegment.SegmentType) -> Bool {
        let cleaned = cleanMathExpression(expression)
        
        // Basic validation
        guard cleaned.count > 0 && cleaned.count < 200 else { return false }
        
        // Enhanced math indicators including more LaTeX commands
        let mathIndicators = [
            "\\frac", "\\sqrt", "\\sum", "\\int", "\\lim", "\\sin", "\\cos", "\\tan",
            "\\alpha", "\\beta", "\\gamma", "\\pi", "\\theta", "\\infty",
            "\\times", "\\cdot", "\\div", "\\pm", "\\mp", "\\leq", "\\geq", "\\neq",
            "\\approx", "\\equiv", "\\circ", "\\deg", "\\angle", "\\triangle",
            "=", "^", "_", "+", "-", "*", "/", "×", "÷", "°", "∠"
        ]
        
        // Check for geometry symbols
        let geometrySymbols = ["∠", "°", "△", "□", "◊", "∥", "⊥"]
        
        let hasMathIndicators = mathIndicators.contains { cleaned.contains($0) }
        let hasGeometrySymbols = geometrySymbols.contains { cleaned.contains($0) }
        
        // If it contains geometry symbols, it's valid math
        if hasGeometrySymbols {
            return true
        }
        
        // For expressions with angle notation pattern
        if cleaned.range(of: #"∠[A-Z]{2,3}"#, options: .regularExpression) != nil {
            return true
        }
        
        // Check for degree symbols or LaTeX degree commands
        if cleaned.contains("°") || cleaned.contains("\\circ") || cleaned.contains("\\deg") {
            return true
        }
        
        // For short expressions, be more lenient
        if cleaned.count <= 20 && (hasMathIndicators || cleaned.contains("=")) {
            return true
        }
        
        // For longer expressions, require math indicators
        return hasMathIndicators
    }
    
    private func removeOverlappingMatches(_ matches: [(range: Range<String.Index>, content: String, type: MarkdownMathSegment.SegmentType)]) -> [(range: Range<String.Index>, content: String, type: MarkdownMathSegment.SegmentType)] {
        var result: [(range: Range<String.Index>, content: String, type: MarkdownMathSegment.SegmentType)] = []
        
        for match in matches {
            let overlaps = result.contains { existing in
                match.range.overlaps(existing.range)
            }
            
            if !overlaps {
                result.append(match)
            }
        }
        
        return result
    }
    
    /// Auto-detect geometry expressions and wrap them in math delimiters
    private func autoDetectGeometryExpressions(_ text: String) -> String {
        var result = text
        
        // Patterns for geometric expressions that need to be wrapped in $ delimiters
        let geometryPatterns: [(String, String)] = [
            // Complete angle expressions with degree symbols - keep as display math for complex expressions
            (#"(?<!\$)(∠[A-Z]{2,3}\s*=\s*\([^)]+\)°)\b(?!\$)"#, "$$$1$$"),
            // Standalone angle symbols - keep as display math
            (#"(?<!\$)(∠[A-Z]{2,3})\b(?!\$)"#, "$$$1$$"),
            // Expressions in parentheses with degree symbols - use inline math for better flow
            (#"(?<!\$)(\([^)]+\)°)\b(?!\$)"#, "$$1$"),
            // Simple degree expressions - use inline math for better flow
            (#"(?<!\$)(\d+[xy]?\s*[+\-]\s*\d+)°\b(?!\$)"#, "$($1)°$"),
            (#"(?<!\$)(\d+[xy]?)°\b(?!\$)"#, "$$1°$"),
            
            // Mathematical expressions with LaTeX commands that need wrapping
            (#"(?<!\$)\\times(?!\$)"#, "$\\times$"),
            (#"(?<!\$)\\cdot(?!\$)"#, "$\\cdot$"),
            (#"(?<!\$)\\div(?!\$)"#, "$\\div$"),
            (#"(?<!\$)\\pm(?!\$)"#, "$\\pm$"),
            (#"(?<!\$)\\mp(?!\$)"#, "$\\mp$"),
            (#"(?<!\$)\\circ(?!\$)"#, "$\\circ$"),
            (#"(?<!\$)\\deg(?!\$)"#, "$\\deg$"),
            (#"(?<!\$)\\angle(?!\$)"#, "$\\angle$"),
            (#"(?<!\$)\\triangle(?!\$)"#, "$\\triangle$"),
            
            // Greek letters that appear standalone
            (#"(?<!\$)\\alpha(?!\$)"#, "$\\alpha$"),
            (#"(?<!\$)\\beta(?!\$)"#, "$\\beta$"),
            (#"(?<!\$)\\gamma(?!\$)"#, "$\\gamma$"),
            (#"(?<!\$)\\delta(?!\$)"#, "$\\delta$"),
            (#"(?<!\$)\\theta(?!\$)"#, "$\\theta$"),
            (#"(?<!\$)\\lambda(?!\$)"#, "$\\lambda$"),
            (#"(?<!\$)\\mu(?!\$)"#, "$\\mu$"),
            (#"(?<!\$)\\pi(?!\$)"#, "$\\pi$"),
            (#"(?<!\$)\\sigma(?!\$)"#, "$\\sigma$"),
            (#"(?<!\$)\\phi(?!\$)"#, "$\\phi$"),
            (#"(?<!\$)\\omega(?!\$)"#, "$\\omega$"),
        ]
        
        for (pattern, replacement) in geometryPatterns {
            do {
                let regex = try NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
                result = regex.stringByReplacingMatches(in: result,
                                                       options: [],
                                                       range: NSRange(result.startIndex..<result.endIndex, in: result),
                                                       withTemplate: replacement)
            } catch {
                print("AutoDetect regex error for pattern \(pattern): \(error)")
            }
        }
        
        // Post-process to fix common issues from auto-detection
        // Fix multiple consecutive dollar signs
        result = result.replacingOccurrences(of: "$$$$", with: "$$") // Remove quadruple delimiters
        result = result.replacingOccurrences(of: "$$$", with: "$")   // Remove triple delimiters
        
        // Ensure proper spacing around inline math for better readability
        // Add space before $ if there's a letter or number immediately before
        result = result.replacingOccurrences(of: #"([a-zA-Z0-9])\$"#, with: "$1 $", options: .regularExpression)
        // Add space after $ if there's a letter or number immediately after
        result = result.replacingOccurrences(of: #"\$([a-zA-Z0-9])"#, with: "$ $1", options: .regularExpression)
        
        // Clean up excessive spaces
        result = result.replacingOccurrences(of: "  +", with: " ", options: .regularExpression)
        result = result.trimmingCharacters(in: .whitespacesAndNewlines)
        
        print("🔍 MarkdownMathRenderer: Auto-detected geometry: '\(text)' -> '\(result)'")
        
        return result
    }
} 