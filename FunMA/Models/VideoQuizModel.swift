//
//  VideoQuizModel.swift
//  Luminous Education
//
//  Created by <PERSON> on 6/11/2024.
//

import Foundation
import AVKit

// Model for a single quiz question
struct VideoQuizQuestion: Identifiable, Equatable {
    let id = UUID()
    let timestamp: TimeInterval  // When to show the question
    let question: String
    let options: [String]
    let correctAnswerIndex: Int
    let duration: TimeInterval   // How long to show the question
    let points: Int             // Points for correct answer
    
    // Optional explanation for the answer
    let explanation: String?
}

// Model to track user's response
struct QuizResponse {
    let questionId: UUID
    let selectedAnswerIndex: Int
    let isCorrect: Bool
    let timestamp: Date
    let timeToAnswer: TimeInterval
}

// Class to manage quiz state and scoring
class VideoQuizManager: ObservableObject {
    @Published var currentQuestion: VideoQuizQuestion?
    @Published var isShowingQuestion = false
    @Published var score = 0
    @Published var responses: [QuizResponse] = []
    
    private var questions: [VideoQuizQuestion]
    private var currentVideoTime: TimeInterval = 0
    private var questionStartTime: Date?
    
    // Reference to the video player
    private weak var player: AVPlayer?
    
    init(questions: [VideoQuizQuestion]) {
        self.questions = questions.sorted { $0.timestamp < $1.timestamp }
    }
    
    // Public method to get quiz timestamps
    var quizTimestamps: [TimeInterval] {
        questions.map { $0.timestamp }
    }
    
    // Set the player reference
    func setPlayer(_ player: AVPlayer) {
        self.player = player
    }
    
    // Update current video time and check if we should show a question
    func updateTime(_ time: TimeInterval) {
        currentVideoTime = time
        checkForQuestions()
    }
    
    // Check if we should display a question at current time
    private func checkForQuestions() {
        guard !isShowingQuestion else { return }
        
        if let nextQuestion = questions.first(where: { question in
            question.timestamp <= currentVideoTime && 
            !responses.contains(where: { $0.questionId == question.id })
        }) {
            // Pause the video when showing a question
            player?.pause()
            showQuestion(nextQuestion)
        }
    }
    
    // Display a question and start timing
    private func showQuestion(_ question: VideoQuizQuestion) {
        currentQuestion = question
        isShowingQuestion = true
        questionStartTime = Date()
    }
    
    // Handle user's answer
    func submitAnswer(_ answerIndex: Int) {
        guard let question = currentQuestion,
              let startTime = questionStartTime else { return }
        
        let timeToAnswer = Date().timeIntervalSince(startTime)
        let isCorrect = answerIndex == question.correctAnswerIndex
        
        if isCorrect {
            score += question.points
        }
        
        let response = QuizResponse(
            questionId: question.id,
            selectedAnswerIndex: answerIndex,
            isCorrect: isCorrect,
            timestamp: Date(),
            timeToAnswer: timeToAnswer
        )
        
        responses.append(response)
        
        // Hide question after recording response
        currentQuestion = nil
        isShowingQuestion = false
        questionStartTime = nil
        
        // Resume video playback after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.player?.play()
        }
    }
    
    // Reset quiz state
    func reset() {
        currentQuestion = nil
        isShowingQuestion = false
        score = 0
        responses.removeAll()
        questionStartTime = nil
    }
} 