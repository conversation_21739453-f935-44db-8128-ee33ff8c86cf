//
//  DeepSeekAPI.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation
import Combine

// DeepSeek API response structure
struct DeepSeekResponse: Decodable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [Choice]
    
    struct Choice: Decodable {
        let index: Int
        let message: Message
        let finishReason: String?
        
        enum CodingKeys: String, CodingKey {
            case index
            case message
            case finishReason = "finish_reason"
        }
    }
    
    struct Message: Decodable {
        let role: String
        let content: String
    }
}

// DeepSeek streaming response structure
struct DeepSeekStreamResponse: Decodable {
    let id: String
    let object: String
    let created: Int
    let model: String
    let choices: [Choice]
    
    struct Choice: Decodable {
        let index: Int
        let delta: Delta
        let finishReason: String?
        
        enum CodingKeys: String, CodingKey {
            case index
            case delta
            case finishReason = "finish_reason"
        }
    }
    
    struct Delta: Decodable {
        let role: String?
        let content: String?
    }
}

// Request structure for DeepSeek API
struct DeepSeekRequest: Encodable {
    let model: String
    let messages: [Message]
    let temperature: Double
    let stream: Bool
    
    struct Message: Encodable {
        let role: String // "system", "user", or "assistant"
        let content: String
    }
}

class DeepSeekService: ObservableObject {
    private let apiURL = URL(string: "https://api.deepseek.com/chat/completions")!
    private let model = "deepseek-chat" // Default model, can be changed as needed
    private var apiKey: String? = "sk-c09acf930ad249698b5888e1fe45fb09" // API key should be securely stored
    
    @Published var isLoading = false
    @Published var error: String? = nil
    
    // Stream response publisher
    private var streamSubject = PassthroughSubject<String, Error>()
    
    // Set API key - in a real app, this should use secure storage
    func setAPIKey(_ key: String) {
        self.apiKey = key
    }
    
    // Send a message to the DeepSeek API and get a streaming response
    func sendStreamingMessage(messages: [ChatMessage]) -> AnyPublisher<String, Error> {
        guard let apiKey = apiKey, !apiKey.isEmpty else {
            return Fail(error: NSError(domain: "DeepSeekService", code: 401, userInfo: [NSLocalizedDescriptionKey: "API key not set"]))
                .eraseToAnyPublisher()
        }
        
        // Convert ChatMessages to the format expected by DeepSeek API
        let requestMessages = messages.map { chatMessage in
            DeepSeekRequest.Message(
                role: chatMessage.role.rawValue,
                content: chatMessage.content
            )
        }
        
        let request = DeepSeekRequest(
            model: model,
            messages: requestMessages,
            temperature: 0.7, // Controls randomness, can be adjusted
            stream: true
        )
        
        // Create and configure the request
        var urlRequest = URLRequest(url: apiURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        self.isLoading = true
        self.streamSubject = PassthroughSubject<String, Error>()
        
        // Create the session and task
        let session = URLSession.shared
        let task = session.dataTask(with: urlRequest) { [weak self] data, response, error in
            guard let self = self else { return }
            
            if let error = error {
                DispatchQueue.main.async {
                    self.error = error.localizedDescription
                    self.isLoading = false
                    self.streamSubject.send(completion: .failure(error))
                }
                return
            }
            
            guard let data = data else {
                let error = NSError(domain: "DeepSeekService", code: 0, userInfo: [NSLocalizedDescriptionKey: "No data received"])
                DispatchQueue.main.async {
                    self.error = error.localizedDescription
                    self.isLoading = false
                    self.streamSubject.send(completion: .failure(error))
                }
                return
            }
            
            // Process streaming data
            self.processStreamData(data)
            
            DispatchQueue.main.async {
                self.isLoading = false
                self.streamSubject.send(completion: .finished)
            }
        }
        
        task.resume()
        
        return self.streamSubject
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
    
    // Process streaming data from the response
    private func processStreamData(_ data: Data) {
        // Split the data by "data: " prefix and line breaks
        let dataString = String(decoding: data, as: UTF8.self)
        let lines = dataString.components(separatedBy: "data: ")
        
        for line in lines {
            let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
            if trimmedLine.isEmpty || trimmedLine == "[DONE]" {
                continue
            }
            
            do {
                if let data = trimmedLine.data(using: .utf8) {
                    let streamResponse = try JSONDecoder().decode(DeepSeekStreamResponse.self, from: data)
                    if let content = streamResponse.choices.first?.delta.content {
                        DispatchQueue.main.async {
                            self.streamSubject.send(content)
                        }
                    }
                }
            } catch {
                DispatchQueue.main.async {
                    self.error = "Error decoding stream response: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // Send a message to the DeepSeek API and get a complete response (non-streaming)
    func sendMessage(messages: [ChatMessage]) -> AnyPublisher<ChatMessage, Error> {
        guard let apiKey = apiKey, !apiKey.isEmpty else {
            return Fail(error: NSError(domain: "DeepSeekService", code: 401, userInfo: [NSLocalizedDescriptionKey: "API key not set"]))
                .eraseToAnyPublisher()
        }
        
        // Convert ChatMessages to the format expected by DeepSeek API
        let requestMessages = messages.map { chatMessage in
            DeepSeekRequest.Message(
                role: chatMessage.role.rawValue,
                content: chatMessage.content
            )
        }
        
        let request = DeepSeekRequest(
            model: model,
            messages: requestMessages,
            temperature: 0.7, // Controls randomness, can be adjusted
            stream: false
        )
        
        // Create and configure the request
        var urlRequest = URLRequest(url: apiURL)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        urlRequest.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        
        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
        } catch {
            return Fail(error: error).eraseToAnyPublisher()
        }
        
        self.isLoading = true
        
        // Send the request
        return URLSession.shared.dataTaskPublisher(for: urlRequest)
            .map(\.data)
            .decode(type: DeepSeekResponse.self, decoder: JSONDecoder())
            .map { response in
                if let choice = response.choices.first {
                    return ChatMessage(content: choice.message.content, isUser: false)
                } else {
                    return ChatMessage(content: "No response from the AI.", isUser: false)
                }
            }
            .handleEvents(
                receiveSubscription: { _ in self.isLoading = true },
                receiveOutput: { _ in self.isLoading = false },
                receiveCompletion: { completion in
                    self.isLoading = false
                    if case .failure(let error) = completion {
                        self.error = error.localizedDescription
                    }
                }
            )
            .receive(on: DispatchQueue.main)
            .eraseToAnyPublisher()
    }
} 
