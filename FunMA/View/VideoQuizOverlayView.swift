//
//  VideoQuizOverlayView.swift
//  Luminous Education
//
//  Created by <PERSON> on 6/11/2024.
//

import SwiftUI
import UIKit

// VideoQuizOverlayView: Displays popup quiz questions during video playback
struct VideoQuizOverlayView: View {
    // Properties
    let question: VideoQuizQuestion
    let onAnswer: (Int) -> Void
    
    // Animation state
    @State private var animationState = 0
    @State private var animateOptions = false
    
    // Answer selection state
    @State private var selectedAnswerIndex: Int? = nil
    @State private var showFeedback = false
    
    // Colors
    let backgroundGradient = LinearGradient(
        gradient: Gradient(colors: [Color.indigo.opacity(0.8), Color.blue.opacity(0.8)]),
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    var body: some View {
        ZStack {
            // Semi-transparent background to focus on the quiz
            Color.black.opacity(0.7)
                .edgesIgnoringSafeArea(.all)
            
            // Quiz card
            VStack(spacing: 25) {
                // Question title with icon
                HStack {
                    Image(systemName: "questionmark.circle.fill")
                        .font(.largeTitle)
                        .foregroundColor(.white)
                        .padding(.trailing, 8)
                    
                    Text(question.question)
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 25)
                .padding(.horizontal)
                
                // Points indicator
                Text("Points: \(question.points)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white.opacity(0.9))
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(Color.white.opacity(0.2))
                    )
                
                // Answer options with staggered animation
                VStack(spacing: 14) {
                    ForEach(0..<question.options.count, id: \.self) { index in
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.7)) {
                                selectAnswer(index)
                            }
                        }) {
                            HStack {
                                Text("\(["A", "B", "C", "D"][min(index, 3)])")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(width: 32, height: 32)
                                    .background(Circle().fill(Color.white.opacity(0.2)))
                                
                                Text(question.options[index])
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding(.leading, 5)
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .padding(.horizontal, 16)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(getButtonColor(for: index))
                                    .shadow(color: Color.black.opacity(0.1), radius: 3, x: 0, y: 2)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.white, lineWidth: selectedAnswerIndex == index ? 2 : 0)
                            )
                            .scaleEffect(selectedAnswerIndex == index ? 1.03 : 1.0)
                        }
                        .disabled(showFeedback)
                        .offset(x: animateOptions ? 0 : -300)
                        .opacity(animateOptions ? 1 : 0)
                        .animation(
                            Animation.spring(response: 0.4, dampingFraction: 0.7)
                                .delay(Double(index) * 0.1),
                            value: animateOptions
                        )
                    }
                }
                .padding(.horizontal)
                
                // Feedback after answering
                if showFeedback {
                    VStack(spacing: 12) {
                        HStack {
                            Image(systemName: selectedAnswerIndex == question.correctAnswerIndex ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .font(.title)
                                .foregroundColor(selectedAnswerIndex == question.correctAnswerIndex ? .green : .red)
                            
                            Text(selectedAnswerIndex == question.correctAnswerIndex ? "Correct!" : "Incorrect!")
                                .font(.title2)
                                .foregroundColor(.white)
                                .fontWeight(.bold)
                        }
                        
                        if let explanation = question.explanation, !explanation.isEmpty {
                            Text(explanation)
                                .font(.body)
                                .foregroundColor(.white.opacity(0.9))
                                .padding(16)
                                .background(
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.white.opacity(0.1))
                                )
                                .padding(.horizontal)
                                .multilineTextAlignment(.center)
                        }
                        
                        // Continue Video button
                        Button(action: {
                            onAnswer(selectedAnswerIndex ?? 0)
                        }) {
                            HStack {
                                Image(systemName: "play.fill")
                                    .font(.headline)
                                Text("Continue Video")
                                    .font(.headline)
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 25)
                                    .fill(Color.blue)
                            )
                            .shadow(color: Color.black.opacity(0.2), radius: 4)
                        }
                        .padding(.top, 16)
                    }
                    .padding()
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
            .frame(maxWidth: 550)
            .padding(.vertical, 30)
            .background(
                RoundedRectangle(cornerRadius: 24)
                    .fill(backgroundGradient)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 24)
                    .stroke(Color.white.opacity(0.2), lineWidth: 1)
            )
            .shadow(color: Color.black.opacity(0.4), radius: 20, x: 0, y: 10)
            .padding(.horizontal, 20)
            .scaleEffect(animationState == 1 ? 1 : 0.8)
            .opacity(animationState == 1 ? 1 : 0)
        }
        .onAppear {
            // Start entrance animations
            withAnimation(.spring(response: 0.5, dampingFraction: 0.7)) {
                animationState = 1
            }
            
            // Animate options with slight delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                withAnimation {
                    animateOptions = true
                }
            }
        }
    }
    
    // Helper function to select an answer
    private func selectAnswer(_ index: Int) {
        selectedAnswerIndex = index
        showFeedback = true
        
        // Play haptic feedback
        #if os(iOS)
        let generator = UIImpactFeedbackGenerator(style: .medium)
        generator.impactOccurred()
        #endif
        
        // Remove the automatic callback - now waiting for user to click Continue
    }
    
    // Helper function to determine button color based on state
    private func getButtonColor(for index: Int) -> Color {
        if !showFeedback {
            return Color.blue.opacity(0.5)
        } else if index == question.correctAnswerIndex {
            return Color.green.opacity(0.6)
        } else if index == selectedAnswerIndex {
            return Color.red.opacity(0.6)
        } else {
            return Color.blue.opacity(0.3)
        }
    }
}

#Preview {
    VideoQuizOverlayView(
        question: VideoQuizQuestion(
            timestamp: 30.0,
            question: "What is the main topic of this video?",
            options: ["Option 1", "Option 2", "Option 3", "Option 4"],
            correctAnswerIndex: 0,
            duration: 20.0,
            points: 10,
            explanation: "This is the explanation for the correct answer."
        ),
        onAnswer: { _ in }
    )
} 
