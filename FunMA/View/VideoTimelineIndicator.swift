//
//  VideoTimelineIndicator.swift
//  Luminous Education
//
//  Created by <PERSON> on 6/11/2024.
//

import SwiftUI

struct VideoTimelineIndicator: View {
    var currentTime: TimeInterval
    var duration: TimeInterval
    var quizTimes: [TimeInterval]
    var toolTimes: [TimeInterval] = []  // New parameter for tool timestamps
    var onSeek: (TimeInterval) -> Void  // Add callback for seeking
    
    // State for displaying tooltip when hovering over a quiz marker
    @State private var hoveredQuizIndex: Int? = nil
    @State private var hoveredToolIndex: Int? = nil
    @State private var isDragging = false
    @State private var dragTime: TimeInterval = 0
    
    var body: some View {
        GeometryReader { geometry in
            VStack(spacing: 4) {
                // Timeline with progress bar and quiz markers
                ZStack(alignment: .leading) {
                    // Background track - make it interactive
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(height: 4)
                        .cornerRadius(2)
                        .gesture(
                            DragGesture(minimumDistance: 0)
                                .onChanged { value in
                                    isDragging = true
                                    let ratio = value.location.x / geometry.size.width
                                    dragTime = TimeInterval(ratio) * duration
                                    dragTime = max(0, min(dragTime, duration))
                                }
                                .onEnded { value in
                                    let ratio = value.location.x / geometry.size.width
                                    let seekTime = TimeInterval(ratio) * duration
                                    onSeek(max(0, min(seekTime, duration)))
                                    isDragging = false
                                }
                        )
                    
                    // Progress bar
                    Rectangle()
                        .fill(Color.blue)
                        .frame(width: max(0, CGFloat((isDragging ? dragTime : currentTime) / max(1, duration)) * geometry.size.width), height: 4)
                        .cornerRadius(2)
                    
                    // Draggable thumb
                    Circle()
                        .fill(Color.white)
                        .frame(width: 12, height: 12)
                        .shadow(radius: 2)
                        .offset(x: max(0, CGFloat((isDragging ? dragTime : currentTime) / max(1, duration)) * geometry.size.width - 6))
                    
                    // Quiz markers on the timeline
                    ForEach(quizTimes.indices, id: \.self) { index in
                        let quizTime = quizTimes[index]
                        let position = CGFloat(quizTime / max(1, duration)) * geometry.size.width
                        
                        ZStack {
                            // Quiz marker
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 12, height: 12)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )
                                .shadow(color: Color.black.opacity(0.3), radius: 2)
                                .offset(x: position - 6)  // Center the circle on the position
                                .zIndex(1) // Ensure it's above the progress bar
                                
                                // Show tooltip when hovering
                                .onHover { isHovered in
                                    withAnimation(.easeInOut(duration: 0.2)) {
                                        hoveredQuizIndex = isHovered ? index : nil
                                    }
                                }
                            
                            // Tooltip
                            if hoveredQuizIndex == index {
                                VStack(spacing: 4) {
                                    Text("Quiz Question")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                    Text("at \(formatTime(quizTime))")
                                        .font(.caption2)
                                }
                                .padding(8)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color.black.opacity(0.8))
                                )
                                .foregroundColor(.white)
                                .offset(x: position - 40, y: -40)
                                .transition(.opacity)
                                .zIndex(2)
                            }
                            
                            // Pulse animation for upcoming quiz
                            if quizTime > currentTime && quizTime - currentTime <= 10 {  // Within 10 seconds
                                Circle()
                                    .stroke(Color.orange.opacity(0.5), lineWidth: 2)
                                    .frame(width: 20, height: 20)
                                    .offset(x: position - 10)
                                    .scaleEffect(1.5)
                                    .opacity(0)
                                    .animation(
                                        Animation.easeInOut(duration: 1)
                                            .repeatForever(autoreverses: true),
                                        value: currentTime
                                    )
                            }
                        }
                        .animation(.easeInOut(duration: 0.2), value: hoveredQuizIndex)
                    }
                    
                    // Interactive tools markers
                    ForEach(toolTimes.indices, id: \.self) { index in
                        let toolTime = toolTimes[index]
                        let position = CGFloat(toolTime / max(1, duration)) * geometry.size.width
                        
                        ZStack {
                            // Tool marker (blue instead of orange)
                            Circle()
                                .fill(Color.blue)
                                .frame(width: 12, height: 12)
                                .overlay(
                                    Circle()
                                        .stroke(Color.white, lineWidth: 2)
                                )
                                .shadow(color: Color.black.opacity(0.3), radius: 2)
                                .offset(x: position - 6)  // Center the circle on the position
                                .zIndex(1) // Ensure it's above the progress bar
                                
                                // Show tooltip when hovering
                                .onHover { isHovered in
                                    withAnimation(.easeInOut(duration: 0.2)) {
                                        hoveredToolIndex = isHovered ? index : nil
                                    }
                                }
                            
                            // Tooltip
                            if hoveredToolIndex == index {
                                VStack(spacing: 4) {
                                    Text("Interactive Tools")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                    Text("at \(formatTime(toolTime))")
                                        .font(.caption2)
                                }
                                .padding(8)
                                .background(
                                    RoundedRectangle(cornerRadius: 6)
                                        .fill(Color.black.opacity(0.8))
                                )
                                .foregroundColor(.white)
                                .offset(x: position - 40, y: -40)
                                .transition(.opacity)
                                .zIndex(2)
                            }
                            
                            // Pulse animation for upcoming tools
                            if toolTime > currentTime && toolTime - currentTime <= 10 {  // Within 10 seconds
                                Circle()
                                    .stroke(Color.blue.opacity(0.5), lineWidth: 2)
                                    .frame(width: 20, height: 20)
                                    .offset(x: position - 10)
                                    .scaleEffect(1.5)
                                    .opacity(0)
                                    .animation(
                                        Animation.easeInOut(duration: 1)
                                            .repeatForever(autoreverses: true),
                                        value: currentTime
                                    )
                            }
                        }
                        .animation(.easeInOut(duration: 0.2), value: hoveredToolIndex)
                    }
                }
                
                // Time indicators
                HStack {
                    Text(formatTime(isDragging ? dragTime : currentTime))
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Spacer()
                    
                    Text(formatTime(duration))
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .frame(height: 30)
    }
    
    private func formatTime(_ timeInterval: TimeInterval) -> String {
        let totalSeconds = Int(timeInterval)
        let minutes = totalSeconds / 60
        let seconds = totalSeconds % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

#Preview {
    VideoTimelineIndicator(
        currentTime: 75,
        duration: 300,
        quizTimes: [30, 150, 240],
        onSeek: { _ in }
    )
    .previewLayout(.fixed(width: 400, height: 50))
} 