import SwiftUI

struct ExerciseQuestionsReviewView: View {
    let exercise: Exercise
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(Array(exercise.questions.enumerated()), id: \.element.id) { index, question in
                    Section(header: Text("Question \(index + 1)").font(.headline).foregroundColor(.primary)) {
                        // Question text in content area with proper layout
                        VStack(alignment: .leading, spacing: 8) {
                            QuestionTextRenderer(text: question.questionText, fontSize: 18)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .fixedSize(horizontal: false, vertical: true)
                                .padding(.vertical, 4)
                        }
                        .padding(.horizontal, 4)
                        
                        // Options
                        if let options = question.options {
                            ForEach(Array(options.enumerated()), id: \.offset) { idx, option in
                                HStack(alignment: .top, spacing: 8) {
                                    Text("\(Character(UnicodeScalar(65 + idx)!)).")
                                        .font(.system(size: 18))
                                        .foregroundColor(.gray)
                                        .frame(width: 20, alignment: .leading)
                                    QuestionTextRenderer(text: option, fontSize: 18)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(.vertical, 2)
                            }
                            // Show correct answer for multiple choice
                            if question.questionType == .multipleChoice, options.indices.contains(question.correctAnswerIndex) {
                                let correctLetter = String(Character(UnicodeScalar(65 + question.correctAnswerIndex)!))
                                let correctText = options[question.correctAnswerIndex]
                                HStack(alignment: .center, spacing: 8) {
                                    Text("Correct Answer: \(correctLetter).")
                                        .font(.subheadline)
                                        .fontWeight(.semibold)
                                        .foregroundColor(.green)
                                    QuestionTextRenderer(text: correctText, fontSize: 18)
                                }
                                .padding(.top, 4)
                            }
                        }
                        
                        // Points
                        HStack {
                            Spacer()
                            Text("Points: \(question.points)")
                                .font(.caption)
                                .foregroundColor(.blue)
                                .padding(.top, 4)
                        }
                    }
                }
            }
            .navigationTitle("Review Questions")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    ExerciseQuestionsReviewView(
        exercise: Exercise(
            title: "Sample Exercise",
            topic: "Algebra",
            subtopic: "Linear Equations",
            classroomIds: ["classroom1"],
            questions: [
                Question(
                    questionText: "What is 2 + 2?",
                    questionType: .multipleChoice,
                    options: ["3", "4", "5", "6"],
                    correctAnswerIndex: 1,
                    points: 10
                )
            ],
            createdBy: "teacher1",
            dueDate: Date()
        )
    )
}
