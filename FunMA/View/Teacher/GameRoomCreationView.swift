import SwiftUI

struct GameRoomCreationView: View {
    @StateObject private var gameRoomManager = GameRoomManager.shared
    @StateObject private var userManager = UserManager.shared
    @State private var selectedGame: Game?
    @State private var showingGameRoom = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isCreatingRoom = false
    @State private var webSocketStatus = "Disconnected"
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Show normal launch game interface
                launchGameInterface
            }
        }
        .fullScreenCover(isPresented: $showingGameRoom) {
            GameRoomView()
        }
        .alert("Error", isPresented: $showingAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .task {
            // Initial load of grades
            await gameRoomManager.fetchGrades()

            // Set Grade 1 (Form 1) as default selection
            if gameRoomManager.availableGrades.contains(.form1) {
                gameRoomManager.selectedGrade = .form1
            }

            // Check WebSocket server status
            await checkWebSocketStatus()
        }
    }
    
    private var launchGameInterface: some View {
        VStack(spacing: 20) {
            // Header
            HStack {
                Text("Launch Game")
                    .font(.title)
                    .fontWeight(.bold)
                Spacer()
                
                // WebSocket Status Indicator
                HStack(spacing: 4) {
                    Circle()
                        .fill(webSocketStatus == "Connected" ? Color.green : Color.red)
                        .frame(width: 8, height: 8)
                    Text(webSocketStatus)
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            .padding()
            
            // Grade Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Select Grade")
                    .font(.headline)
                
                if gameRoomManager.availableGrades.isEmpty {
                    Text("No grades available")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .padding()
                } else {
                    Picker("Grade", selection: $gameRoomManager.selectedGrade) {
                        ForEach(gameRoomManager.availableGrades
                            .sorted { $0.displayName.localizedStandardCompare($1.displayName) == .orderedAscending }, id: \.self
                        ) { grade in
                            Text(grade.displayName).tag(Optional(grade))
                        }
                    }
                    .pickerStyle(.segmented)
                }
            }
            .padding()
            
            if gameRoomManager.isLoading {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let error = gameRoomManager.error {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)
                    
                    Text("Error Loading Data")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(error.localizedDescription)
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                    
                    Button("Retry") {
                        Task {
                            await gameRoomManager.retryFetchData()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if gameRoomManager.availableTopics.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    
                    Text("No Topics Available")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("There are no topics available for this grade level.")
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // Topic Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Select Topic")
                        .font(.headline)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(gameRoomManager.availableTopics
                                .filter { $0.grade == gameRoomManager.selectedGrade }
                                .sorted { $0.name.localizedStandardCompare($1.name) == .orderedAscending }
                            ) { topic in
                                TopicCard(
                                    topic: topic,
                                    isSelected: gameRoomManager.selectedTopic?.id == topic.id,
                                    action: {
                                        if gameRoomManager.selectedTopic?.id != topic.id {
                                            gameRoomManager.selectTopic(topic)
                                            selectedGame = nil
                                        }
                                    }
                                )
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
                
                // Game Selection
                if gameRoomManager.selectedTopic != nil {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Select Game")
                            .font(.headline)
                        
                        if gameRoomManager.availableGames.isEmpty {
                            Text("No games available for this topic")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .padding()
                        } else {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(gameRoomManager.availableGames
                                        .sorted { $0.name.localizedStandardCompare($1.name) == .orderedAscending }
                                    ) { game in
                                        GameCard(
                                            game: game,
                                            isSelected: selectedGame?.id == game.id,
                                            action: {
                                                selectedGame = game
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                    .padding()
                }
            }
            
            Spacer()
            
            // Credit Information (only show for teachers)
            if !gameRoomManager.isLoading && gameRoomManager.error == nil && userManager.currentUser.isTeacher {
                VStack(spacing: 12) {
                    creditBadge
                }
            }

            // Launch Button
            if !gameRoomManager.isLoading && gameRoomManager.error == nil {
                VStack(spacing: 12) {
                    Button(action: launchGame) {
                        HStack {
                            if isCreatingRoom {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Text("Launch Game Room")
                                    .font(.headline)
                            }
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            gameRoomManager.selectedTopic != nil && selectedGame != nil && !isCreatingRoom ?
                            Color.blue :
                            Color.gray
                        )
                        .cornerRadius(10)
                    }
                    .disabled(gameRoomManager.selectedTopic == nil || selectedGame == nil || isCreatingRoom)
                }
                .padding()
            }
        }
    }
    
    private func launchGame() {
        guard let topic = gameRoomManager.selectedTopic,
              let game = selectedGame,
              let grade = gameRoomManager.selectedGrade else {
            print("GameRoomCreationView: Missing required data for launch")
            return
        }

        print("GameRoomCreationView: Starting launchGame")
        print("GameRoomCreationView: Topic: \(topic.name)")
        print("GameRoomCreationView: Game: \(game.name)")
        print("GameRoomCreationView: Grade: \(grade.displayName)")

        // Set loading state
        isCreatingRoom = true

        // Use the async WebSocket version instead of local creation
        Task {
            do {
                print("GameRoomCreationView: Attempting WebSocket creation...")
                try await gameRoomManager.createGameRoom(selectedGame: game)
                print("GameRoomCreationView: WebSocket creation successful")
                await MainActor.run {
                    print("GameRoomCreationView: Setting showingGameRoom to true")
                    showingGameRoom = true
                    isCreatingRoom = false
                }
            } catch {
                print("GameRoomCreationView: WebSocket creation failed: \(error)")

                await MainActor.run {
                    // Show error alert to user
                    alertMessage = "Failed to create game room: \(error.localizedDescription). Using offline mode."
                    showingAlert = true
                    isCreatingRoom = false

                    // Fallback: create a local game room if WebSocket fails
                    print("GameRoomCreationView: Creating fallback game room")
                    let fallbackGameRoom = GameRoom(
                        id: UUID().uuidString,
                        pinCode: GameRoom.generatePinCode(),
                        hostId: userManager.currentUser.id,
                        hostName: userManager.currentUser.username,
                        grade: grade,
                        topic: topic,
                        game: game,
                        createdAt: Date(),
                        status: .waiting,
                        players: [
                            Player(id: userManager.currentUser.id, username: userManager.currentUser.username, role: .host, joinedAt: Date())
                        ],
                        isGameStarted: false,
                        chargedStudentIds: Set<String>()
                    )
                    print("GameRoomCreationView: Fallback game room created with ID: \(fallbackGameRoom.id)")
                    gameRoomManager.currentGameRoom = fallbackGameRoom
                    print("GameRoomCreationView: Setting showingGameRoom to true for fallback")
                    showingGameRoom = true
                }
            }
        }
    }

    // MARK: - Credit Deduction

    /// Deduct credits when students join the game room (10 credits per student, excluding teacher)
    /// - Parameter studentCount: Number of students that have joined (excluding teacher)
    private func deductCreditsForGameRoom(studentCount: Int) async {
        guard studentCount > 0 else {
            print("GameRoomCreationView: No students to deduct credits for")
            return
        }

        let creditsPerStudent = 10
        let totalCreditsToDeduct = studentCount * creditsPerStudent

        print("GameRoomCreationView: Deducting \(totalCreditsToDeduct) credits for \(studentCount) students")

        await deductCredits(
            amount: totalCreditsToDeduct,
            description: "Game room creation - \(studentCount) student(s) joined"
        )
    }

    /// Deduct credits from teacher's account
    /// - Parameters:
    ///   - amount: Number of credits to deduct
    ///   - description: Description for the deduction
    private func deductCredits(amount: Int, description: String) async {
        // First, try to refresh the user profile to get a fresh token
        print("GameRoomCreationView: Refreshing user profile before credit deduction...")
        let profileRefreshSuccess = await userManager.fetchUserProfile()

        if !profileRefreshSuccess {
            print("GameRoomCreationView: ❌ Failed to refresh user profile, trying with existing token")
        }

        guard let authHeader = userManager.getAuthorizationHeader() else {
            print("GameRoomCreationView: ❌ No authorization header available for credit deduction")
            return
        }

        guard let url = URL(string: APIConfig.creditDeductionEndpoint) else {
            print("GameRoomCreationView: ❌ Invalid credit deduction URL")
            return
        }

        let request = CreditDeductionRequest(
            amount: amount,
            description: description
        )

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
            print("GameRoomCreationView: Making credit deduction request: \(amount) credits")

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            // Check for HTTP errors
            if let httpResponse = response as? HTTPURLResponse {
                print("GameRoomCreationView: Credit deduction HTTP Status: \(httpResponse.statusCode)")

                if httpResponse.statusCode == 401 {
                    print("GameRoomCreationView: Credit deduction failed - authentication error")
                    return
                } else if httpResponse.statusCode >= 400 {
                    print("GameRoomCreationView: Credit deduction failed - server error: \(httpResponse.statusCode)")
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("GameRoomCreationView: Error response: \(responseString)")
                    }
                    return
                }
            }

            // Parse the response
            let deductionResponse = try JSONDecoder().decode(CreditDeductionResponse.self, from: data)

            if deductionResponse.success {
                print("GameRoomCreationView: ✅ Successfully deducted \(amount) credits. Remaining balance: \(deductionResponse.newBalance ?? 0)")
                if let message = deductionResponse.message {
                    print("GameRoomCreationView: Server message: \(message)")
                }

                // Update the user's credit in UserManager if we have the new balance
                if let newBalance = deductionResponse.newBalance {
                    await MainActor.run {
                        userManager.updateUserCredit(newBalance)
                        print("GameRoomCreationView: Updated user credit in UserManager: \(newBalance)")

                        // Post notification that credit was updated
                        NotificationCenter.default.post(
                            name: NSNotification.Name("UserCreditUpdated"),
                            object: nil,
                            userInfo: ["newCredit": newBalance]
                        )
                    }
                }
            } else {
                print("GameRoomCreationView: ❌ Credit deduction failed: \(deductionResponse.message ?? "Unknown error")")
            }
        } catch {
            print("GameRoomCreationView: ❌ Credit deduction error: \(error)")
        }
    }
    
    // Credit charging information for game launch
    private var creditBadge: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "info.circle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
                Text("Credit Usage")
                    .font(.headline)
                    .foregroundColor(.blue)
                Spacer()
                Image(systemName: "creditcard.fill")
                .font(.system(size: 18))
                .foregroundColor(.green)
            
                VStack(alignment: .leading, spacing: 2) {
                    Text("\(userManager.currentUser.credit)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    
                    Text("credits remaining")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .textCase(.uppercase)
                }
                }

            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Image(systemName: "person.2.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.orange)
                    Text("10 credits per student that joins")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    Spacer()
                }

                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.green)
                    Text("No charge for teachers")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    Spacer()
                }

                HStack {
                    Image(systemName: "clock.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.blue)
                    Text("Charged when students join the room")
                        .font(.subheadline)
                        .foregroundColor(.primary)
                    Spacer()
                }
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.blue.opacity(0.05))
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color.blue.opacity(0.3), lineWidth: 1)
        )
        .padding(.horizontal)
    }

    private func checkWebSocketStatus() async {
        let isReachable = await GameService.shared.checkWebSocketServerReachability()
        await MainActor.run {
            webSocketStatus = isReachable ? "Connected" : "Disconnected"
        }
    }
}

struct TopicCard: View {
    let topic: Topic
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Text(topic.name)
                    .font(.headline)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                // Spacer(minLength: 0)
                
                Text(topic.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .padding()
            .frame(width: 200, alignment: .leading)
            .frame(minHeight: 120)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct GameCard: View {
    let game: Game
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                HStack(alignment: .center, spacing: 8) {
                    Image(systemName: gameIcon(for: game.type))
                        .font(.title2)
                        .foregroundColor(.blue)
                    
                    Text(game.name)
                        .font(.headline)
                        .multilineTextAlignment(.leading)
                    
                    Spacer() // Push content to the left
                }
                
                Text(game.description)
                    .font(.caption)
                    .foregroundColor(.gray)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack {
                    Text(game.type.rawValue)
                        .font(.caption2)
                        .fontWeight(.semibold)
                        .foregroundColor(gameTypeColor(for: game.type))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(gameTypeBackgroundColor(for: game.type))
                        .cornerRadius(6)
                    
                    Spacer() // Push the badge to the left
                }
            }
            .padding()
            .frame(width: 250, alignment: .leading)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(isSelected ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func gameIcon(for type: GameType) -> String {
        print(type)
        switch type {
        case .scratch: 
            return "gamecontroller.fill"  // More specific icon for Scratch programming
        case .geogebra: 
            return "hammer.fill"  // Mathematical function icon for GeoGebra
        case .native: 
            return "gamecontroller.fill"  // Filled game controller for native games
        case .quiz: 
            return "questionmark.circle.fill"  // Filled question mark for quizzes
        }
    }
    
    private func gameTypeColor(for type: GameType) -> Color {
        switch type {
            case .scratch:
                return .blue
            case .geogebra:
                return .green
            case .native:
                return .purple
            case .quiz:
                return .red
        }
    }
    
    private func gameTypeBackgroundColor(for type: GameType) -> Color {
        switch type {
        case .scratch:
            return .blue.opacity(0.1)
        case .geogebra:
            return .green.opacity(0.1)
        case .native:
            return .purple.opacity(0.1)
        case .quiz:
            return .red.opacity(0.1)
        }
    }
}

struct GameRoomView: View {
    @StateObject private var gameRoomManager = GameRoomManager.shared
    @StateObject private var userManager = UserManager.shared
    @Environment(\.dismiss) private var dismiss

    @State private var showingGamePlayer = false
    @State private var gameURL: String = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingDismissConfirmation = false
    @State private var showingCloseRoomConfirmation = false
    @State private var lastStudentCount = 0  // Track the last student count to detect new joins
    @State private var isProcessingCreditDeduction = false  // Prevent feedback loops in credit deduction
    
    // Use the current game room from GameRoomManager for real-time updates
    private var gameRoom: GameRoom? {
        return gameRoomManager.currentGameRoom
    }
    
    // Check if current user is the host
    private var isCurrentUserHost: Bool {
        guard let gameRoom = gameRoom else { return false }
        let isHost = gameRoom.hostId == userManager.currentUser.id
        print("GameRoomView: Checking if current user is host")
        print("GameRoomView: gameRoom.hostId = \(gameRoom.hostId)")
        print("GameRoomView: userManager.currentUser.id = \(userManager.currentUser.id)")
        print("GameRoomView: isCurrentUserHost = \(isHost)")
        return isHost
    }

    // Credit usage badge for active game room
    private var creditBadge: some View {
        Button(action: {
            // Refresh credit when tapped
            Task {
                await userManager.fetchUserProfile()
            }
        }) {
            HStack(spacing: 6) {
                Image(systemName: "creditcard.fill")
                    .font(.system(size: 14))
                    .foregroundColor(.orange)

                if let gameRoom = gameRoom {
                    let currentStudentCount = gameRoom.players.filter { player in
                        player.role != .host && player.id != userManager.currentUser.id
                    }.count

                    // Use the GameRoomManager's separate charged students tracking for more reliable data
                    let totalChargedStudents = gameRoomManager.getChargedStudentCount()
                    let creditsDeducted = totalChargedStudents * 10

                    // Debug logging (will be printed to console, not in UI)
                    let _ = print("GameRoomView: UI Update - GameRoom ID: \(gameRoom.id)")
                    let _ = print("GameRoomView: UI Update - Current students: \(currentStudentCount)")
                    let _ = print("GameRoomView: UI Update - Charged students from Manager: \(gameRoomManager.getChargedStudentIds())")
                    let _ = print("GameRoomView: UI Update - Total charged: \(totalChargedStudents), Credits deducted: \(creditsDeducted)")

                    VStack(alignment: .leading, spacing: 2) {
                        if totalChargedStudents > 0 {
                            Text("\(creditsDeducted) credits used")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.red)
                            Text("\(totalChargedStudents) student(s) charged")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        } else {
                            Text("No charges yet")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                            Text("0 students charged")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }

                    Spacer()

                } else {
                    Text("Loading...")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }

                // Show remaining credits badge
                remainingCreditsBadge

                // Image(systemName: "arrow.clockwise")
                //     .font(.system(size: 10))
                //     .foregroundColor(.gray)
            }
            .padding(.horizontal, 10)
            .padding(.vertical, 6)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.orange.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.orange.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .id("creditBadge-\(gameRoomManager.getChargedStudentCount())")  // Force refresh when charged students change
    }

    // Remaining credits badge for active game room
    private var remainingCreditsBadge: some View {
        Button(action: {
            // Refresh credit when tapped
            Task {
                await userManager.fetchUserProfile()
            }
        }) {
            VStack(alignment: .center, spacing: 4) {
                HStack(spacing: 4) {
                    Image(systemName: "creditcard.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.green)
                    Text("\(userManager.currentUser.credit)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                }

                Text("credits left")
                    .font(.system(size: 9))
                    .foregroundColor(.secondary)
                    .textCase(.uppercase)
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.green.opacity(0.1))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.green.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }


    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // Header
                HStack {
                    Text("Game Room")
                        .font(.title)
                        .fontWeight(.bold)
                    Spacer()
                    Button(action: {
                        if let gameRoom = gameRoom {
                            if gameRoom.status == .active {
                                showingDismissConfirmation = true
                            } else if gameRoom.status == .waiting && gameRoom.players.count > 1 {
                                // Show confirmation when students are in waiting room
                                showingCloseRoomConfirmation = true
                            } else {
                                dismiss()
                            }
                        } else {
                            dismiss()
                        }
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.title2)
                            .foregroundColor(.gray)
                    }
                }
                .padding()

                // Credit Badge (only show for teachers)
                if isCurrentUserHost && userManager.currentUser.isTeacher {
                    HStack {
                        Spacer()
                        creditBadge
                    }
                    .padding(.horizontal)
                }
            
            // Check if game room exists
            if let gameRoom = gameRoom {
                // Game Room Info
                VStack(spacing: 16) {
                    // Large PIN Code Display for Teacher
                    VStack(spacing: 12) {
                        Text("Game Room PIN")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text(gameRoom.pinCode)
                            .font(.system(size: 48, weight: .bold, design: .monospaced))
                            .foregroundColor(.blue)
                            .padding(.horizontal, 30)
                            .padding(.vertical, 20)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(16)
                            .overlay(
                                RoundedRectangle(cornerRadius: 16)
                                    .stroke(Color.blue, lineWidth: 2)
                            )
                    }
                    .padding(.vertical, 10)
                    
                    // Other info cards
                    InfoCard(title: "Grade", value: gameRoom.grade.rawValue)
                    InfoCard(title: "Topic", value: gameRoom.topic.name)
                    InfoCard(title: "Game", value: gameRoom.game.name)
                    InfoCard(title: "Status", value: gameRoom.isGameStarted ? "Game in Progress" : gameRoom.status.rawValue)
                }
                .padding()
                
                // Players List
                VStack(alignment: .leading, spacing: 8) {
                    Text("Players (\(gameRoom.players.count))")
                        .font(.headline)
                        .onAppear {
                            print("GameRoomView: Players list UI rendered with \(gameRoom.players.count) players")
                            for player in gameRoom.players {
                                print("GameRoomView: UI Player: \(player.username) (ID: \(player.id))")
                            }
                        }

                    ScrollView {
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible()),
                            GridItem(.flexible()),
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 12) {
                            ForEach(gameRoom.players) { player in
                                HStack(spacing: 8) {
                                    Image(systemName: player.role == .host ? "person.fill" : "person")
                                        .font(.body)
                                        .foregroundColor(.blue)
                                    Text(player.username)
                                        .font(.body)
                                        .lineLimit(1)
                                    if player.role == .host {
                                        Text("(Host)")
                                            .font(.caption)
                                            .foregroundColor(.gray)
                                    }
                                    Spacer()
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 10)
                                .frame(maxWidth: .infinity)
                                .background(Color(.systemBackground))
                                .cornerRadius(8)
                            }
                        }
                        .padding(.horizontal, 8)
                    }
                    .frame(maxHeight: 200) // Increased height to accommodate larger tabs
                }
                .padding()
                .id("playersList-\(gameRoom.players.count)-\(gameRoom.players.map { $0.id }.joined(separator: "-"))")

                // Control Buttons - Different for host vs player
                if isCurrentUserHost {
                    // Teacher/Host Controls
                    VStack(spacing: 16) {
                        if gameRoom.status == .waiting {
                            Button(action: { startGame() }) {
                                Text("Start Game")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(gameRoomManager.gameStarted ? Color.gray : Color.green)
                                    .cornerRadius(10)
                            }
                            .disabled(gameRoomManager.gameStarted)
                        } else if gameRoom.status == .active {
                            Button(action: { 
                                gameRoomManager.endGame()
                                dismiss() // Immediately dismiss for teacher
                            }) {
                                Text("End Game")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.orange)
                                    .cornerRadius(10)
                            }
                        } else if gameRoom.status == .completed {
                            VStack(spacing: 12) {
                                Text("Game Completed!")
                                    .font(.headline)
                                    .foregroundColor(.blue)
                                
                                Button(action: { 
                                    gameRoomManager.leaveGameRoom()
                                    dismiss()
                                }) {
                                    Text("Close Game Room")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(Color.blue)
                                        .cornerRadius(10)
                                }
                            }
                        } else if gameRoom.status == .cancelled {
                            VStack(spacing: 12) {
                                Text("Game Cancelled")
                                    .font(.headline)
                                    .foregroundColor(.red)
                                
                                Button(action: { 
                                    gameRoomManager.leaveGameRoom()
                                    dismiss()
                                }) {
                                    Text("Close Game Room")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(Color.red)
                                        .cornerRadius(10)
                                }
                            }
                        }
                    }
                    .padding()
                } else {
                    // Student/Player Controls
                    VStack(spacing: 16) {
                        if gameRoom.status == .waiting {
                            Text("Waiting for teacher to start the game...")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                        } else if gameRoom.status == .active {
                            VStack(spacing: 12) {
                                Text("Game is in progress!")
                                    .font(.headline)
                                    .foregroundColor(.green)
                                
                                if !gameURL.isEmpty {
                                    Button(action: {
                                        showingGamePlayer = true
                                    }) {
                                        HStack {
                                            Image(systemName: "play.circle.fill")
                                            Text("Play Game")
                                        }
                                        .font(.headline)
                                        .foregroundColor(.white)
                                        .frame(maxWidth: .infinity)
                                        .padding()
                                        .background(Color.green)
                                        .cornerRadius(10)
                                    }
                                } else {
                                    Text("No game URL available")
                                        .font(.subheadline)
                                        .foregroundColor(.gray)
                                        .multilineTextAlignment(.center)
                                }
                            }
                        } else if gameRoom.status == .completed {
                            Text("Game completed!")
                                .font(.headline)
                                .foregroundColor(.blue)
                        } else if gameRoom.status == .cancelled {
                            Text("Game was cancelled by the teacher")
                                .font(.headline)
                                .foregroundColor(.red)
                        }
                        
                        Button(action: { 
                            gameRoomManager.leaveGameRoom()
                            dismiss()
                        }) {
                            Text("Leave Game")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.orange)
                                .cornerRadius(10)
                        }
                    }
                    .padding()
                }
            } else {
                // Loading state when game room is nil
                VStack(spacing: 20) {
                    ProgressView()
                        .scaleEffect(1.5)
                    
                    Text("Loading game room...")
                        .font(.headline)
                        .foregroundColor(.gray)
                    
                    Text("If this takes too long, please check your connection and try again.")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
        }
        }
        .background(Color(.systemGroupedBackground))
        .fullScreenCover(isPresented: $showingGamePlayer) {
            GamePlayerView(
                gameURL: gameURL, 
                gameName: gameRoom?.game.name ?? "",
                onClose: {
                    // For teacher view, just close the game player
                    showingGamePlayer = false
                }
            )
        }
        .alert("Info", isPresented: $showingAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .confirmationDialog(
            "Game in Progress",
            isPresented: $showingDismissConfirmation,
            titleVisibility: .visible
        ) {
            Button("End Game and Leave", role: .destructive) {
                gameRoomManager.endGame()
                dismiss() // Immediately dismiss for teacher
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("A game is currently in progress. Ending the game will close the room for all players.")
        }
        .confirmationDialog(
            "Close Game Room",
            isPresented: $showingCloseRoomConfirmation,
            titleVisibility: .visible
        ) {
            Button("Close Room for All Players", role: .destructive) {
                gameRoomManager.cancelGame()
                dismiss() // Immediately dismiss for teacher
            }
            Button("Cancel", role: .cancel) { }
        } message: {
            Text("Students are currently in the waiting room. Closing the game room will remove all players and this action cannot be undone.")
        }
        .onAppear {
            // Get the game URL from the database
            getGameURL()
        }
        .onReceive(gameRoomManager.$currentGameRoom) { updatedGameRoom in
            print("GameRoomView: ===== GAME ROOM UPDATE RECEIVED =====")
            print("GameRoomView: Game Room ID: \(updatedGameRoom?.id ?? "nil")")
            print("GameRoomView: Players Count: \(updatedGameRoom?.players.count ?? 0)")
            if let gameRoom = updatedGameRoom {
                print("GameRoomView: Player Details:")
                for (index, player) in gameRoom.players.enumerated() {
                    print("GameRoomView:   \(index + 1). \(player.username) (ID: \(player.id), Role: \(player.role.rawValue))")
                }
                getGameURL()

                // Handle credit deduction for new students joining (only for teachers)
                if isCurrentUserHost && userManager.currentUser.isTeacher && !isProcessingCreditDeduction {
                    // Get current students (exclude teacher/host)
                    let currentStudents = gameRoom.players.filter { player in
                        player.role != .host && player.id != userManager.currentUser.id
                    }

                    let currentStudentIds = Set(currentStudents.map { $0.id })
                    let chargedStudentIds = gameRoomManager.getChargedStudentIds()

                    print("GameRoomView: ===== CREDIT DEDUCTION CHECK =====")
                    print("GameRoomView: Current students: \(currentStudents.map { "\($0.username)(\($0.id))" }.joined(separator: ", "))")
                    print("GameRoomView: Already charged student IDs: \(chargedStudentIds)")
                    print("GameRoomView: Current student IDs: \(currentStudentIds)")
                    print("GameRoomView: GameRoom charged students from model: \(gameRoom.chargedStudentIds)")

                    // Find new students who haven't been charged yet
                    let newStudentIds = currentStudentIds.subtracting(chargedStudentIds)
                    print("GameRoomView: New student IDs to charge: \(newStudentIds)")

                    if !newStudentIds.isEmpty {
                        let newStudentCount = newStudentIds.count
                        let newStudentNames = currentStudents.filter { newStudentIds.contains($0.id) }.map { $0.username }

                        print("GameRoomView: \(newStudentCount) new student(s) joined: \(newStudentNames.joined(separator: ", "))")
                        print("GameRoomView: Will deduct \(newStudentCount * 10) credits for new student(s)")

                        // Set flag to prevent feedback loops
                        isProcessingCreditDeduction = true

                        // Add new students to charged list in GameRoomManager (batch operation)
                        print("GameRoomView: Adding students to charged list: \(Array(newStudentIds))")
                        gameRoomManager.addChargedStudents(Array(newStudentIds))

                        // Verify the students were added
                        let verifyChargedCount = gameRoomManager.getChargedStudentCount()
                        print("GameRoomView: After adding students, charged count: \(verifyChargedCount)")
                        print("GameRoomView: GameRoom charged students: \(gameRoom.chargedStudentIds)")

                        // Deduct credits for new students (10 credits per student)
                        Task {
                            await deductCreditsForGameRoom(studentCount: newStudentCount)
                            // Reset flag after credit deduction is complete
                            await MainActor.run {
                                isProcessingCreditDeduction = false
                            }
                        }
                    }

                    // Update the last student count for display purposes
                    lastStudentCount = currentStudents.count
                }

                // Auto-dismiss when game is completed or cancelled for teacher
                if isCurrentUserHost {
                    if gameRoom.status == .completed || gameRoom.status == .cancelled {
                        // Small delay to allow UI to update before dismissing
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                            gameRoomManager.leaveGameRoom()
                            dismiss()
                        }
                    }
                }
            }
        }
        .onReceive(gameRoomManager.$players) { updatedPlayers in
            print("GameRoomView: Received players update - Count: \(updatedPlayers.count)")
        }
    }
    
    private func startGame() {
        guard let gameRoom = gameRoom else { return }
        
        print("GameRoomView: Start game button pressed")
        print("GameRoomView: Current game room status: \(gameRoom.status.rawValue)")
        print("GameRoomView: Is hosting: \(gameRoomManager.isHosting)")
        
        // Try WebSocket first, then fallback to local
        if gameRoomManager.isHosting {
            gameRoomManager.startGame()
        } else {
            // Local fallback: update the game room status
            print("GameRoomView: Using local fallback for start game")
            // Note: In a real implementation, you'd update the local game room status
            // For now, we'll just show an alert
            alertMessage = "Game started locally (WebSocket not connected)"
            showingAlert = true
        }
        getGameURL()
    }
    
    private func getGameURL() {
        guard let gameRoom = gameRoom else { return }

        // Use the actual game URL from the database
        if let gameURLFromDB = gameRoom.game.url, !gameURLFromDB.isEmpty {
            gameURL = gameURLFromDB
        } else {
            // Fallback: generate a URL based on game room if no URL in database
            let baseURL = "https://funma-game.com"
            let gamePath = "\(gameRoom.id)/\(gameRoom.game.id)"
            gameURL = "\(baseURL)/play/\(gamePath)"
        }
    }

    // MARK: - Credit Deduction (GameRoomView)

    /// Deduct credits when students join the game room (10 credits per student, excluding teacher)
    /// - Parameter studentCount: Number of students that have joined (excluding teacher)
    private func deductCreditsForGameRoom(studentCount: Int) async {
        guard studentCount > 0 else {
            print("GameRoomView: No students to deduct credits for")
            return
        }

        let creditsPerStudent = 10
        let totalCreditsToDeduct = studentCount * creditsPerStudent

        print("GameRoomView: Deducting \(totalCreditsToDeduct) credits for \(studentCount) students")

        await deductCredits(
            amount: totalCreditsToDeduct,
            description: "Game room - \(studentCount) student(s) joined"
        )
    }

    /// Deduct credits from teacher's account
    /// - Parameters:
    ///   - amount: Number of credits to deduct
    ///   - description: Description for the deduction
    private func deductCredits(amount: Int, description: String) async {
        // First, try to refresh the user profile to get a fresh token
        print("GameRoomView: Refreshing user profile before credit deduction...")
        let profileRefreshSuccess = await userManager.fetchUserProfile()

        if !profileRefreshSuccess {
            print("GameRoomView: ❌ Failed to refresh user profile, trying with existing token")
        }

        guard let authHeader = userManager.getAuthorizationHeader() else {
            print("GameRoomView: ❌ No authorization header available for credit deduction")
            return
        }

        guard let url = URL(string: APIConfig.creditDeductionEndpoint) else {
            print("GameRoomView: ❌ Invalid credit deduction URL")
            return
        }

        let request = CreditDeductionRequest(
            amount: amount,
            description: description
        )

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add authorization header
        for (key, value) in authHeader {
            urlRequest.setValue(value, forHTTPHeaderField: key)
        }

        do {
            urlRequest.httpBody = try JSONEncoder().encode(request)
            print("GameRoomView: Making credit deduction request: \(amount) credits")

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            // Check for HTTP errors
            if let httpResponse = response as? HTTPURLResponse {
                print("GameRoomView: Credit deduction HTTP Status: \(httpResponse.statusCode)")

                if httpResponse.statusCode == 401 {
                    print("GameRoomView: Credit deduction failed - authentication error")
                    return
                } else if httpResponse.statusCode >= 400 {
                    print("GameRoomView: Credit deduction failed - server error: \(httpResponse.statusCode)")
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("GameRoomView: Error response: \(responseString)")
                    }
                    return
                }
            }

            // Parse the response
            let deductionResponse = try JSONDecoder().decode(CreditDeductionResponse.self, from: data)

            if deductionResponse.success {
                print("GameRoomView: ✅ Successfully deducted \(amount) credits. Remaining balance: \(deductionResponse.newBalance ?? 0)")
                if let message = deductionResponse.message {
                    print("GameRoomView: Server message: \(message)")
                }

                // Update the user's credit in UserManager if we have the new balance
                if let newBalance = deductionResponse.newBalance {
                    await MainActor.run {
                        userManager.updateUserCredit(newBalance)
                        print("GameRoomView: Updated user credit in UserManager: \(newBalance)")

                        // Post notification that credit was updated
                        NotificationCenter.default.post(
                            name: NSNotification.Name("UserCreditUpdated"),
                            object: nil,
                            userInfo: ["newCredit": newBalance]
                        )
                    }
                }
            } else {
                print("GameRoomView: ❌ Credit deduction failed: \(deductionResponse.message ?? "Unknown error")")
            }
        } catch {
            print("GameRoomView: ❌ Credit deduction error: \(error)")
        }
    }
}

struct InfoCard: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.gray)
            Spacer()
            Text(value)
                .font(.headline)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

struct GameRoomCreationView_Previews: PreviewProvider {
    static var previews: some View {
        GameRoomCreationView()
    }
} 
