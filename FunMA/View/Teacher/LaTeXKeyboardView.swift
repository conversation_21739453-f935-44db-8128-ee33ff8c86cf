import SwiftUI

struct LaTeXKeyboardView: View {
    let onInsert: (String) -> Void
    @State private var selectedCategory: KeyboardCategory = .symbols
    
    enum KeyboardCategory: String, CaseIterable {
        case symbols = "Symbols"
        case fractions = "Fractions"
        case powers = "Powers"
        case functions = "Functions"
        case geometry = "Geometry"
        
        var icon: String {
            switch self {
            case .symbols: return "plus.forwardslash.minus"
            case .fractions: return "divide"
            case .powers: return "x.squareroot"
            case .functions: return "function"
            case .geometry: return "triangle"
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Category Tabs
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(KeyboardCategory.allCases, id: \.self) { category in
                        Button(action: {
                            selectedCategory = category
                        }) {
                            HStack(spacing: 4) {
                                Image(systemName: category.icon)
                                    .font(.caption)
                                Text(category.rawValue)
                                    .font(.caption)
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(
                                selectedCategory == category 
                                ? Color.blue 
                                : Color(.systemGray5)
                            )
                            .foregroundColor(
                                selectedCategory == category 
                                ? .white 
                                : .primary
                            )
                            .cornerRadius(8)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.horizontal, 16)
            }
            .padding(.vertical, 8)
            
            Divider()
            
            // Keyboard Content
            ScrollView {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(keysForCategory(selectedCategory), id: \.label) { key in
                        LaTeXKeyButton(
                            label: key.label,
                            latexCode: key.latexCode,
                            onTap: onInsert
                        )
                    }
                }
                .padding(16)
            }
        }
        .frame(height: 240)
        .background(Color(.systemGroupedBackground))
        .cornerRadius(12)
    }
    
    private func keysForCategory(_ category: KeyboardCategory) -> [LaTeXKey] {
        switch category {
        case .symbols:
            return [
                LaTeXKey(label: "±", latexCode: "\\pm"),
                LaTeXKey(label: "∞", latexCode: "\\infty"),
                LaTeXKey(label: "≤", latexCode: "\\leq"),
                LaTeXKey(label: "≥", latexCode: "\\geq"),
                LaTeXKey(label: "≠", latexCode: "\\neq"),
                LaTeXKey(label: "≈", latexCode: "\\approx"),
                LaTeXKey(label: "×", latexCode: "\\times"),
                LaTeXKey(label: "÷", latexCode: "\\div"),
                LaTeXKey(label: "∑", latexCode: "\\sum"),
                LaTeXKey(label: "∫", latexCode: "\\int"),
                LaTeXKey(label: "√", latexCode: "\\sqrt{}"),
                LaTeXKey(label: "∴", latexCode: "\\therefore"),
            ]
        case .fractions:
            return [
                LaTeXKey(label: "½", latexCode: "\\frac{1}{2}"),
                LaTeXKey(label: "⅓", latexCode: "\\frac{1}{3}"),
                LaTeXKey(label: "¼", latexCode: "\\frac{1}{4}"),
                LaTeXKey(label: "a/b", latexCode: "\\frac{a}{b}"),
                LaTeXKey(label: "x/y", latexCode: "\\frac{x}{y}"),
                LaTeXKey(label: "( )", latexCode: "\\left(\\right)"),
                LaTeXKey(label: "[ ]", latexCode: "\\left[\\right]"),
                LaTeXKey(label: "{ }", latexCode: "\\left\\{\\right\\}"),
                LaTeXKey(label: "|x|", latexCode: "\\left|x\\right|"),
                LaTeXKey(label: "⌊⌋", latexCode: "\\lfloor\\rfloor"),
                LaTeXKey(label: "⌈⌉", latexCode: "\\lceil\\rceil"),
                LaTeXKey(label: "√x", latexCode: "\\sqrt{x}"),
            ]
        case .powers:
            return [
                LaTeXKey(label: "x²", latexCode: "x^2"),
                LaTeXKey(label: "x³", latexCode: "x^3"),
                LaTeXKey(label: "xⁿ", latexCode: "x^n"),
                LaTeXKey(label: "x₁", latexCode: "x_1"),
                LaTeXKey(label: "xᵢ", latexCode: "x_i"),
                LaTeXKey(label: "xₙ", latexCode: "x_n"),
                LaTeXKey(label: "eˣ", latexCode: "e^x"),
                LaTeXKey(label: "10ˣ", latexCode: "10^x"),
                LaTeXKey(label: "²√", latexCode: "\\sqrt[2]{}"),
                LaTeXKey(label: "³√", latexCode: "\\sqrt[3]{}"),
                LaTeXKey(label: "ⁿ√", latexCode: "\\sqrt[n]{}"),
                LaTeXKey(label: "log", latexCode: "\\log"),
            ]
        case .functions:
            return [
                LaTeXKey(label: "sin", latexCode: "\\sin"),
                LaTeXKey(label: "cos", latexCode: "\\cos"),
                LaTeXKey(label: "tan", latexCode: "\\tan"),
                LaTeXKey(label: "cot", latexCode: "\\cot"),
                LaTeXKey(label: "sec", latexCode: "\\sec"),
                LaTeXKey(label: "csc", latexCode: "\\csc"),
                LaTeXKey(label: "ln", latexCode: "\\ln"),
                LaTeXKey(label: "log", latexCode: "\\log"),
                LaTeXKey(label: "lim", latexCode: "\\lim"),
                LaTeXKey(label: "max", latexCode: "\\max"),
                LaTeXKey(label: "min", latexCode: "\\min"),
                LaTeXKey(label: "f(x)", latexCode: "f(x)"),
            ]
        case .geometry:
            return [
                LaTeXKey(label: "°", latexCode: "^\\circ"),
                LaTeXKey(label: "∠", latexCode: "\\angle"),
                LaTeXKey(label: "△", latexCode: "\\triangle"),
                LaTeXKey(label: "□", latexCode: "\\square"),
                LaTeXKey(label: "⊥", latexCode: "\\perp"),
                LaTeXKey(label: "∥", latexCode: "\\parallel"),
                LaTeXKey(label: "π", latexCode: "\\pi"),
                LaTeXKey(label: "⊙", latexCode: "\\odot"),
                LaTeXKey(label: "∼", latexCode: "\\sim"),
                LaTeXKey(label: "≅", latexCode: "\\cong"),
                LaTeXKey(label: "⟂", latexCode: "\\perp"),
                LaTeXKey(label: "∆", latexCode: "\\Delta"),
            ]
        }
    }
}

struct LaTeXKey {
    let label: String
    let latexCode: String
}

struct LaTeXKeyButton: View {
    let label: String
    let latexCode: String
    let onTap: (String) -> Void
    
    var body: some View {
        Button(action: {
            onTap(latexCode)
        }) {
            Text(label)
                .font(.title3)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .frame(width: 40, height: 40)
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(0.95)
        .animation(.easeInOut(duration: 0.1), value: false)
    }
}

#Preview {
    LaTeXKeyboardView { latex in
        print("Inserted: \(latex)")
    }
    .padding()
} 