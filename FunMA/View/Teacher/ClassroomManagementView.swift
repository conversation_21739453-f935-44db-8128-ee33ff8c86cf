import SwiftUI

struct ClassroomManagementView: View {
    @StateObject private var viewModel = ClassroomViewModel()
    @StateObject private var userManager = UserManager.shared
    @State private var showingCreateClassroom = false
    @State private var showingClassroomDetails = false {
        didSet {
            print("🔄 ClassroomManagementView: showingClassroomDetails changed to \(showingClassroomDetails), selectedClassroom is '\(viewModel.selectedClassroom?.name ?? "nil")'")
        }
    }
    @State private var showingDeleteAlert = false
    @State private var classroomToDelete: Classroom?
    @State private var showingErrorAlert = false
    @State private var isRefreshing = false
    @State private var deleteConfirmationText = ""
    
    var body: some View {
        // NavigationView {
            VStack(spacing: 0) {
                // Header with create button
                HStack {
                    Text("My Classrooms")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button(action: {
                        showingCreateClassroom = true
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                            Text("Create Class")
                        }
                        .font(.headline)
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.blue)
                        .cornerRadius(10)
                    }
                }
                .padding()
                .background(Color(.systemBackground))
                
                Divider()
                
                // Content
                if viewModel.isLoading && viewModel.classrooms.isEmpty {
                    // Loading state
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                        Text("Loading classrooms...")
                            .foregroundColor(.gray)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if viewModel.classrooms.isEmpty {
                    // Empty state
                    EmptyClassroomsView {
                        showingCreateClassroom = true
                    }
                } else {
                    // Classroom list - responsive layout for mobile
                    ScrollView {
                        GeometryReader { geometry in
                            let isCompact = geometry.size.width < 600 // Mobile threshold
                            let columns = isCompact ?
                                [GridItem(.flexible())] :
                                [GridItem(.flexible()), GridItem(.flexible())]

                            LazyVGrid(columns: columns, spacing: 16) {
                                ForEach(viewModel.classrooms) { classroom in
                                    ClassroomCard(
                                        classroom: classroom,
                                        onTap: {
                                            print("🏫 ClassroomManagementView: Tapping classroom '\(classroom.name)' with ID: \(classroom.id)")
                                            viewModel.selectedClassroom = classroom
                                            showingClassroomDetails = true
                                            print("🏫 ClassroomManagementView: showingClassroomDetails set to true")
                                        },
                                        onDelete: {
                                            classroomToDelete = classroom
                                            showingDeleteAlert = true
                                        }
                                    )
                                }
                            }
                            .padding()
                        }
                    }
                }
            }
            .background(Color(.systemGroupedBackground))
            .fullScreenCover(isPresented: $showingCreateClassroom) {
                CreateClassroomView(viewModel: viewModel)
            }
            .fullScreenCover(isPresented: $showingClassroomDetails, onDismiss: {
                print("🏫 ClassroomManagementView: Sheet dismissed, clearing selectedClassroom")
                viewModel.selectedClassroom = nil
            }) {
                if let classroom = viewModel.selectedClassroom {
                    ClassroomDetailView(classroom: classroom, viewModel: viewModel)
                        .onAppear {
                            print("🏫 ClassroomManagementView: Presenting ClassroomDetailView for '\(classroom.name)'")
                        }
                } else {
                    Text("Error: No classroom selected")
                        .foregroundColor(.red)
                        .padding()
                        .onAppear {
                            print("🏫 ClassroomManagementView: ERROR - viewModel.selectedClassroom is nil when showing sheet")
                        }
                }
            }
            .sheet(isPresented: $showingDeleteAlert, onDismiss: {
                deleteConfirmationText = ""
                classroomToDelete = nil
            }) {
                DeleteClassroomConfirmationView(
                    classroom: classroomToDelete,
                    confirmationText: $deleteConfirmationText,
                    onConfirm: {
                        if let classroom = classroomToDelete {
                            Task {
                                let success = await viewModel.deleteClassroom(classroom)
                                await MainActor.run {
                                    if !success {
                                        // Only show error alert if delete failed
                                        if viewModel.error != nil {
                                            showingErrorAlert = true
                                        }
                                    }
                                    showingDeleteAlert = false
                                    deleteConfirmationText = ""
                                    classroomToDelete = nil
                                }
                            }
                        }
                    },
                    onCancel: {
                        showingDeleteAlert = false
                        deleteConfirmationText = ""
                        classroomToDelete = nil
                    }
                )
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") {
                    showingErrorAlert = false
                    viewModel.clearError()
                }
            } message: {
                if let error = viewModel.error {
                    Text(error)
                }
            }
            .onChange(of: viewModel.error) { error in
                if error != nil && !showingErrorAlert {
                    showingErrorAlert = true
                }
            }
            .task {
                print("📱 ClassroomManagementView: Initial load triggered")
                await viewModel.loadClassrooms()
                // Handle any errors that occurred during loading
                if viewModel.error != nil && !showingErrorAlert {
                    print("🚨 ClassroomManagementView: Error occurred during initial load: \(viewModel.error ?? "unknown")")
                    showingErrorAlert = true
                }
            }
            .refreshable {
                // Prevent multiple rapid refreshes
                guard !isRefreshing else {
                    print("🔄 ClassroomManagementView: Refresh already in progress, ignoring")
                    return
                }

                print("🔄 ClassroomManagementView: Pull-to-refresh triggered")
                isRefreshing = true

                // Clear any existing errors before refreshing
                viewModel.clearError()

                do {
                    await viewModel.loadClassrooms()

                    // Handle any errors that occurred during refresh
                    if viewModel.error != nil && !showingErrorAlert {
                        print("🚨 ClassroomManagementView: Error occurred during refresh: \(viewModel.error ?? "unknown")")
                        showingErrorAlert = true
                    }
                } catch {
                    print("🚨 ClassroomManagementView: Exception during refresh: \(error)")
                }

                isRefreshing = false
            }
            .onAppear {
                print("🏫 ClassroomManagementView: Appearing with \(viewModel.classrooms.count) classrooms")
            }
        // }
    }
}

// MARK: - Classroom Card Component
struct ClassroomCard: View {
    let classroom: Classroom
    let onTap: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(classroom.name)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    Text(classroom.grade.displayName)
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
                
                Spacer()
                
                Menu {
                    Button("View Details") {
                        onTap()
                    }
                    
                    Divider()
                    
                    Button("Delete", role: .destructive) {
                        onDelete()
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.gray)
                        .padding(8)
                        .background(Color(.systemGray6))
                        .clipShape(Circle())
                }
            }
            
            // Stats
            HStack(spacing: 16) {
                StatItem(
                    icon: "person.2.fill",
                    value: "\(classroom.students.count)",
                    label: "Students"
                )
                
                if let subject = classroom.subject {
                    StatItem(
                        icon: "book.fill",
                        value: subject,
                        label: "Subject"
                    )
                }
            }
            
            // Footer
            HStack {
                Text("Created \(formatDate(classroom.createdAt))")
                    .font(.caption)
                    .foregroundColor(.gray)
                
                Spacer()
                
                Text(classroom.schoolYear)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(4)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .onTapGesture {
            onTap()
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Stat Item Component
struct StatItem: View {
    let icon: String
    let value: String
    let label: String

    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.caption)

            Text("\(value) \(label)")
                .font(.caption)
                .fontWeight(.semibold)
                .lineLimit(1)
        }
    }
}

// MARK: - Empty State Component
struct EmptyClassroomsView: View {
    let onCreateClassroom: () -> Void
    
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "building.2")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Classrooms Yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("Create your first classroom to start managing students and assignments")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            Button(action: onCreateClassroom) {
                HStack {
                    Image(systemName: "plus")
                    Text("Create Your First Classroom")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.blue)
                .cornerRadius(10)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
}

// MARK: - Create Classroom View
struct CreateClassroomView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject var viewModel: ClassroomViewModel
    
    @State private var className = ""
    @State private var selectedGrade: Grade = .form1
    @State private var subject = ""
    @State private var schoolYear = ""
    @State private var isCreating = false
    
    var body: some View {
        NavigationView {
            Form {
                Section("Classroom Details") {
                    TextField("Class Name (e.g., 1A, 2B)", text: $className)
                        .autocapitalization(.allCharacters)
                    
                    Picker("Grade", selection: $selectedGrade) {
                        ForEach(Grade.allCases, id: \.self) { grade in
                            Text(grade.displayName).tag(grade)
                        }
                    }
                    
                    TextField("Subject (Optional)", text: $subject)
                        .autocapitalization(.words)
                    
                    TextField("School Year", text: $schoolYear)
                        .placeholder(when: schoolYear.isEmpty) {
                            Text(viewModel.getCurrentSchoolYear())
                                .foregroundColor(.gray)
                        }
                }
                
                Section {
                    Button(action: createClassroom) {
                        if isCreating {
                            HStack {
                                ProgressView()
                                    .scaleEffect(0.8)
                                Text("Creating...")
                            }
                        } else {
                            Text("Create Classroom")
                        }
                    }
                    .disabled(className.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isCreating)
                }
            }
            .navigationTitle("New Classroom")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                }
            )
        }
        .onAppear {
            schoolYear = viewModel.getCurrentSchoolYear()
        }
    }
    
    private func createClassroom() {
        let trimmedClassName = className.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedSubject = subject.trimmingCharacters(in: .whitespacesAndNewlines)
        let finalSchoolYear = schoolYear.isEmpty ? viewModel.getCurrentSchoolYear() : schoolYear
        
        isCreating = true
        
        Task {
            let success = await viewModel.createClassroom(
                name: trimmedClassName,
                grade: selectedGrade,
                subject: trimmedSubject.isEmpty ? nil : trimmedSubject,
                schoolYear: finalSchoolYear
            )
            
            await MainActor.run {
                isCreating = false
                if success {
                    dismiss()
                }
            }
        }
    }
}

// MARK: - Extension for Placeholder
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {

        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

// MARK: - Delete Confirmation View
struct DeleteClassroomConfirmationView: View {
    let classroom: Classroom?
    @Binding var confirmationText: String
    let onConfirm: () -> Void
    let onCancel: () -> Void

    private var isDeleteEnabled: Bool {
        confirmationText.lowercased() == "delete"
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Warning Icon
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.red)

                // Title and Description
                VStack(spacing: 12) {
                    Text("Delete Classroom")
                        .font(.title2)
                        .fontWeight(.bold)

                    if let classroom = classroom {
                        Text("You are about to permanently delete '\(classroom.name)'. This action cannot be undone and will remove all students from the classroom.")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                }

                // Confirmation Input
                VStack(alignment: .leading, spacing: 8) {
                    Text("To confirm deletion, type \"delete\" below:")
                        .font(.headline)
                        .foregroundColor(.primary)

                    TextField("Type 'delete' to confirm", text: $confirmationText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .autocapitalization(.none)
                        .disableAutocorrection(true)
                }
                .padding(.horizontal)

                Spacer()

                // Action Buttons
                VStack(spacing: 12) {
                    Button(action: onConfirm) {
                        Text("Delete Classroom")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(isDeleteEnabled ? Color.red : Color.gray)
                            .cornerRadius(10)
                    }
                    .disabled(!isDeleteEnabled)

                    Button(action: onCancel) {
                        Text("Cancel")
                            .font(.headline)
                            .foregroundColor(.blue)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color(.systemGray6))
                            .cornerRadius(10)
                    }
                }
                .padding(.horizontal)
            }
            .padding()
            .navigationTitle("Confirm Deletion")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Cancel") {
                    onCancel()
                }
            )
        }
    }
}

// MARK: - Preview
struct ClassroomManagementView_Previews: PreviewProvider {
    static var previews: some View {
        ClassroomManagementView()
    }
}