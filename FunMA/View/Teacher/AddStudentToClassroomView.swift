import SwiftUI

struct AddStudentToClassroomView: View {
    let classroom: Classroom
    @ObservedObject var viewModel: ClassroomViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    @State private var showingSuccessAlert = false
    @State private var successMessage = ""
    @State private var showingErrorAlert = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Header
                headerSection
                
                Divider()
                
                // Search Section
                searchSection
                
                // Results Section
                resultsSection
            }
            .navigationTitle("Add Students")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: But<PERSON>("Cancel") {
                    dismiss()
                }
            )
        }
        .alert("Success", isPresented: $showingSuccessAlert) {
            Button("OK") { }
        } message: {
            Text(successMessage)
        }
        .alert("Error", isPresented: $showingErrorAlert) {
            Button("OK") { }
        } message: {
            if let error = viewModel.studentSearchError {
                Text(error)
            }
        }
        .onChange(of: viewModel.studentSearchError) { error in
            if error != nil {
                showingErrorAlert = true
            }
        }
        .task {
            // Auto-search when view appears
            if let schoolId = viewModel.getTeacherSchoolId() {
                await searchStudents(schoolId: schoolId)
            } else {
                print("⚠️ AddStudentToClassroomView: Teacher schoolId not available - cannot search students")
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Add to \(classroom.name)")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("\(classroom.grade.displayName) • \(classroom.schoolYear)")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                
                Spacer()
            }
            
            HStack {
                Image(systemName: "person.2.badge.plus")
                    .foregroundColor(.blue)
                
                Text("Students from your school who aren't in this classroom yet")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    // MARK: - Search Section
    
    private var searchSection: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)
                
                TextField("Search by name or username...", text: $searchText)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onSubmit {
                        if let schoolId = viewModel.getTeacherSchoolId() {
                            Task {
                                await searchStudents(schoolId: schoolId)
                            }
                        } else {
                            print("⚠️ AddStudentToClassroomView: Cannot search - teacher schoolId not available")
                        }
                    }
                
                if !searchText.isEmpty {
                    Button("Clear") {
                        searchText = ""
                        if let schoolId = viewModel.getTeacherSchoolId() {
                            Task {
                                await searchStudents(schoolId: schoolId)
                            }
                        } else {
                            print("⚠️ AddStudentToClassroomView: Cannot search - teacher schoolId not available")
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            
            Button("Search") {
                if let schoolId = viewModel.getTeacherSchoolId() {
                    Task {
                        await searchStudents(schoolId: schoolId)
                    }
                } else {
                    print("⚠️ AddStudentToClassroomView: Cannot search - teacher schoolId not available")
                }
            }
            .disabled(viewModel.isSearchingStudents || viewModel.getTeacherSchoolId() == nil)
            .buttonStyle(.bordered)
        }
        .padding()
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Results Section
    
    private var resultsSection: some View {
        Group {
            if viewModel.isSearchingStudents {
                loadingView
            } else if viewModel.availableStudents.isEmpty {
                emptyStateView
            } else {
                studentsListView
            }
        }
    }
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Searching students...")
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "person.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Available Students")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text(getEmptyStateMessage())
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private var studentsListView: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(viewModel.availableStudents) { student in
                    StudentSearchCard(
                        student: student,
                        onAdd: {
                            Task {
                                let success = await viewModel.addStudentToClassroom(student, to: classroom)
                                await MainActor.run {
                                    if success {
                                        successMessage = "Successfully added \(student.name) to \(classroom.name)"
                                        showingSuccessAlert = true
                                    }
                                }
                            }
                        }
                    )
                }
            }
            .padding()
        }
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Helper Methods
    
    private func searchStudents(schoolId: String) async {
        let searchTerm = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        await viewModel.searchStudentsFromSameSchool(
            schoolId: schoolId,
            searchTerm: searchTerm.isEmpty ? nil : searchTerm,
            excludeClassroomId: classroom.id
        )
    }
    
    private func getEmptyStateMessage() -> String {
        if viewModel.getTeacherSchoolId() == nil {
            return "School information not available. Please contact your administrator."
        } else if searchText.isEmpty {
            return "All students from your school are already in this classroom"
        } else {
            return "No students found matching '\(searchText)'"
        }
    }
}

// MARK: - Student Search Card Component
struct StudentSearchCard: View {
    let student: StudentSearchResult
    let onAdd: () -> Void
    
    @State private var isAdding = false
    
    var body: some View {
        HStack(spacing: 12) {
            // Avatar
            Circle()
                .fill(Color.blue.opacity(0.2))
                .frame(width: 50, height: 50)
                .overlay(
                    Text(student.name.prefix(1).uppercased())
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)
                )
            
            // Student Info
            VStack(alignment: .leading, spacing: 4) {
                Text(student.name)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Text("@\(student.username)")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                
                if let grade = student.grade {
                    Text(grade.displayName)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                }
            }
            
            Spacer()
            
            // Add Button
            Button(action: {
                isAdding = true
                onAdd()
                // Note: isAdding will be reset when the student is removed from the list
            }) {
                if isAdding {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Adding...")
                    }
                } else {
                    HStack {
                        Image(systemName: "plus")
                        Text("Add")
                    }
                }
            }
            .disabled(isAdding || student.isAlreadyInClassroom)
            .buttonStyle(.bordered)
            .foregroundColor(.blue)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .onChange(of: student.isAlreadyInClassroom) { _ in
            isAdding = false
        }
    }
}

// MARK: - Preview
struct AddStudentToClassroomView_Previews: PreviewProvider {
    static var previews: some View {
        AddStudentToClassroomView(
            classroom: Classroom(
                name: "1A",
                grade: .form1,
                teacherId: "teacher1",
                teacherName: "Ms. Smith",
                schoolYear: "2024-2025"
            ),
            viewModel: ClassroomViewModel()
        )
    }
} 