import SwiftUI

// MARK: - Teacher Dashboard Statistics Model
struct TeacherDashboardStats {
    let totalStudents: Int
    let activeStudents: Int
    let activeCourses: Int
    let completedExercises: Int
    let pendingExercises: Int
    var activeGames: Int
    var liveExercises: Int
    
    // New features
    var pendingGrading: [PendingGradingItem]
    var overdueAlerts: [OverdueAlert]
    var performanceData: PerformanceAnalytics
    var riskIndicators: [StudentRiskIndicator]
}

// MARK: - New Feature Models
struct PendingGradingItem: Identifiable {
    let id = UUID()
    let submissionId: UUID
    let studentName: String
    let exerciseTitle: String
    let submittedAt: Date
    let needsManualGrading: Bool
}

struct OverdueAlert: Identifiable {
    let id = UUID()
    let exerciseTitle: String
    let dueDate: Date
    let overdueBy: TimeInterval
    let studentsAffected: Int
    let classroomName: String
}

struct PerformanceAnalytics {
    let averageScore: Double
    let completionRate: Double
    let scoresTrend: [Double] // Last 7 days
    let topicPerformance: [TopicPerformance]
}

struct TopicPerformance: Identifiable {
    let id = UUID()
    let topic: String
    let averageScore: Double
    let completionCount: Int
}

struct StudentRiskIndicator: Identifiable {
    let id = UUID()
    let studentId: String // Add student ID to uniquely identify students
    let studentName: String
    let riskLevel: RiskLevel
    let reasons: [String]
    let lastActivity: Date
    let averageScore: Double
}

enum RiskLevel: String, CaseIterable {
    case high = "High"
    case medium = "Medium" 
    case low = "Low"
    
    var color: Color {
        switch self {
        case .high: return .red
        case .medium: return .orange
        case .low: return .yellow
        }
    }
}

struct TeacherDashboardView: View {
    @StateObject private var userManager = UserManager.shared
    @StateObject private var classroomViewModel = ClassroomViewModel()
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    @StateObject private var gameRoomManager = GameRoomManager.shared
    @State private var selectedTab: TeacherTab = .overview
    @State private var dashboardStats = TeacherDashboardStats(
        totalStudents: 0,
        activeStudents: 0,
        activeCourses: 0,
        completedExercises: 0,
        pendingExercises: 0,
        activeGames: 0,
        liveExercises: 0,
        pendingGrading: [],
        overdueAlerts: [],
        performanceData: PerformanceAnalytics(averageScore: 0.0, completionRate: 0.0, scoresTrend: [], topicPerformance: []),
        riskIndicators: []
    )
    @State private var isLoadingStats = false
    @State private var isLoadingBasicStats = false
    @State private var isLoadingAdvancedStats = false
    @State private var lastRefreshTime: Date?
    
    // Cache for performance
    private static var cachedStats: TeacherDashboardStats?
    private static var cacheTimestamp: Date?
    private static let cacheValidityDuration: TimeInterval = 300 // 5 minutes
    
    enum TeacherTab {
        case overview
    }

    var body: some View {
        GeometryReader { geometry in
            let isCompact = geometry.size.width < 600 // Mobile threshold

            VStack(spacing: 0) {
                // Header - responsive sizing
                HStack {
                    Text("Teacher Dashboard")
                        .font(isCompact ? .title2 : .title)
                        .fontWeight(.bold)
                    Spacer()

                    // Refresh button
                    Button(action: {
                        Task {
                            await forceRefreshDashboardStats()
                        }
                    }) {
                        if isLoadingStats {
                            ProgressView()
                                .scaleEffect(0.6)
                                .progressViewStyle(CircularProgressViewStyle(tint: .blue))
                        } else {
                            Image(systemName: "arrow.clockwise")
                                .font(isCompact ? .body : .title3)
                                .foregroundColor(.blue)
                        }
                    }
                    .disabled(isLoadingStats)
                }
                .padding(isCompact ? 12 : 16)
                .background(Color(UIColor.systemBackground))
            
                // Tab selector - responsive sizing
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: isCompact ? 12 : 20) {
                        ForEach([TeacherTab.overview], id: \.self) { tab in
                            Button(action: {
                                selectedTab = tab
                            }) {
                                Text(tabTitle(for: tab))
                                    .font(isCompact ? .subheadline : .body)
                                    .fontWeight(selectedTab == tab ? .semibold : .regular)
                                    .foregroundColor(selectedTab == tab ? .blue : .gray)
                                    .padding(.vertical, isCompact ? 6 : 8)
                                    .padding(.horizontal, isCompact ? 12 : 16)
                                    .background(
                                        selectedTab == tab ?
                                        Color.blue.opacity(0.1) :
                                        Color.clear
                                    )
                                    .cornerRadius(8)
                            }
                        }
                    }
                    .padding(.horizontal, isCompact ? 12 : 16)
                }
                .padding(.vertical, isCompact ? 6 : 8)
                .background(Color(UIColor.systemBackground))

                // Content - responsive spacing
                ScrollView {
                    VStack(spacing: isCompact ? 12 : 20) {
                        switch selectedTab {
                        case .overview:
                            overviewContent(isCompact: isCompact)
                        }
                    }
                    .padding(isCompact ? 12 : 16)
                }
            }
            .background(Color(.systemGray6))
            .task {
                await loadDashboardStats()
            }
        }
    }


    
    private func overviewContent(isCompact: Bool) -> some View {
        VStack(spacing: isCompact ? 12 : 20) {
            // Quick stats - responsive sizing
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: isCompact ? 10 : 16) {
                DashboardStatCard(
                    title: "Total Students",
                    value: isLoadingStats ? "..." : "\(dashboardStats.totalStudents)",
                    icon: "person.3.fill",
                    isCompact: isCompact
                )
                DashboardStatCard(
                    title: "Live Exercises",
                    value: isLoadingStats ? "..." : "\(dashboardStats.liveExercises)",
                    icon: "checkmark.circle.fill",
                    isCompact: isCompact
                )
            }

            // Quick Action Stats - responsive sizing
            if isLoadingAdvancedStats {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: isCompact ? 10 : 16) {
                    LoadingStatCard(
                        title: "Pending Grading",
                        icon: "pencil.circle.fill",
                        color: .orange,
                        isCompact: isCompact
                    )
                    LoadingStatCard(
                        title: "Overdue",
                        icon: "exclamationmark.triangle.fill",
                        color: .red,
                        isCompact: isCompact
                    )
                }
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: isCompact ? 10 : 16) {
                    AlertStatCard(
                        title: "Pending Grading",
                        count: dashboardStats.pendingGrading.count,
                        icon: "pencil.circle.fill",
                        color: .orange,
                        isCompact: isCompact
                    )
                    AlertStatCard(
                        title: "Overdue",
                        count: dashboardStats.overdueAlerts.count,
                        icon: "exclamationmark.triangle.fill",
                        color: .red,
                        isCompact: isCompact
                    )
                }
            }

            // Pending Grading Queue
            if !dashboardStats.pendingGrading.isEmpty {
                PendingGradingSection(items: dashboardStats.pendingGrading, isCompact: isCompact)
            }

            // Overdue Alerts
            if !dashboardStats.overdueAlerts.isEmpty {
                OverdueAlertsSection(alerts: dashboardStats.overdueAlerts, isCompact: isCompact)
            }

            // Performance Analytics
            if isLoadingAdvancedStats {
                LoadingPerformanceSection(isCompact: isCompact)
            } else {
                PerformanceAnalyticsSection(analytics: dashboardStats.performanceData, isCompact: isCompact)
            }

            // Student Risk Indicators
            if !dashboardStats.riskIndicators.isEmpty {
                StudentRiskSection(indicators: dashboardStats.riskIndicators, isCompact: isCompact)
            }
        }
    }
    
    private func loadDashboardStats() async {
        // Check cache first
        if let cachedStats = Self.cachedStats,
           let cacheTimestamp = Self.cacheTimestamp,
           Date().timeIntervalSince(cacheTimestamp) < Self.cacheValidityDuration {
            await MainActor.run {
                dashboardStats = cachedStats
                lastRefreshTime = cacheTimestamp
            }
            print("📊 TeacherDashboardView: Using cached dashboard stats")
            return
        }

        await MainActor.run {
            isLoadingStats = true
            isLoadingBasicStats = true
        }

        // Load basic stats first (fast)
        await loadBasicStats()

        // Then load advanced analytics (slower)
        await loadAdvancedStats()

        await MainActor.run {
            isLoadingStats = false
            lastRefreshTime = Date()
        }
    }

    private func loadBasicStats() async {
        print("📊 TeacherDashboardView: Loading basic stats...")

        // Load classrooms if not already loaded
        if classroomViewModel.classrooms.isEmpty {
            await classroomViewModel.loadClassrooms()
        }

        // Calculate basic stats from already loaded classroom data
        let totalStudents = classroomViewModel.classrooms.reduce(0) { $0 + $1.students.count }
        let activeStudents = classroomViewModel.classrooms.reduce(0) { total, classroom in
            total + classroom.students.filter { $0.status == .active }.count
        }
        let activeCourses = max(Set(classroomViewModel.classrooms.compactMap { $0.subject }).count, classroomViewModel.classrooms.count)

        // Get game statistics (quick)
        let activeGames = gameRoomManager.currentGameRoom?.status == .active ? 1 : 0

        // Update UI with basic stats immediately
        await MainActor.run {
            dashboardStats = TeacherDashboardStats(
                totalStudents: totalStudents,
                activeStudents: activeStudents,
                activeCourses: activeCourses,
                completedExercises: dashboardStats.completedExercises,
                pendingExercises: dashboardStats.pendingExercises,
                activeGames: activeGames,
                liveExercises: dashboardStats.liveExercises,
                pendingGrading: dashboardStats.pendingGrading,
                overdueAlerts: dashboardStats.overdueAlerts,
                performanceData: dashboardStats.performanceData,
                riskIndicators: dashboardStats.riskIndicators
            )
            isLoadingBasicStats = false
        }

        print("📊 TeacherDashboardView: Basic stats loaded - Students: \(totalStudents), Active: \(activeStudents)")
    }
    
    private func loadAdvancedStats() async {
        print("📊 TeacherDashboardView: Loading advanced stats...")

        await MainActor.run {
            isLoadingAdvancedStats = true
        }

        // Create task groups for parallel loading
        async let liveExercisesTask = getLiveExercisesCount()
        async let pendingGradingTask = getPendingGradingItems()
        async let overdueAlertsTask = getOverdueAlerts()
        async let performanceDataTask = getPerformanceAnalytics()
        async let riskIndicatorsTask = getStudentRiskIndicators()

        // Await all results
        let liveExercises = await liveExercisesTask
        let pendingGrading = await pendingGradingTask
        let overdueAlerts = await overdueAlertsTask
        let performanceData = await performanceDataTask
        let riskIndicators = await riskIndicatorsTask

        // Update UI with advanced stats
        let updatedStats = TeacherDashboardStats(
            totalStudents: dashboardStats.totalStudents,
            activeStudents: dashboardStats.activeStudents,
            activeCourses: dashboardStats.activeCourses,
            completedExercises: performanceData.completedExercises,
            pendingExercises: performanceData.pendingExercises,
            activeGames: dashboardStats.activeGames,
            liveExercises: liveExercises,
            pendingGrading: pendingGrading,
            overdueAlerts: overdueAlerts,
            performanceData: performanceData.analytics,
            riskIndicators: riskIndicators
        )

        // Update cache
        Self.cachedStats = updatedStats
        Self.cacheTimestamp = Date()

        await MainActor.run {
            dashboardStats = updatedStats
            isLoadingAdvancedStats = false
        }

        print("📊 TeacherDashboardView: Advanced stats loaded")
        print("📊 Pending grading items: \(pendingGrading.count)")
        print("📊 Overdue alerts: \(overdueAlerts.count)")
        print("📊 At-risk students: \(riskIndicators.count)")
    }

    private func getLiveExercisesCount() async -> Int {
        do {
            var allExercises: [Exercise] = []

            // Load exercises for each classroom in parallel
            await withTaskGroup(of: [Exercise].self) { group in
                for classroom in classroomViewModel.classrooms {
                    group.addTask {
                        do {
                            let viewModel = ExerciseViewModel()
                            try await viewModel.getExercises(for: classroom.id)
                            return viewModel.exercises
                        } catch {
                            print("Error loading exercises for classroom \(classroom.id): \(error)")
                            return []
                        }
                    }
                }

                // Collect results
                for await result in group {
                    allExercises.append(contentsOf: result)
                }
            }

            // Count live exercises
            let now = Date()
            return allExercises.filter { exercise in
                exercise.dueDate > now && exercise.createdAt.timeIntervalSince(now) >= -86400
            }.count
        } catch {
            print("Error calculating live exercises: \(error)")
            return 0
        }
    }

    private func getPendingGradingItems() async -> [PendingGradingItem] {
        // Simplified version that delegates to ClassroomViewModel
        let items = await classroomViewModel.getPendingGradingItems()
        return items
    }

    private func getOverdueAlerts() async -> [OverdueAlert] {
        // Simplified version that delegates to ClassroomViewModel
        let alerts = await classroomViewModel.getOverdueAlerts()
        return alerts
    }

    private struct PerformanceResult {
        let analytics: PerformanceAnalytics
        let completedExercises: Int
        let pendingExercises: Int
    }

    private func getPerformanceAnalytics() async -> PerformanceResult {
        // Simplified version that delegates to ClassroomViewModel
        let analytics = await classroomViewModel.getPerformanceAnalytics()

        // Count completed and pending exercises
        var completed = 0
        var pending = 0

        for classroom in classroomViewModel.classrooms {
            do {
                let viewModel = ExerciseViewModel()
                try await viewModel.getExercises(for: classroom.id)

                let now = Date()
                for exercise in viewModel.exercises {
                    // Check if exercise is past due (completed) or still active (pending)
                    if exercise.dueDate < now {
                        completed += 1
                    } else {
                        pending += 1
                    }
                }
            } catch {
                print("Error counting exercises: \(error)")
            }
        }

        return PerformanceResult(
            analytics: analytics,
            completedExercises: completed,
            pendingExercises: pending
        )
    }

    private func getStudentRiskIndicators() async -> [StudentRiskIndicator] {
        // Simplified version that delegates to ClassroomViewModel
        let indicators = await classroomViewModel.getStudentRiskIndicators()
        return indicators
    }

    private func forceRefreshDashboardStats() async {
        // Clear cache to force refresh
        Self.cachedStats = nil
        Self.cacheTimestamp = nil

        // Reload stats
        await loadDashboardStats()
    }

    private func tabTitle(for tab: TeacherTab) -> String {
        switch tab {
        case .overview: return "Overview"
        }
    }
}

struct DashboardStatCard: View {
    let title: String
    let value: String
    let icon: String
    var subtitle: String? = nil
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 8 : 12) {
            HStack {
                Image(systemName: icon)
                    .font(isCompact ? .body : .title2)
                    .foregroundColor(.blue)
                Spacer()
            }

            Text(value)
                .font(isCompact ? .title2 : .title)
                .fontWeight(.bold)
                .lineLimit(1)

            VStack(alignment: .leading, spacing: isCompact ? 2 : 4) {
                Text(title)
                    .font(isCompact ? .caption : .subheadline)
                    .foregroundColor(.gray)
                    .lineLimit(1)

                if let subtitle = subtitle {
                    Text(subtitle)
                        .font(.caption2)
                        .foregroundColor(.gray.opacity(0.8))
                        .lineLimit(1)
                }
            }
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(.systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct LoadingActivityCard: View {
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(Color.gray.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    ProgressView()
                        .scaleEffect(0.6)
                )

            VStack(alignment: .leading, spacing: 4) {
                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 16)

                RoundedRectangle(cornerRadius: 4)
                    .fill(Color.gray.opacity(0.1))
                    .frame(height: 12)
                    .frame(maxWidth: 100)
            }

            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct LoadingStatCard: View {
    let title: String
    let icon: String
    let color: Color
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 8 : 12) {
            HStack {
                Image(systemName: icon)
                    .font(isCompact ? .body : .title2)
                    .foregroundColor(color)
                Spacer()
            }

            ProgressView()
                .scaleEffect(isCompact ? 0.6 : 0.8)
                .frame(height: isCompact ? 20 : 30)

            Text(title)
                .font(isCompact ? .caption : .subheadline)
                .foregroundColor(.gray)
                .lineLimit(1)
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct LoadingPerformanceSection: View {
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 10 : 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(isCompact ? .subheadline : .body)
                    .foregroundColor(.blue)
                Text("Performance Analytics")
                    .font(isCompact ? .subheadline : .headline)
                Spacer()
            }

            // Loading placeholders - responsive sizing
            HStack(spacing: isCompact ? 12 : 20) {
                VStack {
                    ProgressView()
                        .scaleEffect(isCompact ? 0.6 : 0.8)
                    Text("Avg Score")
                        .font(.caption2)
                        .foregroundColor(.gray)
                }

                VStack {
                    ProgressView()
                        .scaleEffect(isCompact ? 0.6 : 0.8)
                    Text("Completion")
                        .font(.caption2)
                        .foregroundColor(.gray)
                }

                Spacer()
            }

            Text("Loading analytics...")
                .font(isCompact ? .caption : .subheadline)
                .foregroundColor(.gray)
                .italic()
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct ActivityCard: View {
    var body: some View {
        HStack(spacing: 12) {
            Circle()
                .fill(Color.blue.opacity(0.1))
                .frame(width: 40, height: 40)
                .overlay(
                    Image(systemName: "person.fill")
                        .foregroundColor(.blue)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text("John Doe completed Math 101")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text("2 hours ago")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
    }
}

struct TeacherDashboardView_Previews: PreviewProvider {
    static var previews: some View {
        TeacherDashboardView()
    }
}

// MARK: - New Analytics UI Components

struct AlertStatCard: View {
    let title: String
    let count: Int
    let icon: String
    let color: Color
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 8 : 12) {
            HStack {
                Image(systemName: icon)
                    .font(isCompact ? .body : .title2)
                    .foregroundColor(color)
                Spacer()
            }

            Text("\(count)")
                .font(isCompact ? .title2 : .title)
                .fontWeight(.bold)
                .foregroundColor(color)
                .lineLimit(1)

            Text(title)
                .font(isCompact ? .caption : .subheadline)
                .foregroundColor(.gray)
                .lineLimit(1)
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct PendingGradingSection: View {
    let items: [PendingGradingItem]
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 8 : 12) {
            HStack {
                Image(systemName: "pencil.circle.fill")
                    .font(isCompact ? .subheadline : .body)
                    .foregroundColor(.orange)
                Text("Pending Grading")
                    .font(isCompact ? .subheadline : .headline)
                Spacer()
                Text("\(items.count)")
                    .font(.caption2)
                    .padding(.horizontal, isCompact ? 6 : 8)
                    .padding(.vertical, isCompact ? 2 : 4)
                    .background(Color.orange.opacity(0.2))
                    .cornerRadius(isCompact ? 6 : 8)
            }

            LazyVStack(spacing: isCompact ? 6 : 8) {
                ForEach(items.prefix(isCompact ? 3 : 5)) { item in
                    PendingGradingRow(item: item, isCompact: isCompact)
                }

                if items.count > (isCompact ? 3 : 5) {
                    HStack {
                        Text("and \(items.count - (isCompact ? 3 : 5)) more...")
                            .font(.caption2)
                            .foregroundColor(.gray)
                        Spacer()
                    }
                    .padding(.horizontal, isCompact ? 8 : 12)
                }
            }
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct PendingGradingRow: View {
    let item: PendingGradingItem
    var isCompact: Bool = false

    var body: some View {
        HStack(spacing: isCompact ? 8 : 12) {
            Circle()
                .fill(Color.orange.opacity(0.2))
                .frame(width: isCompact ? 30 : 40, height: isCompact ? 30 : 40)
                .overlay(
                    Image(systemName: "pencil")
                        .font(isCompact ? .caption : .body)
                        .foregroundColor(.orange)
                )

            VStack(alignment: .leading, spacing: isCompact ? 2 : 4) {
                Text(item.exerciseTitle)
                    .font(isCompact ? .caption : .subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)

                Text("by \(item.studentName)")
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .lineLimit(1)

                Text("Submitted \(timeAgoString(from: item.submittedAt))")
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .lineLimit(1)
            }

            Spacer()

            if !isCompact {
                Button("Grade") {
                    // Handle grading action
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
        }
        .padding(.horizontal, isCompact ? 8 : 12)
    }
}

struct OverdueAlertsSection: View {
    let alerts: [OverdueAlert]
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 8 : 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .font(isCompact ? .subheadline : .body)
                    .foregroundColor(.red)
                Text("Overdue")
                    .font(isCompact ? .subheadline : .headline)
                Spacer()
                Text("\(alerts.count)")
                    .font(.caption2)
                    .padding(.horizontal, isCompact ? 6 : 8)
                    .padding(.vertical, isCompact ? 2 : 4)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(isCompact ? 6 : 8)
            }

            LazyVStack(spacing: isCompact ? 6 : 8) {
                ForEach(alerts.prefix(isCompact ? 2 : 3)) { alert in
                    OverdueAlertRow(alert: alert, isCompact: isCompact)
                }

                if alerts.count > (isCompact ? 2 : 3) {
                    HStack {
                        Text("and \(alerts.count - (isCompact ? 2 : 3)) more overdue exercises...")
                            .font(.caption2)
                            .foregroundColor(.gray)
                        Spacer()
                    }
                    .padding(.horizontal, isCompact ? 8 : 12)
                }
            }
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct OverdueAlertRow: View {
    let alert: OverdueAlert
    var isCompact: Bool = false

    var body: some View {
        HStack(spacing: isCompact ? 8 : 12) {
            Circle()
                .fill(Color.red.opacity(0.2))
                .frame(width: isCompact ? 30 : 40, height: isCompact ? 30 : 40)
                .overlay(
                    Image(systemName: "clock")
                        .font(isCompact ? .caption : .body)
                        .foregroundColor(.red)
                )

            VStack(alignment: .leading, spacing: isCompact ? 2 : 4) {
                Text(alert.exerciseTitle)
                    .font(isCompact ? .caption : .subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)

                Text("\(alert.studentsAffected) students in \(alert.classroomName)")
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .lineLimit(1)

                Text("Overdue by \(overdueString(from: alert.overdueBy))")
                    .font(.caption2)
                    .foregroundColor(.red)
                    .lineLimit(1)
            }

            Spacer()
        }
        .padding(.horizontal, isCompact ? 8 : 12)
    }
}

struct PerformanceAnalyticsSection: View {
    let analytics: PerformanceAnalytics
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 10 : 16) {
            HStack {
                Image(systemName: "chart.bar.fill")
                    .font(isCompact ? .subheadline : .body)
                    .foregroundColor(.blue)
                Text("Performance Analytics")
                    .font(isCompact ? .subheadline : .headline)
                Spacer()
            }

            // Key metrics - responsive sizing
            HStack(spacing: isCompact ? 12 : 20) {
                VStack {
                    Text("\(Int(analytics.averageScore))%")
                        .font(isCompact ? .body : .title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                        .lineLimit(1)
                    Text("Avg Score")
                        .font(.caption2)
                        .foregroundColor(.gray)
                }

                VStack {
                    Text("\(Int(analytics.completionRate))%")
                        .font(isCompact ? .body : .title2)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                        .lineLimit(1)
                    Text("Completion")
                        .font(.caption2)
                        .foregroundColor(.gray)
                }

                Spacer()
            }

            // Score trend (simplified visualization) - responsive sizing
            if !analytics.scoresTrend.isEmpty && !isCompact {
                VStack(alignment: .leading, spacing: 8) {
                    Text("7-Day Score Trend")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    let dateFormatter: DateFormatter = {
                        let df = DateFormatter()
                        df.dateFormat = "M/d"
                        return df
                    }()
                    let calendar = Calendar.current
                    let today = Date()
                    let labels: [(String, String)] = (0..<analytics.scoresTrend.count).map { offset in
                        let date = calendar.date(byAdding: .day, value: -(analytics.scoresTrend.count - 1 - offset), to: today) ?? today
                        let weekday = calendar.shortWeekdaySymbols[calendar.component(.weekday, from: date) - 1]
                        let dateString = dateFormatter.string(from: date)
                        return (weekday, dateString)
                    }
                    HStack(alignment: .bottom, spacing: 16) {
                        ForEach(0..<analytics.scoresTrend.count, id: \.self) { index in
                            VStack {
                                Spacer()
                                Rectangle()
                                    .fill(Color.blue.opacity(0.6))
                                    .frame(width: 48, height: max(analytics.scoresTrend[index] / 100 * 80, 4))
                                HStack(spacing: 2) {
                                    Text(labels[index].0)
                                        .font(.caption2)
                                        .foregroundColor(.gray)
                                    Text(labels[index].1)
                                        .font(.caption2)
                                        .foregroundColor(.gray)
                                }
                                .frame(width: 56)
                            }
                        }
                    }
                    .frame(height: 120)
                }
            }

            // Top performing topics - responsive sizing
            if !analytics.topicPerformance.isEmpty {
                VStack(alignment: .leading, spacing: isCompact ? 6 : 8) {
                    Text("Top Performing Topics")
                        .font(isCompact ? .caption : .subheadline)
                        .fontWeight(.medium)

                    ForEach(analytics.topicPerformance.prefix(isCompact ? 2 : 3)) { topic in
                        HStack {
                            Text(topic.topic)
                                .font(.caption2)
                                .lineLimit(1)
                            Spacer()
                            Text("\(Int(topic.averageScore))%")
                                .font(.caption2)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct StudentRiskSection: View {
    let indicators: [StudentRiskIndicator]
    var isCompact: Bool = false

    var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 8 : 12) {
            HStack {
                Image(systemName: "exclamationmark.shield.fill")
                    .font(isCompact ? .subheadline : .body)
                    .foregroundColor(.red)
                Text("Students at Risk")
                    .font(isCompact ? .subheadline : .headline)
                Spacer()
                Text("\(indicators.count)")
                    .font(.caption2)
                    .padding(.horizontal, isCompact ? 6 : 8)
                    .padding(.vertical, isCompact ? 2 : 4)
                    .background(Color.red.opacity(0.2))
                    .cornerRadius(isCompact ? 6 : 8)
            }

            LazyVStack(spacing: isCompact ? 6 : 8) {
                ForEach(indicators.prefix(isCompact ? 3 : 5)) { indicator in
                    StudentRiskRow(indicator: indicator, isCompact: isCompact)
                }

                if indicators.count > (isCompact ? 3 : 5) {
                    HStack {
                        Text("and \(indicators.count - (isCompact ? 3 : 5)) more students...")
                            .font(.caption2)
                            .foregroundColor(.gray)
                        Spacer()
                    }
                    .padding(.horizontal, isCompact ? 8 : 12)
                }
            }
        }
        .padding(isCompact ? 10 : 16)
        .background(Color(uiColor: .systemBackground))
        .cornerRadius(isCompact ? 8 : 12)
        .shadow(color: Color.black.opacity(0.05), radius: isCompact ? 2 : 5, x: 0, y: isCompact ? 1 : 2)
    }
}

struct StudentRiskRow: View {
    let indicator: StudentRiskIndicator
    var isCompact: Bool = false

    var body: some View {
        HStack(spacing: isCompact ? 8 : 12) {
            Circle()
                .fill(indicator.riskLevel.color.opacity(0.2))
                .frame(width: isCompact ? 30 : 40, height: isCompact ? 30 : 40)
                .overlay(
                    Image(systemName: "person.fill.questionmark")
                        .foregroundColor(indicator.riskLevel.color)
                        .font(isCompact ? .caption2 : .caption)
                )

            VStack(alignment: .leading, spacing: isCompact ? 2 : 4) {
                Text(indicator.studentName)
                    .font(isCompact ? .caption : .subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)

                Text(indicator.reasons.first ?? "")
                    .font(.caption2)
                    .foregroundColor(.gray)
                    .lineLimit(1)

                Text("Avg: \(Int(indicator.averageScore))% • \(indicator.riskLevel.rawValue) Risk")
                    .font(.caption2)
                    .foregroundColor(indicator.riskLevel.color)
                    .lineLimit(1)
            }

            Spacer()
        }
        .padding(.horizontal, isCompact ? 8 : 12)
    }
}

// MARK: - Helper Functions

private func timeAgoString(from date: Date) -> String {
    let interval = Date().timeIntervalSince(date)
    let hours = Int(interval) / 3600
    let days = hours / 24
    
    if days > 0 {
        return "\(days) day\(days == 1 ? "" : "s") ago"
    } else if hours > 0 {
        return "\(hours) hour\(hours == 1 ? "" : "s") ago"
    } else {
        return "Just now"
    }
}

private func overdueString(from interval: TimeInterval) -> String {
    let hours = Int(interval) / 3600
    let days = hours / 24
    
    if days > 0 {
        return "\(days) day\(days == 1 ? "" : "s")"
    } else if hours > 0 {
        return "\(hours) hour\(hours == 1 ? "" : "s")"
    } else {
        return "< 1 hour"
    }
} 
