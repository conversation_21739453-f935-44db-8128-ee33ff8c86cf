import SwiftUI

struct LaTeXTextEditor: View {
    @Binding var text: String
    let placeholder: String
    let minLines: Int
    let maxLines: Int
    @State private var showKeyboard = false
    @State private var cursorPosition: Int = 0
    @FocusState private var isTextFieldFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Text Input Area
            VStack(alignment: .leading, spacing: 4) {
                TextField(placeholder, text: $text, axis: .vertical)
                    .lineLimit(minLines...maxLines)
                    .focused($isTextFieldFocused)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .onChange(of: text) { newValue in
                        // Update cursor position when text changes
                        cursorPosition = newValue.count
                    }
                
                // Keyboard Toggle and Info
                HStack {
                    Text("LaTeX supported: Use $...$ for math")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Spacer()
                    
                    But<PERSON>(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showKeyboard.toggle()
                        }
                        if showKeyboard {
                            isTextFieldFocused = false
                        }
                    }) {
                        HStack(spacing: 4) {
                            Image(systemName: "function")
                            Text(showKeyboard ? "Hide LaTeX" : "Show LaTeX")
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
            }
            
            // LaTeX Keyboard
            if showKeyboard {
                LaTeXKeyboardView { latexCode in
                    insertLaTeXAtCursor(latexCode)
                }
                .transition(.asymmetric(
                    insertion: .move(edge: .bottom).combined(with: .opacity),
                    removal: .move(edge: .bottom).combined(with: .opacity)
                ))
            }
        }
    }
    
    private func insertLaTeXAtCursor(_ latexCode: String) {
        // Get current cursor position (or end of text if not available)
        let insertPosition = cursorPosition
        
        // Handle special cases for LaTeX codes that need cursor positioning
        var codeToInsert = latexCode
        var newCursorOffset = 0
        
        // For codes with placeholders, position cursor inside
        if latexCode.contains("{}") {
            codeToInsert = latexCode.replacingOccurrences(of: "{}", with: "{|}")
            if let placeholderRange = codeToInsert.range(of: "|") {
                newCursorOffset = codeToInsert.distance(from: codeToInsert.startIndex, to: placeholderRange.lowerBound)
                codeToInsert = codeToInsert.replacingOccurrences(of: "|", with: "")
            }
        } else if latexCode.contains("\\left(\\right)") {
            codeToInsert = "\\left(|\\right)"
            if let placeholderRange = codeToInsert.range(of: "|") {
                newCursorOffset = codeToInsert.distance(from: codeToInsert.startIndex, to: placeholderRange.lowerBound)
                codeToInsert = codeToInsert.replacingOccurrences(of: "|", with: "")
            }
        } else if latexCode.contains("\\left[\\right]") {
            codeToInsert = "\\left[|\\right]"
            if let placeholderRange = codeToInsert.range(of: "|") {
                newCursorOffset = codeToInsert.distance(from: codeToInsert.startIndex, to: placeholderRange.lowerBound)
                codeToInsert = codeToInsert.replacingOccurrences(of: "|", with: "")
            }
        } else if latexCode.contains("\\left\\{\\right\\}") {
            codeToInsert = "\\left\\{|\\right\\}"
            if let placeholderRange = codeToInsert.range(of: "|") {
                newCursorOffset = codeToInsert.distance(from: codeToInsert.startIndex, to: placeholderRange.lowerBound)
                codeToInsert = codeToInsert.replacingOccurrences(of: "|", with: "")
            }
        }
        
        // Wrap with $ if it's a math expression and not already wrapped
        if shouldWrapInDollarSigns(latexCode) && !isAlreadyInMathMode(at: insertPosition) {
            codeToInsert = "$\(codeToInsert)$"
            newCursorOffset += 1 // Account for the leading $
        }
        
        // Insert the LaTeX code at the current position
        let startIndex = text.index(text.startIndex, offsetBy: min(insertPosition, text.count))
        text.insert(contentsOf: codeToInsert, at: startIndex)
        
        // Update cursor position
        if newCursorOffset > 0 {
            cursorPosition = insertPosition + newCursorOffset
        } else {
            cursorPosition = insertPosition + codeToInsert.count
        }
        
        // Refocus the text field after insertion
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            isTextFieldFocused = true
        }
    }
    
    private func shouldWrapInDollarSigns(_ latexCode: String) -> Bool {
        // Check if the LaTeX code needs to be wrapped in dollar signs
        let mathCommands = ["\\frac", "\\sqrt", "\\sum", "\\int", "\\pm", "\\times", "\\div", 
                           "\\leq", "\\geq", "\\neq", "\\approx", "\\infty", "\\sin", "\\cos", 
                           "\\tan", "\\log", "\\ln", "\\pi", "\\angle", "\\triangle", "^", "_"]
        
        return mathCommands.contains { latexCode.contains($0) }
    }
    
    private func isAlreadyInMathMode(at position: Int) -> Bool {
        // Simple check to see if we're already inside $ ... $ 
        let beforeCursor = String(text.prefix(position))
        let dollarCount = beforeCursor.filter { $0 == "$" }.count
        return dollarCount % 2 == 1 // Odd number means we're inside math mode
    }
}

struct LaTeXTextEditor_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            LaTeXTextEditor(
                text: .constant("Enter your question here"),
                placeholder: "Question Text",
                minLines: 3,
                maxLines: 6
            )
            .padding()
            
            LaTeXTextEditor(
                text: .constant(""),
                placeholder: "Option Text",
                minLines: 2,
                maxLines: 4
            )
            .padding()
        }
    }
} 