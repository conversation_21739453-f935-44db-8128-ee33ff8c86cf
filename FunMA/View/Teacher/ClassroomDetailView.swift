import SwiftUI

struct ClassroomDetailView: View {
    let classroom: Classroom
    @ObservedObject var viewModel: ClassroomViewModel
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingRemoveAlert = false
    @State private var studentToRemove: ClassroomStudent?
    @State private var selectedTab = 1 // 0 = Students, 1 = Exercises (now defaulting to Exercises)
    @State private var exerciseToShow: Exercise?
    @State private var showingAddStudent = false
    @State private var selectedStudent: ClassroomStudent?
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                headerSection
                Divider()
                contentSection
            }
            .navigationBarItems(
                leading: Button("Done") {
                    dismiss()
                }
            )
            .alert("Remove Student", isPresented: $showingRemoveAlert) {
                removeStudentAlert
            } message: {
                removeStudentMessage
            }
        }
        .onAppear {
            print("🏫 ClassroomDetailView: Appearing for classroom '\(classroom.name)' with \(classroom.students.count) students")
        }
        .task {
            // Load exercises assigned to this classroom
            do {
                try await exerciseViewModel.getAllExercises()
                print("✅ ClassroomDetailView: Loaded \(exerciseViewModel.exercises.count) total exercises")
                print("✅ ClassroomDetailView: Found \(assignedExercises.count) exercises assigned to classroom '\(classroom.name)'")
            } catch {
                print("❌ ClassroomDetailView: Failed to load exercises: \(error.localizedDescription)")
            }
        }
        .fullScreenCover(item: $exerciseToShow) { (exercise: Exercise) in
            ExerciseResponseView(exercise: exercise, classroom: classroom)
                .onAppear {
                    print("🎯 ClassroomDetailView: fullScreenCover presenting ExerciseResponseView for '\(exercise.title)'")
                    print("🎯 ClassroomDetailView: Classroom: '\(classroom.name)' with \(classroom.students.count) students")
                }
        }
        .fullScreenCover(isPresented: $showingAddStudent) {
            AddStudentToClassroomView(classroom: classroom, viewModel: viewModel)
                .onAppear {
                    print("🎓 ClassroomDetailView: Presenting AddStudentToClassroomView for classroom '\(classroom.name)'")
                }
        }
        .sheet(item: $selectedStudent) { student in
            StudentPerformanceView(student: student, classroom: classroom)
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            headerTitle
            quickStats
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private var headerTitle: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(classroom.name)
                    .font(.title)
                    .fontWeight(.bold)
                
                classroomInfo
            }
            
            Spacer()
        }
    }
    
    private var classroomInfo: some View {
        HStack {
            Text(classroom.grade.displayName)
                .font(.subheadline)
                .foregroundColor(.blue)
            
            if let subject = classroom.subject {
                Text("•")
                    .foregroundColor(.gray)
                Text(subject)
                    .font(.subheadline)
                    .foregroundColor(.gray)
            }
        }
    }
    
    private var quickStats: some View {
        HStack(spacing: 15) {
            QuickStatItem(
                icon: "person.2.fill",
                value: "\(classroom.students.count)",
                label: "Students"
            )
            
            QuickStatItem(
                icon: "checkmark.circle.fill",
                value: "\(activeStudentsCount)",
                label: "Active"
            )
            
            QuickStatItem(
                icon: "doc.text.below.ecg",
                value: "\(assignedExercises.count)",
                label: "Exercises"
            )
            
            QuickStatItem(
                icon: "calendar",
                value: classroom.schoolYear,
                label: "School Year"
            )
        }
    }
    
    private var activeStudentsCount: Int {
        classroom.students.filter { $0.status == .active }.count
    }
    
    // MARK: - Exercise-related computed properties
    
    private var assignedExercises: [Exercise] {
        exerciseViewModel.exercises.filter { exercise in
            exercise.classroomIds.contains(classroom.id)
        }
    }
    
    // MARK: - Content Section
    
    private var contentSection: some View {
        VStack(spacing: 0) {
            // Tab Picker
            Picker("View", selection: $selectedTab) {
                Text("Exercises (\(assignedExercises.count))").tag(1)
                Text("Students (\(classroom.students.count))").tag(0)
            }
            .pickerStyle(.segmented)
            .padding()
            
            // Tab Content
            if selectedTab == 1 {
                exercisesTabContent
            } else {
                studentsTabContent
            }
        }
    }
    
    private var studentsTabContent: some View {
        VStack(spacing: 0) {
            // Add Student Button
            HStack {
                Spacer()
                
                Button(action: {
                    showingAddStudent = true
                }) {
                    HStack {
                        Image(systemName: "person.badge.plus")
                        Text("Add Students")
                    }
                    .font(.subheadline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    .background(Color.blue)
                    .cornerRadius(8)
                }
            }
            .padding()
            .background(Color(.systemGroupedBackground))
            
            // Students Content
            Group {
                if classroom.students.isEmpty {
                    emptyState
                } else {
                    studentsList
                }
            }
        }
    }
    
    private var exercisesTabContent: some View {
        Group {
            if assignedExercises.isEmpty {
                exercisesEmptyState
            } else {
                exercisesList
            }
        }
    }
    
    private var emptyState: some View {
        VStack(spacing: 24) {
            Image(systemName: "person.2.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            emptyStateText
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private var emptyStateText: some View {
        VStack(spacing: 8) {
            Text("No Students Yet")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("This classroom doesn't have any students yet")
                .font(.subheadline)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
    }
    

    
    private var studentsList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(classroom.students) { student in
                    StudentCard(
                        student: student,
                        onTap: {
                            selectedStudent = student
                        },
                        onRemove: { 
                            studentToRemove = student
                            showingRemoveAlert = true
                        },
                        onUpdateStatus: { status in
                            Task {
                                await viewModel.updateStudentStatus(student, newStatus: status, in: classroom)
                            }
                        }
                    )
                }
            }
            .padding()
        }
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Exercise Views
    
    private var exercisesEmptyState: some View {
        VStack(spacing: 24) {
            Image(systemName: "doc.text.below.ecg")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Exercises Assigned")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("This classroom doesn't have any exercises assigned yet")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    private var exercisesList: some View {
        ScrollView {
            LazyVStack(spacing: 12) {
                ForEach(assignedExercises) { exercise in
                    Button(action: {
                        // print("🎯 ClassroomDetailView: Exercise card tapped for '\(exercise.title)'")
                        print("🎯 ClassroomDetailView: Setting exerciseToShow to '\(exercise.title)'")
                        exerciseToShow = exercise
                        print("🎯 ClassroomDetailView: exerciseToShow set to '\(exerciseToShow?.title ?? "nil")'")
                    }) {
                        ClassroomExerciseCard(exercise: exercise)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding()
        }
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Alerts
    
    private var removeStudentAlert: some View {
        Group {
            Button("Cancel", role: .cancel) { 
                studentToRemove = nil
            }
            Button("Remove", role: .destructive) {
                if let student = studentToRemove {
                    Task {
                        await viewModel.removeStudent(student, from: classroom)
                    }
                }
                studentToRemove = nil
            }
        }
    }
    
    private var removeStudentMessage: some View {
        Group {
            if let student = studentToRemove {
                Text("Are you sure you want to remove \(student.name) from this classroom?")
            }
        }
    }
}

// MARK: - Quick Stat Item Component
struct QuickStatItem: View {
    let icon: String
    let value: String
    let label: String

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.subheadline)

            Text("\(value) \(label)")
                .font(.subheadline)
                .fontWeight(.semibold)
                .lineLimit(1)
        }
    }
}

// MARK: - Student Card Component
struct StudentCard: View {
    let student: ClassroomStudent
    let onTap: () -> Void
    let onRemove: () -> Void
    let onUpdateStatus: (ClassroomStudent.StudentStatus) -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            avatarSection
            studentInfo
            Spacer()
            actionsMenu
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
        .onTapGesture {
            onTap()
        }
    }
    
    private var avatarSection: some View {
        Circle()
            .fill(student.status.color.opacity(0.2))
            .frame(width: 50, height: 50)
            .overlay(
                Text(student.name.prefix(1).uppercased())
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(student.status.color)
            )
            .onAppear {
                print("🎓 StudentCard: Showing student '\(student.name)' with status '\(student.status.displayName)'")
            }
    }
    
    private var studentInfo: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(student.name)
                .font(.headline)
                .foregroundColor(.primary)
            
            Text("@\(student.username)")
                .font(.subheadline)
                .foregroundColor(.gray)
            
            HStack {
                Text(student.status.displayName)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(student.status.color.opacity(0.1))
                    .foregroundColor(student.status.color)
                    .cornerRadius(4)
                
                Text("Joined \(formatDate(student.joinedAt))")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
    }
    
    private var actionsMenu: some View {
        Menu {
            statusActions
            Divider()
            removeAction
        } label: {
            Image(systemName: "ellipsis")
                .foregroundColor(.gray)
                .padding(8)
                .background(Color(.systemGray6))
                .clipShape(Circle())
        }
    }
    
    private var statusActions: some View {
        Group {
            if student.status == .active {
                Button("Mark as Inactive") {
                    onUpdateStatus(.inactive)
                }
            } else if student.status == .inactive {
                Button("Mark as Active") {
                    onUpdateStatus(.active)
                }
            }
            
            Button("Mark as Transferred") {
                onUpdateStatus(.transferred)
            }
        }
    }
    
    private var removeAction: some View {
        Button("Remove from Class", role: .destructive) {
            onRemove()
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Classroom Exercise Card Component
struct ClassroomExerciseCard: View {
    let exercise: Exercise
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(exercise.title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text(exercise.topic)
                        .font(.subheadline)
                        .foregroundColor(.blue)
                    
                    if !exercise.subtopic.isEmpty {
                        Text(exercise.subtopic)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(exercise.questions.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    
                    Text("Questions")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            // Due date and created date
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    HStack {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(.orange)
                        Text("Due: \(exercise.dueDate.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    
                    HStack {
                        Image(systemName: "calendar")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Text("Created: \(exercise.createdAt.formatted(date: .abbreviated, time: .omitted))")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Spacer()
                
                // Exercise status indicator
                ExerciseStatusBadge(exercise: exercise)
            }
            
            // Show other assigned classrooms if multiple
            if exercise.classroomIds.count > 1 {
                HStack {
                    Image(systemName: "building.2")
                        .font(.caption)
                        .foregroundColor(.purple)
                    Text("Also assigned to \(exercise.classroomIds.count - 1) other classroom(s)")
                        .font(.caption)
                        .foregroundColor(.purple)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

// MARK: - Exercise Status Badge
struct ExerciseStatusBadge: View {
    let exercise: Exercise
    
    private var status: (text: String, color: Color) {
        let now = Date()
        if exercise.dueDate < now {
            return ("Overdue", .red)
        } else if exercise.dueDate.timeIntervalSinceNow < 24 * 60 * 60 { // Due within 24 hours
            return ("Due Soon", .orange)
        } else {
            return ("Active", .green)
        }
    }
    
    var body: some View {
        Text(status.text)
            .font(.caption)
            .fontWeight(.semibold)
            .foregroundColor(status.color)
    }
} 

// MARK: - Student Performance View
struct StudentPerformanceView: View {
    let student: ClassroomStudent
    let classroom: Classroom
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    @Environment(\.dismiss) private var dismiss
    
    @State private var studentSubmissions: [StudentSubmission] = []
    @State private var studentExercises: [Exercise] = []
    @State private var isLoading = true
    @State private var performanceData: StudentPerformanceData?
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    studentHeader
                    
                    if isLoading {
                        ProgressView("Loading student data...")
                            .frame(maxWidth: .infinity, minHeight: 200)
                    } else {
                        performanceOverview
                        exerciseHistory
                        detailedAnalytics
                    }
                }
                .padding()
            }
            .navigationTitle("Student Performance")
            .navigationBarTitleDisplayMode(.large)
            .navigationBarItems(
                trailing: Button("Done") {
                    dismiss()
                }
            )
        }
        .task {
            await loadStudentData()
        }
    }
    
    private var studentHeader: some View {
        VStack(spacing: 16) {
            // Student Avatar and Basic Info
            HStack(spacing: 16) {
                Circle()
                    .fill(student.status.color.opacity(0.2))
                    .frame(width: 80, height: 80)
                    .overlay(
                        Text(student.name.prefix(2).uppercased())
                            .font(.title)
                            .fontWeight(.bold)
                            .foregroundColor(student.status.color)
                    )
                
                VStack(alignment: .leading, spacing: 8) {
                    Text(student.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("@\(student.username)")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    
                    HStack {
                        Text(student.status.displayName)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(student.status.color.opacity(0.1))
                            .foregroundColor(student.status.color)
                            .cornerRadius(6)
                        
                        Text("Class: \(classroom.name)")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var performanceOverview: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Performance Overview")
                .font(.headline)
            
            if let performance = performanceData {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: 16) {
                    PerformanceStatCard(
                        title: "Average Score",
                        value: "\(Int(performance.averageScore))%",
                        color: performance.averageScore >= 70 ? .green : performance.averageScore >= 50 ? .orange : .red,
                        icon: "chart.bar.fill"
                    )
                    
                    PerformanceStatCard(
                        title: "Completion Rate",
                        value: "\(Int(performance.completionRate))%",
                        color: performance.completionRate >= 80 ? .green : performance.completionRate >= 60 ? .orange : .red,
                        icon: "checkmark.circle.fill"
                    )
                    
                    PerformanceStatCard(
                        title: "Exercises Done",
                        value: "\(performance.completedExercises)",
                        color: .blue,
                        icon: "doc.text.fill"
                    )
                    
                    PerformanceStatCard(
                        title: "Late Submissions",
                        value: "\(performance.lateSubmissions)",
                        color: performance.lateSubmissions > 2 ? .red : performance.lateSubmissions > 0 ? .orange : .green,
                        icon: "clock.fill"
                    )
                }
                
                // Risk indicators if any
                if !performance.riskFactors.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Areas of Concern")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.red)
                        
                        ForEach(performance.riskFactors, id: \.self) { risk in
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.red)
                                    .font(.caption)
                                Text(risk)
                                    .font(.caption)
                                    .foregroundColor(.red)
                                Spacer()
                            }
                        }
                    }
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var exerciseHistory: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Exercise History")
                .font(.headline)
            
            if studentSubmissions.isEmpty {
                Text("No exercises completed yet")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity, minHeight: 60)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(studentSubmissions.prefix(10)) { submission in
                        StudentExerciseRow(submission: submission, exercise: getExercise(for: submission.exerciseId))
                    }
                    
                    if studentSubmissions.count > 10 {
                        Text("and \(studentSubmissions.count - 10) more exercises...")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private var detailedAnalytics: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Detailed Analytics")
                .font(.headline)
            
            if let performance = performanceData {
                VStack(alignment: .leading, spacing: 12) {
                    AnalyticsRow(label: "Last Activity", value: formatDate(performance.lastActivity))
                    AnalyticsRow(label: "Days Since Joined", value: "\(performance.daysSinceJoined)")
                    AnalyticsRow(label: "Strongest Topic", value: performance.strongestTopic)
                    AnalyticsRow(label: "Needs Improvement", value: performance.weakestTopic)
                    AnalyticsRow(label: "Total Study Time", value: "\(performance.totalStudyTimeMinutes) min")
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.05), radius: 5, x: 0, y: 2)
    }
    
    private func loadStudentData() async {
        isLoading = true
        
        do {
            // Load exercises for the classroom
            try await exerciseViewModel.getExercises(for: classroom.id)
            studentExercises = exerciseViewModel.exercises
            
            // Load all submissions and filter for this student
            var allSubmissions: [StudentSubmission] = []
            for exercise in studentExercises {
                do {
                    let submissions = try await exerciseViewModel.getStudentSubmissions(for: exercise.id)
                    let studentSpecificSubmissions = submissions.filter { $0.studentId == student.id }
                    allSubmissions.append(contentsOf: studentSpecificSubmissions)
                } catch {
                    print("Error loading submissions for exercise \(exercise.id): \(error)")
                }
            }
            
            studentSubmissions = allSubmissions.sorted { $0.startTime > $1.startTime }
            
            // Calculate performance data
            performanceData = calculatePerformanceData()
            
        } catch {
            print("Error loading student data: \(error)")
        }
        
        isLoading = false
    }
    
    private func calculatePerformanceData() -> StudentPerformanceData {
        let scores = studentSubmissions.compactMap { $0.score }
        let averageScore = scores.isEmpty ? 0.0 : scores.reduce(0, +) / Double(scores.count)
        
        let completedCount = studentSubmissions.filter { $0.endTime != nil }.count
        let totalAssigned = studentExercises.count
        let completionRate = totalAssigned > 0 ? Double(completedCount) / Double(totalAssigned) * 100 : 0.0
        
        let lateSubmissions = studentSubmissions.filter { $0.isLateSubmission == true }.count
        
        let lastActivity = studentSubmissions.compactMap { $0.endTime }.max() ?? student.joinedAt
        let daysSinceJoined = Calendar.current.dateComponents([.day], from: student.joinedAt, to: Date()).day ?? 0
        
        // Calculate topic performance
        var topicScores: [String: [Double]] = [:]
        for submission in studentSubmissions {
            if let score = submission.score,
               let exercise = getExercise(for: submission.exerciseId) {
                if topicScores[exercise.topic] == nil {
                    topicScores[exercise.topic] = []
                }
                topicScores[exercise.topic]?.append(score)
            }
        }
        
        let topicAverages = topicScores.mapValues { scores in
            scores.reduce(0, +) / Double(scores.count)
        }
        
        let strongestTopic = topicAverages.max(by: { $0.value < $1.value })?.key ?? "N/A"
        let weakestTopic = topicAverages.min(by: { $0.value < $1.value })?.key ?? "N/A"
        
        // Calculate study time (simplified)
        let totalStudyTimeMinutes = studentSubmissions.compactMap { submission in
            guard let endTime = submission.endTime else { return nil }
            return Int(endTime.timeIntervalSince(submission.startTime) / 60)
        }.reduce(0, +)
        
        // Risk factors
        var riskFactors: [String] = []
        if averageScore < 60 && !scores.isEmpty {
            riskFactors.append("Low average score (\(Int(averageScore))%)")
        }
        if lateSubmissions > 2 {
            riskFactors.append("\(lateSubmissions) late submissions")
        }
        if completionRate < 70 {
            riskFactors.append("Low completion rate (\(Int(completionRate))%)")
        }
        let daysSinceActivity = Calendar.current.dateComponents([.day], from: lastActivity, to: Date()).day ?? 0
        if daysSinceActivity > 7 {
            riskFactors.append("No activity for \(daysSinceActivity) days")
        }
        
        return StudentPerformanceData(
            averageScore: averageScore,
            completionRate: completionRate,
            completedExercises: completedCount,
            lateSubmissions: lateSubmissions,
            lastActivity: lastActivity,
            daysSinceJoined: daysSinceJoined,
            strongestTopic: strongestTopic,
            weakestTopic: weakestTopic,
            totalStudyTimeMinutes: totalStudyTimeMinutes,
            riskFactors: riskFactors
        )
    }
    
    private func getExercise(for exerciseId: UUID) -> Exercise? {
        return studentExercises.first { $0.id == exerciseId }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Student Performance Data Model
struct StudentPerformanceData {
    let averageScore: Double
    let completionRate: Double
    let completedExercises: Int
    let lateSubmissions: Int
    let lastActivity: Date
    let daysSinceJoined: Int
    let strongestTopic: String
    let weakestTopic: String
    let totalStudyTimeMinutes: Int
    let riskFactors: [String]
}

// MARK: - Performance Stat Card
struct PerformanceStatCard: View {
    let title: String
    let value: String
    let color: Color
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(10)
    }
}

// MARK: - Student Exercise Row
struct StudentExerciseRow: View {
    let submission: StudentSubmission
    let exercise: Exercise?
    
    var body: some View {
        HStack(spacing: 12) {
            // Score indicator
            Circle()
                .fill(scoreColor.opacity(0.2))
                .frame(width: 40, height: 40)
                .overlay(
                    Text(submission.score != nil ? "\(Int(submission.score!))" : "?")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(scoreColor)
                )
            
            VStack(alignment: .leading, spacing: 4) {
                Text(exercise?.title ?? "Unknown Exercise")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(exercise?.topic ?? "")
                    .font(.caption)
                    .foregroundColor(.blue)
                
                HStack {
                    Text("Submitted: \(formatDate(submission.endTime ?? submission.startTime))")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    if submission.isLateSubmission == true {
                        Text("• LATE")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.red)
                    }
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                if let score = submission.score {
                    Text("\(Int(score))%")
                        .font(.subheadline)
                        .fontWeight(.bold)
                        .foregroundColor(scoreColor)
                } else {
                    Text("Pending")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                
                Text("\(submission.answers.count) answers")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(Color(.systemGray6).opacity(0.5))
        .cornerRadius(8)
    }
    
    private var scoreColor: Color {
        guard let score = submission.score else { return .gray }
        if score >= 80 { return .green }
        if score >= 60 { return .orange }
        return .red
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Analytics Row
struct AnalyticsRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .font(.subheadline)
                .foregroundColor(.gray)
            Spacer()
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
        }
        .padding(.vertical, 4)
    }
} 