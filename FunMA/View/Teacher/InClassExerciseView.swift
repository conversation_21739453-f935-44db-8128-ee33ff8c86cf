import SwiftUI
import MarkdownUI
import SwiftMath

// Add DueFilter enum
private enum DueFilter: String, CaseIterable, Identifiable {
    case all = "All"
    case current = "Current"
    case past = "Past"
    var id: String { self.rawValue }
}

struct InClassExerciseView: View {
    @StateObject private var viewModel = ExerciseViewModel()
    @StateObject private var userManager = UserManager.shared
    @StateObject private var curriculumManager = CurriculumManager.shared
    @StateObject private var classroomViewModel = ClassroomViewModel()
    @State private var showingCreateExercise = false
    @State private var selectedClassroomId: String = ""
    @State private var dueFilter: DueFilter = .all
    @State private var exerciseToDelete: Exercise?
    @State private var showingDeleteExerciseConfirmation = false
    @State private var selectedExerciseForResponses: Exercise?
    @State private var exerciseToViewQuestions: Exercise?
    
    // Available classrooms (fetched from API)
    private var availableClassrooms: [Classroom] {
        classroomViewModel.classrooms
    }
    
    // Filtered exercises based on selected classroom and due filter
    private var filteredExercises: [Exercise] {
        var exercises = viewModel.exercises
        
        // Filter by classroom if selected
        if !selectedClassroomId.isEmpty {
            exercises = exercises.filter { $0.classroomIds.contains(selectedClassroomId) }
        }
        
        // Filter by due filter
        let now = Date()
        switch dueFilter {
        case .current:
            exercises = exercises.filter { $0.dueDate >= now }
        case .past:
            exercises = exercises.filter { $0.dueDate < now }
        case .all:
            break
        }
        return exercises
    }
    
    // Count of overdue exercises
    private var overdueCount: Int {
        let now = Date()
        return filteredExercises.filter { $0.dueDate < now }.count
    }
    
    // Count of exercises due soon (within 24 hours)
    private var dueSoonCount: Int {
        let now = Date()
        let oneDayFromNow = now.addingTimeInterval(24 * 60 * 60)
        return filteredExercises.filter { 
            $0.dueDate >= now && $0.dueDate <= oneDayFromNow 
        }.count
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Title Header Section
            HStack {
                Text("Exercise")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding(.horizontal)
            .padding(.top, 8)
            
            // Enhanced Header Section
            VStack(spacing: 16) {
                // Filter Section with improved styling
                VStack(alignment: .leading, spacing: 12) {   
                    // Responsive header layout
                    GeometryReader { geometry in
                        let isCompact = geometry.size.width < 600 // Mobile threshold

                        HStack{
                            Picker("Select Classroom", selection: $selectedClassroomId) {
                                HStack {
                                    Image(systemName: "globe")
                                    Text(isCompact ? "All" : "All Classrooms")
                                }.tag("")

                                ForEach(availableClassrooms) { classroom in
                                    HStack {
                                        Image(systemName: "building.2")
                                            .foregroundColor(.blue)
                                        Text(classroom.name)
                                            .foregroundColor(.blue)
                                        if !isCompact {
                                            Text("(\(classroom.students.count) students)")
                                                .font(.caption)
                                                .foregroundColor(.gray)
                                        }
                                    }.tag(classroom.id)
                                }
                            }
                            .pickerStyle(.menu)
                            .foregroundColor(.blue)
                            .onChange(of: selectedClassroomId) { newClassroomId in
                                Task {
                                    if !newClassroomId.isEmpty {
                                        try? await viewModel.getExercises(for: newClassroomId)
                                    } else {
                                        try? await viewModel.getAllExercises()
                                    }
                                }
                            }

                            if !isCompact {
                                HStack{
                                    Picker("Due Filter", selection: $dueFilter) {
                                        ForEach(DueFilter.allCases) { filter in
                                            Text(filter.rawValue).tag(filter)
                                        }
                                    }
                                    .pickerStyle(.menu)
                                    .frame(width: 100)
                                }
                            }

                            Spacer()

                            Text("\(filteredExercises.count) Exercises")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                                .padding(.trailing, 12)
                                .lineLimit(1)

                            Button(action: {
                                print("🔧 DEBUG: Create Exercise button tapped")
                                showingCreateExercise = true
                            }) {
                                if isCompact {
                                    // Mobile: Only show icon
                                    Image(systemName: "plus.circle.fill")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                        .padding(12)
                                        .background(Color.blue)
                                        .clipShape(Circle())
                                } else {
                                    // Desktop: Show icon + text
                                    HStack {
                                        Image(systemName: "plus.circle.fill")
                                        Text("Create")
                                    }
                                    .font(.headline)
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(Color.blue)
                                    .cornerRadius(10)
                                }
                            }
                            .buttonStyle(.plain)
                        }
                    }
                    .frame(height: 44) // Fixed height for GeometryReader
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                
            }
            .padding()
            .background(Color(.systemBackground))
            
            Divider()
            
            // Exercise List - Shows ALL exercises by default
            if viewModel.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                        .scaleEffect(1.2)
                    Text("Loading exercises...")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if filteredExercises.isEmpty {
                EnhancedEmptyStateView(
                    title: viewModel.exercises.isEmpty ? "No Exercises Created" : "No Matching Exercises",
                    systemImage: viewModel.exercises.isEmpty ? "doc.text.below.ecg" : "magnifyingglass",
                    description: viewModel.exercises.isEmpty ? 
                        "Get started by creating your first exercise for your students" : 
                        "No exercises match your current filter. Try selecting a different classroom.",
                    actionTitle: viewModel.exercises.isEmpty ? "Create Exercise" : nil,
                    action: viewModel.exercises.isEmpty ? {
                        showingCreateExercise = true
                    } : nil
                )
            } else {
                ScrollView {
                    // Use single column layout for mobile (compact width)
                    GeometryReader { geometry in
                        let isCompact = geometry.size.width < 600 // Mobile threshold
                        let columns = isCompact ?
                            [GridItem(.flexible())] :
                            [GridItem(.flexible(), spacing: 20), GridItem(.flexible(), spacing: 20)]

                        LazyVGrid(columns: columns, spacing: 20) {
                            ForEach(filteredExercises) { exercise in
                                ExerciseCard(
                                    exercise: exercise,
                                    viewModel: viewModel,
                                    onDelete: {
                                        print("🔧 DEBUG: Delete button tapped for exercise: \(exercise.title)")
                                        exerciseToDelete = exercise
                                        showingDeleteExerciseConfirmation = true
                                    },
                                    onViewQuestions: {
                                        print("🔧 DEBUG: View Questions button tapped for exercise: \(exercise.title)")
                                        exerciseToViewQuestions = exercise
                                    },
                                    onCardTap: {
                                        print("🔧 DEBUG: Exercise card tapped for exercise: \(exercise.title)")
                                        print("🔧 DEBUG: Exercise ID: \(exercise.id)")
                                        // Navigate to ExerciseResponseView
                                        selectedExerciseForResponses = exercise
                                    }
                                )
                            }
                        }
                        .padding()
                    }
                }
                .background(Color(.systemGroupedBackground))
            }
        }
        .fullScreenCover(isPresented: $showingCreateExercise) {
            CreateExerciseView(viewModel: viewModel, selectedClassroomId: selectedClassroomId)
        }
        .fullScreenCover(item: $selectedExerciseForResponses) { exercise in
            ExerciseResponseView(
                exercise: exercise,
                classroom: selectedClassroomId.isEmpty ? nil : availableClassrooms.first { $0.id == selectedClassroomId }
            )
            .onAppear {
                print("🔧 DEBUG: InClassExerciseView - presenting ExerciseResponseView for exercise: \(exercise.title)")
                print("🔧 DEBUG: Exercise ID: \(exercise.id)")
                print("🔧 DEBUG: Selected classroom: \(selectedClassroomId.isEmpty ? "All" : selectedClassroomId)")
            }
        }
        .fullScreenCover(item: $exerciseToViewQuestions) { exercise in
            ExerciseQuestionsReviewView(exercise: exercise)
        }
        .alert("Delete Exercise", isPresented: $showingDeleteExerciseConfirmation) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let exercise = exerciseToDelete {
                    deleteExercise(exercise)
                }
            }
        } message: {
            Text("Are you sure you want to delete this exercise? This action cannot be undone and will also delete all student responses.")
        }
        .task {
            // Load teacher's classrooms first
            await loadClassrooms()
            
            // Load ALL exercises from MongoDB API by default - no classroom filter
            let startTime = Date()
            print("🕐 InClassExerciseView: Starting to load ALL exercises at \(startTime)")
            
            do {
                try await viewModel.getAllExercises()
                let endTime = Date()
                let duration = endTime.timeIntervalSince(startTime)
                print("✅ InClassExerciseView: Successfully loaded \(viewModel.exercises.count) exercises in \(String(format: "%.2f", duration)) seconds")
            } catch {
                let endTime = Date()
                let duration = endTime.timeIntervalSince(startTime)
                print("❌ InClassExerciseView: Failed to load exercises after \(String(format: "%.2f", duration)) seconds: \(error.localizedDescription)")
            }
        }
    }
    
    private func loadClassrooms() async {
        do {
            try await classroomViewModel.loadClassrooms()
            print("✅ InClassExerciseView: Successfully loaded \(classroomViewModel.classrooms.count) classrooms")
        } catch {
            print("❌ InClassExerciseView: Failed to load classrooms: \(error.localizedDescription)")
        }
    }
    
    private func deleteExercise(_ exercise: Exercise) {
        Task {
            do {
                // Use the new API endpoint to delete the exercise
                try await viewModel.deleteExercise(exercise.id)
                exerciseToDelete = nil
                print("✅ InClassExerciseView: Successfully deleted exercise: \(exercise.title)")
            } catch {
                print("❌ InClassExerciseView: Failed to delete exercise: \(error.localizedDescription)")
                // Even if API call fails, remove from local list as fallback
                await MainActor.run {
                    viewModel.exercises.removeAll { $0.id == exercise.id }
                }
                exerciseToDelete = nil
            }
        }
    }
}

// MARK: - String Extension for Regex Matching
extension String {
    func matches(pattern: String) -> Bool {
        guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
            return false
        }
        let range = NSRange(self.startIndex..., in: self)
        return regex.firstMatch(in: self, options: [], range: range) != nil
    }
}

#Preview {
    InClassExerciseView()
}
