//
//  FlippedClassroomResourceView.swift
//  FunMA
//
//  Created by AI Assistant
//

import SwiftUI
import UniformTypeIdentifiers

struct FlippedClassroomResourceView: View {
    @State private var topic: String = ""
    @State private var description: String = ""
    @State private var selectedMaterialTypes: Set<MaterialType> = []
    @State private var uploadedFiles: [UploadedFile] = []
    @State private var isShowingFilePicker = false
    @State private var isGenerating = false
    @State private var showingSuccessAlert = false
    @State private var targetAudience: AudienceLevel = .form1
    @State private var duration: Int = 30 // in minutes
    
    enum MaterialType: String, CaseIterable, Identifiable {
        case video = "Video Lectures"
        case notes = "Study Notes"
        case exercises = "Practice Exercises"
        case quiz = "Interactive Quizzes"
        case presentation = "Presentation Slides"
        case worksheets = "Worksheets"
        
        var id: String { self.rawValue }
        
        var icon: String {
            switch self {
            case .video: return "video.fill"
            case .notes: return "doc.text.fill"
            case .exercises: return "pencil.and.outline"
            case .quiz: return "questionmark.circle.fill"
            case .presentation: return "rectangle.stack.fill"
            case .worksheets: return "doc.on.doc.fill"
            }
        }
    }
    
    enum AudienceLevel: String, CaseIterable, Identifiable {
        case form1 = "Form 1"
        case form2 = "Form 2"
        case form3 = "Form 3"
        case form4 = "Form 4"
        case form5 = "Form 5"
        case form6 = "Form 6"
        
        var id: String { self.rawValue }
    }
    
    struct UploadedFile: Identifiable {
        let id = UUID()
        let name: String
        let type: String
        let size: String
    }
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Text("Flipped Classroom Resource")
                    .font(.title)
                    .fontWeight(.bold)
                    .padding(.bottom, 4)
                headerSection
                topicSection
                fileUploadSection
                settingsSection
                materialTypeSection
                generateSection
            }
            .padding()
        }
        .alert("Resources Generated Successfully!", isPresented: $showingSuccessAlert) {
            Button("OK") { }
        } message: {
            Text("Your flipped classroom materials have been generated and are ready for use.")
        }
    }
    
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "video.and.waveform.fill")
                    .font(.title2)
                    .foregroundColor(.blue)
                Text("Create Flipped Classroom Materials")
                    .font(.title2)
                    .fontWeight(.bold)
            }
            
            Text("Upload your lecture materials and generate engaging content for out-of-class learning.")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .padding(.bottom, 10)
    }
    
    private var topicSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "lightbulb.fill")
                    .foregroundColor(.orange)
                Text("Topic & Description")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Topic")
                    .font(.subheadline)
                    .fontWeight(.medium)
                TextField("Enter the main topic (e.g., Quadratic Equations)", text: $topic)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Description")
                    .font(.subheadline)
                    .fontWeight(.medium)
                TextField("Provide additional context or specific learning objectives...", text: $description, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var fileUploadSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "doc.badge.plus")
                    .foregroundColor(.green)
                Text("Upload Materials")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            if uploadedFiles.isEmpty {
                Button(action: {
                    isShowingFilePicker = true
                }) {
                    VStack(spacing: 12) {
                        Image(systemName: "cloud.upload")
                            .font(.system(size: 40))
                            .foregroundColor(.blue)
                        
                        Text("Upload Teaching Plan / Materials")
                            .font(.headline)
                            .foregroundColor(.blue)
                        
                        Text("PDF, PowerPoint, Word documents, images, or video files")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .frame(maxWidth: .infinity)
                    .frame(height: 120)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue.opacity(0.5), style: StrokeStyle(lineWidth: 2, dash: [8]))
                    )
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(uploadedFiles) { file in
                        HStack {
                            Image(systemName: fileIcon(for: file.type))
                                .foregroundColor(.blue)
                            
                            VStack(alignment: .leading, spacing: 2) {
                                Text(file.name)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                Text("\(file.type) • \(file.size)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Button(action: {
                                if let index = uploadedFiles.firstIndex(where: { $0.id == file.id }) {
                                    uploadedFiles.remove(at: index)
                                }
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.red)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        .padding(.vertical, 8)
                        .padding(.horizontal, 12)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    
                    Button(action: {
                        isShowingFilePicker = true
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                            Text("Add More Files")
                        }
                        .font(.subheadline)
                        .foregroundColor(.blue)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
        .fileImporter(
            isPresented: $isShowingFilePicker,
            allowedContentTypes: [
                .pdf, 
                .jpeg, 
                .png, 
                .movie, 
                .audio,
                UTType(filenameExtension: "ppt")!,
                UTType(filenameExtension: "pptx")!,
                UTType(filenameExtension: "doc")!,
                UTType(filenameExtension: "docx")!
            ],
            allowsMultipleSelection: true
        ) { result in
            handleFileImport(result)
        }
    }
    
    private var settingsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "gearshape.fill")
                    .foregroundColor(.purple)
                Text("Settings")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Target Audience")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Picker("Audience Level", selection: $targetAudience) {
                        ForEach(AudienceLevel.allCases) { level in
                            Text(level.rawValue).tag(level)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                
                Spacer()
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Duration (minutes)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    HStack {
                        Button(action: {
                            if duration > 15 { duration -= 15 }
                        }) {
                            Image(systemName: "minus.circle")
                        }
                        .disabled(duration <= 15)
                        
                        Text("\(duration)")
                            .font(.headline)
                            .frame(width: 40)
                        
                        Button(action: {
                            if duration < 120 { duration += 15 }
                        }) {
                            Image(systemName: "plus.circle")
                        }
                        .disabled(duration >= 120)
                    }
                    .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var materialTypeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "square.grid.2x2")
                    .foregroundColor(.indigo)
                Text("Material Types to Generate")
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                ForEach(MaterialType.allCases) { type in
                    Button(action: {
                        if selectedMaterialTypes.contains(type) {
                            selectedMaterialTypes.remove(type)
                        } else {
                            selectedMaterialTypes.insert(type)
                        }
                    }) {
                        VStack(spacing: 8) {
                            Image(systemName: type.icon)
                                .font(.system(size: 24))
                                .foregroundColor(selectedMaterialTypes.contains(type) ? .white : .blue)
                            
                            Text(type.rawValue)
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(selectedMaterialTypes.contains(type) ? .white : .primary)
                                .multilineTextAlignment(.center)
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 80)
                        .background(selectedMaterialTypes.contains(type) ? Color.blue : Color.blue.opacity(0.1))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.blue.opacity(selectedMaterialTypes.contains(type) ? 1.0 : 0.3), lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.05))
        .cornerRadius(12)
    }
    
    private var generateSection: some View {
        VStack(spacing: 16) {
            Button(action: {
                generateMaterials()
            }) {
                HStack {
                    if isGenerating {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "wand.and.stars")
                            .font(.system(size: 18))
                    }
                    
                    Text(isGenerating ? "Generating Materials..." : "Generate Materials")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(canGenerate ? Color.blue : Color.gray.opacity(0.5))
                .cornerRadius(12)
            }
            .disabled(!canGenerate || isGenerating)
            .buttonStyle(PlainButtonStyle())
            
            if !canGenerate {
                HStack {
                    Image(systemName: "info.circle")
                        .foregroundColor(.orange)
                    Text("Please provide a topic and select at least one material type to generate.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var canGenerate: Bool {
        !topic.isEmpty && !selectedMaterialTypes.isEmpty
    }
    
    private func fileIcon(for type: String) -> String {
        switch type.lowercased() {
        case "pdf": return "doc.text.fill"
        case "ppt", "pptx": return "rectangle.stack.fill"
        case "doc", "docx": return "doc.fill"
        case "jpg", "jpeg", "png": return "photo.fill"
        case "mp4", "mov": return "video.fill"
        case "mp3", "wav": return "music.note"
        default: return "doc.fill"
        }
    }
    
    private func generateMaterials() {
        isGenerating = true
        
        // Simulate generation process
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
            isGenerating = false
            showingSuccessAlert = true
            
            // Reset form after successful generation
            topic = ""
            description = ""
            selectedMaterialTypes.removeAll()
            uploadedFiles.removeAll()
            targetAudience = .form1
            duration = 30
        }
    }
    
    private func handleFileImport(_ result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            for url in urls {
                let fileName = url.lastPathComponent
                let fileExtension = url.pathExtension.uppercased()
                let fileSize = getFileSize(url: url)
                
                let uploadedFile = UploadedFile(
                    name: fileName,
                    type: fileExtension,
                    size: fileSize
                )
                
                uploadedFiles.append(uploadedFile)
            }
        case .failure(let error):
            print("File import failed: \(error.localizedDescription)")
        }
    }
    
    private func getFileSize(url: URL) -> String {
        do {
            let resources = try url.resourceValues(forKeys: [.fileSizeKey])
            if let fileSize = resources.fileSize {
                return ByteCountFormatter.string(fromByteCount: Int64(fileSize), countStyle: .file)
            }
        } catch {
            print("Error getting file size: \(error)")
        }
        return "Unknown size"
    }
}

struct FlippedClassroomResourceView_Previews: PreviewProvider {
    static var previews: some View {
        FlippedClassroomResourceView()
    }
} 