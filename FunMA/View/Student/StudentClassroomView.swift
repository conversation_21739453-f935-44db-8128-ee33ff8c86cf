import SwiftUI

struct StudentClassroomView: View {
    @StateObject private var classroomViewModel = ClassroomViewModel()
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    @StateObject private var userManager = UserManager.shared
    @State private var studentClassroom: Classroom?
    // Exercise interaction disabled in classroom view
    // @State private var selectedExercise: Exercise?
    // @State private var showingExercise = false
    // @State private var showingExerciseReview = false
    @State private var isLoading = true
    @State private var error: String?
    @State private var showingErrorAlert = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                if isLoading {
                    loadingView
                } else if let error = error {
                    errorView(error)
                } else if let classroom = studentClassroom {
                    classroomContentView(classroom: classroom)
                } else {
                    noClassroomView
                }
            }
            .navigationTitle("My Classroom")
            .task {
                await loadStudentClassroom()
            }
            .refreshable {
                print("🔄 StudentClassroomView: Pull-to-refresh triggered")
                error = nil // Clear any existing errors before refreshing
                await loadStudentClassroom()
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") {
                    showingErrorAlert = false
                    error = nil
                }
            } message: {
                if let error = error {
                    Text(error)
                }
            }
            .onChange(of: error) { errorMessage in
                if errorMessage != nil && !showingErrorAlert {
                    showingErrorAlert = true
                }
            }
            // Exercise sheets removed - cards are not clickable in classroom view
            // .fullScreenCover(isPresented: $showingExercise) { ... }
            // .fullScreenCover(isPresented: $showingExerciseReview) { ... }
        }
    }
    
    // MARK: - Loading View

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Loading your classroom...")
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // MARK: - Error View

    private func errorView(_ errorMessage: String) -> some View {
        VStack(spacing: 24) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 60))
                .foregroundColor(.orange)

            VStack(spacing: 8) {
                Text("Error Loading Classroom")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text(errorMessage)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }

            Button("Try Again") {
                Task {
                    await loadStudentClassroom()
                }
            }
            .font(.headline)
            .foregroundColor(.white)
            .padding(.horizontal, 24)
            .padding(.vertical, 12)
            .background(Color.blue)
            .cornerRadius(10)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - No Classroom View
    
    private var noClassroomView: some View {
        VStack(spacing: 24) {
            Image(systemName: "building.2")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Classroom Assigned")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("You are not currently assigned to any classroom. Please contact your teacher.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            if let error = error {
                Text("Error: \(error)")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
    
    // MARK: - Classroom Content View
    
    private func classroomContentView(classroom: Classroom) -> some View {
        VStack(spacing: 0) {
            classroomHeaderView(classroom: classroom)
            Divider()
            classroomExercisesView(classroom: classroom)
        }
    }
    
    private func classroomHeaderView(classroom: Classroom) -> some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(classroom.name)
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text(classroom.grade.displayName)
                        .font(.subheadline)
                        .foregroundColor(.blue)
                    
                    if let subject = classroom.subject {
                        Text(subject)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                    }
                    
                    Text("Teacher: \(classroom.teacherName)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(exerciseViewModel.exercises.count) Exercises")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                        .lineLimit(1)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
    }
    
    private func classroomExercisesView(classroom: Classroom) -> some View {
        VStack(spacing: 0) {
            if exerciseViewModel.isLoading {
                VStack(spacing: 16) {
                    ProgressView()
                    Text("Loading exercises...")
                        .foregroundColor(.gray)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if exerciseViewModel.exercises.isEmpty {
                VStack(spacing: 24) {
                    Image(systemName: "doc.text")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    
                    VStack(spacing: 8) {
                        Text("No Exercises Yet")
                            .font(.title3)
                            .fontWeight(.semibold)
                        
                        Text("Your teacher hasn't assigned any exercises to this classroom yet.")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding()
            } else {
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(exerciseViewModel.exercises) { exercise in
                            StudentExerciseCard(
                                exercise: exercise,
                                studentClassrooms: studentClassroom != nil ? [studentClassroom!] : [],
                                onTap: {
                                    // Exercise cards are disabled in classroom view
                                    print("📚 StudentClassroomView: Exercise card tap disabled for '\(exercise.title)'")
                                }
                            )
                        }
                    }
                    .padding()
                }
                .background(Color(.systemGroupedBackground))
            }
        }
    }
    
    // MARK: - Data Loading
    
    private func loadStudentClassroom() async {
        isLoading = true
        error = nil
        
        do {
            // Get the student's classroom
            if let classroom = try await userManager.getStudentClassroom() {
                await MainActor.run {
                    self.studentClassroom = classroom
                }
                
                // Load exercises assigned to the student (using JWT authentication)
                try await exerciseViewModel.getStudentExercises()
                
                // Submission loading removed - exercise interaction disabled in classroom view
                // try await loadStudentSubmissions()
                
                print("📚 StudentClassroomView: Loaded classroom '\(classroom.name)' with \(exerciseViewModel.exercises.count) exercises")
            } else {
                await MainActor.run {
                    self.studentClassroom = nil
                    print("📚 StudentClassroomView: Student is not assigned to any classroom")
                }
            }
        } catch {
            await MainActor.run {
                let errorMsg: String

                // Provide more specific error messages based on the type of error
                if let apiError = error as? APIError {
                    switch apiError {
                    case .noData:
                        errorMsg = "No classroom data received from server. Please check your internet connection and try again."
                    case .unauthorized:
                        errorMsg = "Authentication failed. Please log in again."
                    case .networkError(let message):
                        errorMsg = "Network error: \(message). Please check your internet connection."
                    case .decodingError(let message):
                        errorMsg = "Data format error: \(message). Please try again later."
                    case .serverError(let message):
                        errorMsg = "Server error: \(message). Please try again later."
                    case .invalidURL:
                        errorMsg = "Invalid request. Please try again."
                    }
                } else {
                    errorMsg = "Failed to load classroom: \(error.localizedDescription)"
                }

                self.error = errorMsg
                print("❌ StudentClassroomView: \(errorMsg)")
                print("🔍 StudentClassroomView: Error details: \(error)")
            }
        }
        
        await MainActor.run {
            isLoading = false
        }
    }
    
    // MARK: - Helper Functions
    
    // Function removed - exercise interaction disabled in classroom view
    // private func hasCompletedExercise(_ exerciseId: UUID) -> Bool { ... }
    
    // Function removed - exercise interaction disabled in classroom view
    // private func loadStudentSubmissions() async throws { ... }
}

#Preview {
    StudentClassroomView()
} 