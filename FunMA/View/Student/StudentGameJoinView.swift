import SwiftUI
import WebKit
import ARKit
import RealityKit
import ObjectiveC

struct StudentGameJoinView: View, GameServiceDelegate {
    @StateObject private var gameRoomManager = GameRoomManager.shared
    @StateObject private var userManager = UserManager.shared
    @StateObject private var gameService = GameService.shared
    @State private var pinCode: String = ""
    @State private var studentName: String = ""
    @State private var gameURL: String = ""
    @State private var showingGamePlayer = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var showingGameEndedAlert = false
    @State private var gameEndedMessage = ""
    @State private var isJoining = false
    @State private var hasJoinedRoom = false  // Track if student has joined a room
    @State private var isLoadingGameURL = false  // Track URL loading state for late joiners
    @State private var urlLoadingTimeout: Timer?  // Timeout timer for URL loading
    @State private var joinRecoveryTimeout: Timer?  // Timeout to detect stuck join state
    @State private var hasShownGame = false  // Prevent showing game multiple times
    @FocusState private var isPinCodeFocused: Bool
    @FocusState private var isNameFocused: Bool
    
    var body: some View {
        Group {
            if hasJoinedRoom {
                // Show waiting screen when student has joined
                GameWaitingView(
                    gameRoom: gameRoomManager.currentGameRoom,
                    isLoadingGameURL: isLoadingGameURL,
                    onLeaveGame: {
                        leaveGame()
                    }
                )
            } else {
                // Show join form
                joinGameForm
            }
        }
        .fullScreenCover(isPresented: $showingGamePlayer) {
            GamePlayerView(
                gameURL: gameURL,
                gameName: gameRoomManager.currentGameRoom?.game.name ?? "Class Game",
                onClose: {
                    // When game is closed, return directly to join form
                    hasJoinedRoom = false
                    gameRoomManager.leaveGameRoom()
                    gameService.resetGameState()
                    showingGamePlayer = false
                    hasShownGame = false
                }
            )
        }
        .onChange(of: showingGamePlayer) { newValue in
            print("StudentGameJoinView: showingGamePlayer changed to: \(newValue)")
            if newValue {
                print("StudentGameJoinView: About to show GamePlayerView with URL: \(gameURL)")
                print("StudentGameJoinView: Game name: \(gameRoomManager.currentGameRoom?.game.name ?? "Class Game")")

                // Clear any pending alerts since game is now showing successfully
                showingAlert = false
                alertMessage = ""

                // Cancel all timeouts since game is showing
                urlLoadingTimeout?.invalidate()
                urlLoadingTimeout = nil
                joinRecoveryTimeout?.invalidate()
                joinRecoveryTimeout = nil
                isLoadingGameURL = false
            }
        }
        .alert("Join Game", isPresented: $showingAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .alert("Game Ended", isPresented: $showingGameEndedAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(gameEndedMessage)
        }
        .onAppear {
            // Set default name to user's name if available
            if !userManager.currentUser.name.isEmpty {
                studentName = userManager.currentUser.name
            }
            // Focus the PIN field when view appears
            isPinCodeFocused = true

            // Reset any stale game state when view appears
            print("StudentGameJoinView: onAppear - resetting game state")
            gameService.resetGameState()
            gameRoomManager.leaveGameRoom() // Clean up any stale game room state

            // Cancel any existing timeouts
            urlLoadingTimeout?.invalidate()
            urlLoadingTimeout = nil
            joinRecoveryTimeout?.invalidate()
            joinRecoveryTimeout = nil
            isLoadingGameURL = false
            hasShownGame = false
        }
        .onDisappear {
            // Clean up timeouts when view disappears
            urlLoadingTimeout?.invalidate()
            urlLoadingTimeout = nil
            joinRecoveryTimeout?.invalidate()
            joinRecoveryTimeout = nil
        }
        .onReceive(gameRoomManager.$currentGameRoom) { gameRoom in
            print("StudentGameJoinView: onReceive triggered with gameRoom: \(gameRoom?.id ?? "nil")")
            print("StudentGameJoinView: Current user ID: \(userManager.currentUser.id)")
            print("StudentGameJoinView: GameRoom host ID: \(gameRoom?.hostId ?? "nil")")
            print("StudentGameJoinView: GameRoom game URL: \(gameRoom?.game.url ?? "nil")")
            print("StudentGameJoinView: GameRoom game name: \(gameRoom?.game.name ?? "nil")")
            print("StudentGameJoinView: GameRoom game ID: \(gameRoom?.game.id ?? "nil")")
            print("StudentGameJoinView: Game started flag: \(gameRoom?.isGameStarted ?? false)")
            
            // Handle successful join
            if let gameRoom = gameRoom {
                let isHost = isCurrentUserHost(gameRoom)
                print("StudentGameJoinView: isCurrentUserHost check result: \(isHost)")
                print("StudentGameJoinView: gameRoom.hostId: \(gameRoom.hostId)")
                print("StudentGameJoinView: userManager.currentUser.id: \(userManager.currentUser.id)")

                if !isHost {
                    print("StudentGameJoinView: Student joined successfully")
                    hasJoinedRoom = true

                    // Cancel recovery timeout since we successfully joined
                    joinRecoveryTimeout?.invalidate()
                    joinRecoveryTimeout = nil

                    // Store the game URL if available
                    if let roomGameURL = gameRoom.game.url, !roomGameURL.isEmpty {
                        print("StudentGameJoinView: Storing game URL from room: \(roomGameURL)")
                        gameURL = roomGameURL
                    }

                    // If game has already started, handle late joiner scenario
                    print("StudentGameJoinView: Checking if game is started: \(gameRoom.isGameStarted)")
                    print("StudentGameJoinView: Game room status: \(gameRoom.status)")

                    if gameRoom.isGameStarted {
                        print("StudentGameJoinView: Game already started, handling late joiner")

                        // Check if we already have a URL from gameService and can show immediately
                        if let webViewURL = gameService.webViewURL, !webViewURL.isEmpty, !showingGamePlayer {
                            print("StudentGameJoinView: Have gameService URL, showing game immediately")
                            gameURL = webViewURL
                            hasShownGame = true
                            showingGamePlayer = true
                        } else {
                            print("StudentGameJoinView: No immediate URL available, starting late joiner process")
                            handleLateJoinerGameStart(gameRoom: gameRoom)
                        }
                    } else {
                        print("StudentGameJoinView: Game not started yet, student will wait")
                    }
                } else {
                    print("StudentGameJoinView: Current user is the host, not handling as student")
                }
            } else if gameRoom == nil && hasJoinedRoom {
                // Room was closed or left
                print("StudentGameJoinView: Game room is nil, leaving room")
                hasJoinedRoom = false
            }
        }
        .onReceive(gameRoomManager.$error) { error in
            if let error = error {
                print("StudentGameJoinView: Received error: \(error.localizedDescription)")
                print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")

                // Only show error if game is not already showing
                if !showingGamePlayer {
                    alertMessage = error.localizedDescription
                    showingAlert = true
                    isJoining = false
                } else {
                    print("StudentGameJoinView: Game is already showing, ignoring GameRoomManager error")
                }
            }
        }
        .onReceive(gameService.$gameState) { gameState in
            print("StudentGameJoinView: Game state changed to: \(gameState)")
            print("StudentGameJoinView: hasJoinedRoom: \(hasJoinedRoom)")
            print("StudentGameJoinView: Current game room: \(gameRoomManager.currentGameRoom?.id ?? "nil")")
            handleGameStateChange(gameState)
        }
        .onReceive(gameService.$webViewURL) { webViewURL in
            print("StudentGameJoinView: WebView URL updated: \(webViewURL ?? "nil")")
            if let url = webViewURL, !url.isEmpty {
                gameURL = url
            }
        }
    }
    
    private var joinGameForm: some View {
        VStack(spacing: 30) {
            // Header with icon
            VStack(spacing: 12) {
                Image(systemName: "gamecontroller.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                    .padding(.bottom, 8)
                
                Text("Join Class Game")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text("Enter your name and the 6-digit pin code from your teacher")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
            }
            .padding(.top, 40)
            
            // Student Name Input
            VStack(alignment: .leading, spacing: 8) {
                Text("Your Name")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                TextField("Enter your name", text: $studentName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($isNameFocused)
                    .autocapitalization(.words)
                    .disableAutocorrection(true)
            }
            .padding(.horizontal, 40)
            
            // Pin Code Input
            VStack(spacing: 16) {
                Text("Pin Code")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                // Visual PIN display with tap to focus
                HStack(spacing: 12) {
                    ForEach(0..<6) { index in
                        ZStack {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemBackground))
                                .frame(height: 60)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 12)
                                        .stroke(isPinCodeFocused ? Color.blue : Color.gray.opacity(0.3), lineWidth: 2)
                                )
                            
                            if index < pinCode.count {
                                Text(String(pinCode[pinCode.index(pinCode.startIndex, offsetBy: index)]))
                                    .font(.title)
                                    .fontWeight(.semibold)
                            } else {
                                Text("•")
                                    .font(.title)
                                    .foregroundColor(.gray.opacity(0.3))
                            }
                        }
                        .onTapGesture {
                            isPinCodeFocused = true
                        }
                    }
                }
                
                // Hidden text field for keyboard input
                TextField("", text: $pinCode)
                    .keyboardType(.numberPad)
                    .focused($isPinCodeFocused)
                    .opacity(0)
                    .frame(height: 1)
                    .onChange(of: pinCode) { newValue in
                        // Limit to 6 digits and only allow numbers
                        let filteredValue = String(newValue.filter { $0.isNumber }.prefix(6))
                        pinCode = filteredValue
                        
                        // Auto-submit when 6 digits are entered
                        if filteredValue.count == 6 {
                            // Small delay to allow the UI to update
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                isNameFocused = true
                            }
                        }
                    }
            }
            .padding(.horizontal, 40)
            
            Spacer()
            
            // Join Button
            Button(action: joinGame) {
                HStack {
                    if isJoining {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                        Text("Joining...")
                            .font(.headline)
                    } else {
                        Text("Join Game")
                            .font(.headline)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 56)
                .background(
                    pinCode.count == 6 && !studentName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && !isJoining ?
                    Color.blue :
                    Color.gray.opacity(0.3)
                )
                .foregroundColor(.white)
                .cornerRadius(16)
            }
            .disabled(pinCode.count != 6 || studentName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isJoining)
            .padding(.horizontal, 40)
            .padding(.bottom, 40)
        }
    }
    
    private func isCurrentUserHost(_ gameRoom: GameRoom) -> Bool {
        return gameRoom.hostId == userManager.currentUser.id
    }
    
    private func leaveGame() {
        print("StudentGameJoinView: Leaving game")

        // Cancel any URL loading timeout
        urlLoadingTimeout?.invalidate()
        urlLoadingTimeout = nil
        isLoadingGameURL = false

        gameRoomManager.leaveGameRoom()
        gameService.resetGameState()
        hasJoinedRoom = false
        showingGamePlayer = false
        hasShownGame = false
    }

    // MARK: - Late Joiner Handling

    private func handleLateJoinerGameStart(gameRoom: GameRoom) {
        print("StudentGameJoinView: Handling late joiner for active game")
        print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")
        print("StudentGameJoinView: hasShownGame: \(hasShownGame)")
        print("StudentGameJoinView: gameService.webViewURL: \(gameService.webViewURL ?? "nil")")
        print("StudentGameJoinView: gameRoom.game.url: \(gameRoom.game.url ?? "nil")")

        // If game is already showing, don't start the loading process
        if showingGamePlayer {
            print("StudentGameJoinView: Game is already showing, skipping late joiner process")
            return
        }

        // Set loading state
        isLoadingGameURL = true

        // Start timeout timer for URL loading (60 seconds to allow for WebSocket response and network delays)
        urlLoadingTimeout = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: false) { _ in
            print("StudentGameJoinView: URL loading timeout reached")
            // Only show timeout if we're still loading and haven't shown the game
            if self.isLoadingGameURL && !self.showingGamePlayer {
                self.handleURLLoadingTimeout()
            }
        }

        // For late joiners, the server should have already sent gameStarted message with URL
        // The server doesn't handle requestGameUrl, so let's not send it
        print("StudentGameJoinView: Late joiner - waiting for gameStarted message data to be processed...")

        // Wait 2 seconds for WebSocket messages to be processed, then start checking for URL
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.attemptToLoadGameURL(gameRoom: gameRoom, retryCount: 0, maxRetries: 5)
        }
    }

    private func attemptToLoadGameURL(gameRoom: GameRoom, retryCount: Int, maxRetries: Int) {
        print("StudentGameJoinView: Attempting to load game URL (attempt \(retryCount + 1)/\(maxRetries + 1))")

        // Check if we've been cancelled
        guard isLoadingGameURL else {
            print("StudentGameJoinView: URL loading was cancelled, stopping attempts")
            return
        }

        // Try multiple sources for the game URL with priority order
        var candidateURL: String?
        var urlSource: String = ""

        // 1. Check GameService webViewURL first (from gameStarted message)
        if let webViewURL = gameService.webViewURL, !webViewURL.isEmpty {
            candidateURL = webViewURL
            urlSource = "GameService webViewURL (gameStarted message)"
        }
        // 2. Check room's game URL (from room data - this is what server provides)
        else if let roomGameURL = gameRoom.game.url, !roomGameURL.isEmpty {
            candidateURL = roomGameURL
            urlSource = "Room game URL (server provided)"
        }
        // 3. Check local gameURL (fallback)
        else if !gameURL.isEmpty {
            candidateURL = gameURL
            urlSource = "Local gameURL (fallback)"
        }

        if let url = candidateURL {
            print("StudentGameJoinView: Found URL from \(urlSource): \(url)")

            // For late joiners, be more aggressive about using the server-provided URL
            // The WebView can handle URL issues better than our validation
            if urlSource.contains("server provided") && retryCount >= 1 && !showingGamePlayer {
                print("StudentGameJoinView: Using server-provided room URL directly for late joiner (attempt \(retryCount + 1))")

                // Cancel timeout and show the game directly
                urlLoadingTimeout?.invalidate()
                urlLoadingTimeout = nil
                joinRecoveryTimeout?.invalidate()
                joinRecoveryTimeout = nil
                isLoadingGameURL = false
                gameURL = url
                print("StudentGameJoinView: Setting showingGamePlayer = true from server-provided URL")
                hasShownGame = true
                showingGamePlayer = true
                return
            }

            // For gameStarted message URLs, use them immediately
            if urlSource.contains("gameStarted message") && !showingGamePlayer {
                print("StudentGameJoinView: Using gameStarted message URL directly")

                // Cancel timeout and show the game directly
                urlLoadingTimeout?.invalidate()
                urlLoadingTimeout = nil
                joinRecoveryTimeout?.invalidate()
                joinRecoveryTimeout = nil
                isLoadingGameURL = false
                gameURL = url
                print("StudentGameJoinView: Setting showingGamePlayer = true from gameStarted URL")
                hasShownGame = true
                showingGamePlayer = true
                return
            }

            // Validate URL before showing WebView
            Task.detached {
                let (isValid, workingUrl) = await self.gameService.validateGameUrl(url)
                await MainActor.run {
                    // Cancel timeout if we got a valid URL
                    if isValid && !self.showingGamePlayer {
                        self.urlLoadingTimeout?.invalidate()
                        self.urlLoadingTimeout = nil
                        self.joinRecoveryTimeout?.invalidate()
                        self.joinRecoveryTimeout = nil
                        self.isLoadingGameURL = false

                        print("StudentGameJoinView: URL is valid, showing WebView")
                        if let workingUrl = workingUrl, workingUrl != url {
                            self.gameURL = workingUrl
                            print("StudentGameJoinView: Using working URL: \(workingUrl)")
                        } else {
                            self.gameURL = url
                        }
                        print("StudentGameJoinView: Setting showingGamePlayer = true from URL validation")
                        self.hasShownGame = true
                        self.showingGamePlayer = true
                    } else {
                        print("StudentGameJoinView: URL validation failed for \(url)")

                        // For late joiners, if we have a server-provided URL and we've tried once,
                        // just use it anyway - the WebView might be able to handle it
                        if urlSource.contains("server provided") && retryCount >= 0 && !self.showingGamePlayer {
                            print("StudentGameJoinView: Using server-provided URL anyway for late joiner")
                            self.urlLoadingTimeout?.invalidate()
                            self.urlLoadingTimeout = nil
                            self.joinRecoveryTimeout?.invalidate()
                            self.joinRecoveryTimeout = nil
                            self.isLoadingGameURL = false
                            self.gameURL = url
                            print("StudentGameJoinView: Setting showingGamePlayer = true from fallback URL")
                            self.hasShownGame = true
                            self.showingGamePlayer = true
                            return
                        }

                        // Retry if we haven't exceeded max retries
                        if retryCount < maxRetries {
                            let delay = 2.0 + Double(retryCount) * 0.5
                            print("StudentGameJoinView: Retrying URL loading in \(delay) seconds...")
                            DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                                self.attemptToLoadGameURL(gameRoom: gameRoom, retryCount: retryCount + 1, maxRetries: maxRetries)
                            }
                        } else {
                            print("StudentGameJoinView: Max retries exceeded, showing error")
                            self.handleURLLoadingFailure()
                        }
                    }
                }
            }
        } else {
            print("StudentGameJoinView: No URL available yet, waiting for WebSocket response...")

            // Server doesn't handle requestGameUrl, so we just wait and retry
            print("StudentGameJoinView: No URL available yet, waiting for gameStarted message processing...")

            // If no URL is available, wait longer and retry (unless max retries exceeded)
            if retryCount < maxRetries {
                // Use progressive delay: 3s, 4s, 5s, 6s, 7s
                let delay = 3.0 + Double(retryCount)
                print("StudentGameJoinView: Waiting \(delay) seconds before retry...")

                DispatchQueue.main.asyncAfter(deadline: .now() + delay) {
                    self.attemptToLoadGameURL(gameRoom: gameRoom, retryCount: retryCount + 1, maxRetries: maxRetries)
                }
            } else {
                print("StudentGameJoinView: Max retries exceeded, no URL found after waiting for WebSocket")
                handleURLLoadingFailure()
            }
        }
    }

    private func handleURLLoadingTimeout() {
        print("StudentGameJoinView: Handling URL loading timeout")
        print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")

        urlLoadingTimeout?.invalidate()
        urlLoadingTimeout = nil
        isLoadingGameURL = false

        // Only show error if game is not already showing
        if !showingGamePlayer {
            print("StudentGameJoinView: Game not showing, displaying timeout error")
            alertMessage = "The game server is taking too long to respond. Please check your internet connection and try rejoining the game."
            showingAlert = true
        } else {
            print("StudentGameJoinView: Game is already showing, ignoring timeout error")
        }
    }

    private func handleURLLoadingFailure() {
        print("StudentGameJoinView: Handling URL loading failure")
        print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")

        urlLoadingTimeout?.invalidate()
        urlLoadingTimeout = nil
        isLoadingGameURL = false

        // Only show error if game is not already showing
        if !showingGamePlayer {
            print("StudentGameJoinView: Game not showing, displaying connection error")
            alertMessage = "Unable to connect to the game server. Please check your internet connection and try rejoining the game."
            showingAlert = true
        } else {
            print("StudentGameJoinView: Game is already showing, ignoring connection error")
        }
    }

    private func checkAndRecoverFromStuckJoin() {
        print("StudentGameJoinView: Checking for stuck join state")
        print("StudentGameJoinView: hasJoinedRoom: \(hasJoinedRoom)")
        print("StudentGameJoinView: isJoining: \(isJoining)")
        print("StudentGameJoinView: currentGameRoom: \(gameRoomManager.currentGameRoom?.id ?? "nil")")

        // Cancel the recovery timeout
        joinRecoveryTimeout?.invalidate()
        joinRecoveryTimeout = nil

        // If we have a game room but haven't joined, try to recover
        if let gameRoom = gameRoomManager.currentGameRoom, !hasJoinedRoom {
            print("StudentGameJoinView: Detected stuck state - have game room but not joined")

            // Check if the current user is in the players list
            let currentUserId = userManager.currentUser.id
            let isInPlayersList = gameRoom.players.contains { $0.id == currentUserId }

            print("StudentGameJoinView: Current user in players list: \(isInPlayersList)")

            if isInPlayersList {
                print("StudentGameJoinView: Recovering - setting hasJoinedRoom = true")
                hasJoinedRoom = true

                // If the game is active, handle late joiner
                if gameRoom.isGameStarted {
                    print("StudentGameJoinView: Game is active, handling late joiner recovery")
                    handleLateJoinerGameStart(gameRoom: gameRoom)
                }
            } else {
                print("StudentGameJoinView: User not in players list, join may have failed")
                print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")

                // Only show error if game is not already showing
                if !showingGamePlayer {
                    isJoining = false
                    alertMessage = "Failed to join the game room. Please try again."
                    showingAlert = true
                } else {
                    print("StudentGameJoinView: Game is already showing, ignoring join failure error")
                }
            }
        } else if hasJoinedRoom && gameRoomManager.currentGameRoom?.isGameStarted == true && !showingGamePlayer && !isLoadingGameURL {
            print("StudentGameJoinView: Detected stuck state - joined room with active game but not showing player")

            // Try to recover by handling late joiner again
            if let gameRoom = gameRoomManager.currentGameRoom {
                print("StudentGameJoinView: Attempting late joiner recovery")
                hasShownGame = false  // Reset flag to allow showing game again
                handleLateJoinerGameStart(gameRoom: gameRoom)
            }
        }
    }
    
    private func joinGame() {
        guard pinCode.count == 6 && !studentName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        print("StudentGameJoinView: Starting join game with PIN: \(pinCode), Name: \(studentName)")
        isJoining = true
        
        Task.detached {
            do {
                print("StudentGameJoinView: Calling joinGameRoom...")
                try await gameRoomManager.joinGameRoom(
                    pinCode: pinCode,
                    playerId: userManager.currentUser.id,
                    playerName: studentName.trimmingCharacters(in: .whitespacesAndNewlines)
                )
                print("StudentGameJoinView: joinGameRoom completed successfully")

                // Set up a recovery timeout in case the student gets stuck
                await MainActor.run {
                    self.joinRecoveryTimeout = Timer.scheduledTimer(withTimeInterval: 10.0, repeats: false) { _ in
                        print("StudentGameJoinView: Join recovery timeout - checking if student is stuck")
                        self.checkAndRecoverFromStuckJoin()
                    }
                }

                // The roomJoined message will be handled by onReceive
            } catch {
                print("StudentGameJoinView: Error joining game: \(error)")
                
                await MainActor.run {
                    // Check if it's a WebSocket connection error
                    if error.localizedDescription.contains("WebSocket") || 
                       error.localizedDescription.contains("timeout") ||
                       error.localizedDescription.contains("network") ||
                       error.localizedDescription.contains("notConnectedToInternet") {
                        
                        alertMessage = "Connection failed. Please check your internet connection and make sure the WebSocket server is running. Error: \(error.localizedDescription)"
                    } else if error.localizedDescription.contains("Invalid PIN code") {
                        alertMessage = "Invalid PIN code. Please check the 6-digit code from your teacher."
                    } else if error.localizedDescription.contains("Student name is required") {
                        alertMessage = "Please enter your name to join the game."
                    } else if error.localizedDescription.contains("Room is full") {
                        alertMessage = "This game room is full. Maximum 50 players allowed."
                    } else if error.localizedDescription.contains("no longer available") {
                        alertMessage = "This game room is no longer available."
                    } else {
                        alertMessage = error.localizedDescription
                    }
                    
                    showingAlert = true
                    isJoining = false
                }
            }
            
            await MainActor.run {
                isJoining = false
            }
        }
    }
    
    // MARK: - GameServiceDelegate
    func gameService(_ service: GameService, createdRoom room: GameRoom) {
        print("StudentGameJoinView: Room created - PIN: \(room.pinCode)")
    }

    func gameService(_ service: GameService, joinedRoom room: GameRoom) {
        print("StudentGameJoinView: Joined room delegate called - PIN: \(room.pinCode)")
        print("StudentGameJoinView: Game room game URL: \(room.game.url ?? "nil")")
        print("StudentGameJoinView: Game room game name: \(room.game.name)")
        print("StudentGameJoinView: Game room game ID: \(room.game.id)")
        print("StudentGameJoinView: Game room isGameStarted: \(room.isGameStarted)")
        print("StudentGameJoinView: Game room status: \(room.status)")

        // This delegate method should trigger the same logic as the onReceive for currentGameRoom
        // But let's make sure it's working properly for late joiners
        if room.isGameStarted && !isCurrentUserHost(room) {
            print("StudentGameJoinView: Delegate detected late joiner scenario")
            DispatchQueue.main.async {
                // Cancel recovery timeout since we got the room joined callback
                self.joinRecoveryTimeout?.invalidate()
                self.joinRecoveryTimeout = nil

                self.hasJoinedRoom = true
                self.gameURL = room.game.url ?? ""
                self.handleLateJoinerGameStart(gameRoom: room)
            }
        }
    }
    
    func gameService(_ service: GameService, playerJoined player: Player) {
        print("StudentGameJoinView: Player joined delegate called - \(player.username)")
        print("StudentGameJoinView: Player ID: \(player.id)")
        print("StudentGameJoinView: Current user ID: \(userManager.currentUser.id)")
        print("StudentGameJoinView: Is current user: \(player.id == userManager.currentUser.id)")

        // If this is the current user joining and we haven't joined the room yet,
        // this might be the signal that we successfully joined an active game
        if player.id == userManager.currentUser.id && !hasJoinedRoom {
            print("StudentGameJoinView: Current user joined via playerJoined delegate")

            // Check if there's a current game room and if the game is active
            if let gameRoom = gameRoomManager.currentGameRoom, gameRoom.isGameStarted {
                print("StudentGameJoinView: Game is active, handling late joiner via playerJoined")
                DispatchQueue.main.async {
                    // Cancel recovery timeout since we're handling the join
                    self.joinRecoveryTimeout?.invalidate()
                    self.joinRecoveryTimeout = nil

                    self.hasJoinedRoom = true
                    self.handleLateJoinerGameStart(gameRoom: gameRoom)
                }
            } else if let gameRoom = gameRoomManager.currentGameRoom {
                print("StudentGameJoinView: Game not active yet, setting hasJoinedRoom = true")
                DispatchQueue.main.async {
                    // Cancel recovery timeout since we successfully joined
                    self.joinRecoveryTimeout?.invalidate()
                    self.joinRecoveryTimeout = nil

                    self.hasJoinedRoom = true
                }
            }
        }
    }
    
    func gameService(_ service: GameService, playerLeft playerId: String) {
        print("StudentGameJoinView: Player left - ID: \(playerId)")
    }
    
    func gameServiceDidStartGame(_ service: GameService) {
        print("StudentGameJoinView: Game started delegate called")
        print("StudentGameJoinView: GameService webViewURL: \(service.webViewURL ?? "nil")")
        print("StudentGameJoinView: hasJoinedRoom: \(hasJoinedRoom)")
        print("StudentGameJoinView: isLoadingGameURL: \(isLoadingGameURL)")

        // If we have a webViewURL from the gameStarted message, use it immediately
        // For late joiners, we might get this before hasJoinedRoom is set, so be more flexible
        if let webViewURL = service.webViewURL, !webViewURL.isEmpty {
            print("StudentGameJoinView: Got URL from gameStarted message: \(webViewURL)")
            print("StudentGameJoinView: Current hasShownGame: \(hasShownGame)")
            print("StudentGameJoinView: Current showingGamePlayer: \(showingGamePlayer)")
            print("StudentGameJoinView: Current hasJoinedRoom: \(hasJoinedRoom)")

            // Cancel any existing loading timeout
            urlLoadingTimeout?.invalidate()
            urlLoadingTimeout = nil
            joinRecoveryTimeout?.invalidate()
            joinRecoveryTimeout = nil
            isLoadingGameURL = false

            // Set the URL and show the game - always show for gameStarted URLs
            gameURL = webViewURL
            print("StudentGameJoinView: Setting showingGamePlayer = true from gameStarted message")
            print("StudentGameJoinView: Final gameURL: \(gameURL)")
            hasShownGame = true
            showingGamePlayer = true

            // If we haven't joined the room yet, this might be a late joiner scenario
            if !hasJoinedRoom && gameRoomManager.currentGameRoom != nil {
                print("StudentGameJoinView: Late joiner detected, setting hasJoinedRoom = true")
                hasJoinedRoom = true
            }

            return
        }

        // If we're already loading URL for a late joiner, let that process continue
        if isLoadingGameURL {
            print("StudentGameJoinView: Already loading URL for late joiner, skipping duplicate start")
            return
        }

        // Handle game start for students who were waiting
        if hasJoinedRoom, let gameRoom = gameRoomManager.currentGameRoom {
            handleLateJoinerGameStart(gameRoom: gameRoom)
        }
    }
    
    func gameServiceDidEndGame(_ service: GameService) {
        print("StudentGameJoinView: Game ended delegate called")
        // The game state change handler will handle closing the WebView
        // This method is kept for backward compatibility
    }
    
    func gameServiceDidLeaveRoom(_ service: GameService) {
        print("StudentGameJoinView: Left room")
        hasJoinedRoom = false
        showingGamePlayer = false
    }
    
    func gameService(_ service: GameService, didReceiveError error: Error) {
        print("StudentGameJoinView: WebSocket error - \(error.localizedDescription)")
        print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")

        // Only show error if game is not already showing
        if !showingGamePlayer {
            alertMessage = "WebSocket Error: \(error.localizedDescription)"
            showingAlert = true
            isJoining = false
        } else {
            print("StudentGameJoinView: Game is already showing, ignoring WebSocket error")
        }
    }
    
    func gameService(_ service: GameService, receivedGameUrl url: String) {
        print("StudentGameJoinView: Received game URL - \(url)")
        print("StudentGameJoinView: Previous gameURL was: \(gameURL)")
        print("StudentGameJoinView: hasJoinedRoom: \(hasJoinedRoom)")
        print("StudentGameJoinView: isLoadingGameURL: \(isLoadingGameURL)")

        gameURL = url
        print("StudentGameJoinView: Updated gameURL to: \(gameURL)")

        // If we have a game room with active game and a URL, show the game immediately
        // For late joiners, we might get URL before hasJoinedRoom is fully set
        if gameRoomManager.currentGameRoom?.isGameStarted == true && !showingGamePlayer {
            print("StudentGameJoinView: Received URL for active game, showing WebView immediately")
            print("StudentGameJoinView: Current hasShownGame: \(hasShownGame)")
            print("StudentGameJoinView: Current hasJoinedRoom: \(hasJoinedRoom)")

            // Cancel any loading timeouts since we got a URL
            urlLoadingTimeout?.invalidate()
            urlLoadingTimeout = nil
            joinRecoveryTimeout?.invalidate()
            joinRecoveryTimeout = nil
            isLoadingGameURL = false

            // Show the game immediately - the WebView can handle URL validation
            print("StudentGameJoinView: Setting showingGamePlayer = true from receivedGameUrl")
            print("StudentGameJoinView: Final gameURL: \(gameURL)")
            hasShownGame = true
            showingGamePlayer = true

            // Ensure hasJoinedRoom is set for late joiners
            if !hasJoinedRoom && gameRoomManager.currentGameRoom != nil {
                print("StudentGameJoinView: Late joiner detected in receivedGameUrl, setting hasJoinedRoom = true")
                hasJoinedRoom = true
            }
        }
    }
    
    private func handleGameStateChange(_ gameState: GameService.GameState) {
        print("StudentGameJoinView: Handling game state change to: \(gameState)")
        
        switch gameState {
        case .waiting:
            // Game is waiting for players or teacher to start
            print("StudentGameJoinView: Game is in waiting state")
            showingGamePlayer = false
            
        case .active:
            // Game has started, open WebView if we have a URL
            print("StudentGameJoinView: Game is active, checking for WebView URL")
            print("StudentGameJoinView: gameURL: \(gameURL)")
            print("StudentGameJoinView: gameService.webViewURL: \(gameService.webViewURL ?? "nil")")
            print("StudentGameJoinView: hasShownGame: \(hasShownGame)")
            print("StudentGameJoinView: showingGamePlayer: \(showingGamePlayer)")

            if !gameURL.isEmpty && !showingGamePlayer {
                print("StudentGameJoinView: Opening WebView with URL: \(gameURL)")
                hasShownGame = true
                showingGamePlayer = true
            } else if let webViewURL = gameService.webViewURL, !webViewURL.isEmpty, !showingGamePlayer {
                print("StudentGameJoinView: Using GameService WebView URL: \(webViewURL)")
                gameURL = webViewURL
                hasShownGame = true
                showingGamePlayer = true
            } else if showingGamePlayer {
                print("StudentGameJoinView: Game already showing")
            } else {
                print("StudentGameJoinView: No WebView URL available for active game")
                // Don't show error immediately, let the late joiner logic handle it
            }
            
        case .completed:
            // Game has ended, close WebView and return to join game view
            // Only show the alert if the user has actually joined a room
            print("StudentGameJoinView: Game completed, hasJoinedRoom: \(hasJoinedRoom)")
            showingGamePlayer = false
            
            if hasJoinedRoom {
                // User was in a room, show the game ended alert
                hasJoinedRoom = false  // Exit waiting room
                gameRoomManager.leaveGameRoom()  // Clean up game room state
                gameEndedMessage = "The game has ended. You can join another game."
                showingGameEndedAlert = true
            } else {
                // User wasn't in a room, just reset the game state silently
                print("StudentGameJoinView: User wasn't in a room, silently resetting game state")
                gameRoomManager.leaveGameRoom()  // Clean up any stale state
            }
            
        case .cancelled:
            // Game was cancelled, close WebView and return to join game view
            // Only show the alert if the user has actually joined a room
            print("StudentGameJoinView: Game cancelled, hasJoinedRoom: \(hasJoinedRoom)")
            showingGamePlayer = false
            
            if hasJoinedRoom {
                // User was in a room, show the game cancelled alert
                hasJoinedRoom = false  // Exit waiting room
                gameRoomManager.leaveGameRoom()  // Clean up game room state
                gameEndedMessage = "The game was cancelled. You can join another game."
                showingGameEndedAlert = true
            } else {
                // User wasn't in a room, just reset the game state silently
                print("StudentGameJoinView: User wasn't in a room, silently resetting game state")
                gameRoomManager.leaveGameRoom()  // Clean up any stale state
            }
        }
    }
}

// MARK: - Game Waiting View
struct GameWaitingView: View {
    let gameRoom: GameRoom?
    let isLoadingGameURL: Bool
    let onLeaveGame: () -> Void
    
    var body: some View {
        ZStack {
            // Background
            Color(.systemGroupedBackground)
                .ignoresSafeArea()
            
            VStack {
                // Top section with PIN code in top right
                HStack {
                    Spacer()
                    if let gameRoom = gameRoom {
                        VStack(spacing: 4) {
                            Text("GAME ROOM PIN")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text(gameRoom.pinCode)
                                .font(.system(size: 24, weight: .bold, design: .monospaced))
                                .foregroundColor(.blue)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                        .padding(.top, 20)
                        .padding(.trailing, 20)
                    }
                }
                
                Spacer()
                
                // Center section with waiting message
                VStack(spacing: 30) {
                    // Header
                    VStack(spacing: 16) {
                        Image(systemName: "gamecontroller.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.blue)

                        if isLoadingGameURL {
                            Text("Loading Game...")
                                .font(.title)
                                .fontWeight(.bold)

                            Text("Waiting for game URL from server...")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                        } else if gameRoom?.isGameStarted == true {
                            Text("Game in Progress")
                                .font(.title)
                                .fontWeight(.bold)

                            Text("The game has started. You'll join shortly.")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                        } else {
                            Text("Waiting for Teacher to Start Game")
                                .font(.title)
                                .fontWeight(.bold)

                            Text("You have successfully joined the game room")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                        }
                    }

                    // Waiting Animation
                    VStack(spacing: 16) {
                        ProgressView()
                            .scaleEffect(1.2)
                            .progressViewStyle(CircularProgressViewStyle(tint: .blue))

                        if isLoadingGameURL {
                            Text("Connecting to game server...")
                                .font(.caption)
                                .foregroundColor(.gray)
                                .multilineTextAlignment(.center)
                        }
                    }
                }
                
                Spacer()
                
                // Bottom section with leave button
                Button(action: onLeaveGame) {
                    Text("Leave Game")
                        .font(.headline)
                        .foregroundColor(.red)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.red, lineWidth: 1)
                        )
                }
                .padding(.horizontal, 40)
                .padding(.bottom, 40)
            }
        }
    }
}

// MARK: - Native Game Player View
struct NativeGamePlayerView: View {
    let gameURL: String
    let gameName: String
    let onClose: () -> Void
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Display the native game view based on the URL
                nativeGameView
                    .frame(width: geometry.size.width, height: geometry.size.height)
                    .edgesIgnoringSafeArea(.all)
                
                // Close button overlay for all AR games
                VStack {
                    HStack {
                        Spacer()
                        Button(action: {
                            onClose()
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                        }
                        .padding(.top, 50)
                        .padding(.trailing, 20)
                    }
                    Spacer()
                }
            }
        }
        .ignoresSafeArea(.all, edges: .all)
        .onAppear {
            print("NativeGamePlayerView: onAppear with URL: \(gameURL)")
        }
    }
    
    @ViewBuilder
    private var nativeGameView: some View {
        // Use the new simplified native game registry
        if let gameView = NativeGameRegistry.createGameView(from: gameURL) {
            gameView
        } else {
            // Fallback for invalid URLs
            VStack(spacing: 16) {
                Image(systemName: "exclamationmark.triangle")
                    .font(.system(size: 50))
                    .foregroundColor(.red)
                
                Text("Invalid Native Game URL")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("The game URL format is invalid.")
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                Button("Close") {
                    onClose()
                }
                .buttonStyle(.borderedProminent)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(.systemBackground))
        }
    }
}

// MARK: - AR View Debug View (Removed - No longer needed with simplified URL handling)

// MARK: - Game Player View
struct GamePlayerView: View {
    let gameURL: String
    let gameName: String
    let onClose: () -> Void
    @Environment(\.dismiss) private var dismiss
    @State private var isLoading = true
    @State private var errorMessage: String?
    @State private var loadingTimeout: Timer?
    
    var body: some View {
        // Check if this is a native game URL
        if GameService.shared.isNativeGameUrl(gameURL) {
            NativeGamePlayerView(gameURL: gameURL, gameName: gameName, onClose: onClose)
        } else {
            // Use the existing WebView for web-based games
            GeometryReader { geometry in
                ZStack {
                    // Game WebView with loading overlay
                    ZStack {
                        // Always show the WebView
                        GameWebView(url: gameURL, isLoading: $isLoading, errorMessage: $errorMessage)
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .edgesIgnoringSafeArea(.all)
                        
                        // Show loading overlay when loading
                        if isLoading {
                            VStack(spacing: 16) {
                                ProgressView()
                                    .scaleEffect(1.5)
                                Text("Loading game...")
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(Color.black)
                        }
                        
                        // Show error overlay when there's an error
                        if let errorMessage = errorMessage {
                            VStack(spacing: 16) {
                                Image(systemName: "exclamationmark.triangle")
                                    .font(.system(size: 50))
                                    .foregroundColor(.red)
                                
                                Text("Error Loading Game")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                
                                Text(errorMessage)
                                    .font(.body)
                                    .foregroundColor(.gray)
                                    .multilineTextAlignment(.center)
                                    .padding(.horizontal)
                                
                                Button("Close") {
                                    print("GamePlayerView: Close button tapped")
                                    onClose()
                                }
                                .buttonStyle(.borderedProminent)
                            }
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .background(Color.black)
                        }
                        
                        // Close button for easy exit
                        VStack {
                            HStack {
                                Spacer()
                                Button(action: {
                                    print("GamePlayerView: Close button tapped")
                                    onClose()
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.system(size: 30))
                                        .foregroundColor(.white)
                                        .background(Color.black.opacity(0.5))
                                        .clipShape(Circle())
                                }
                                .padding(.top, 50)
                                .padding(.trailing, 20)
                            }
                            Spacer()
                        }
                    }
                }
            }
            .ignoresSafeArea(.all, edges: .all)
            .onAppear {
                print("GamePlayerView: onAppear with URL: \(gameURL), gameName: \(gameName)")
                if GameService.shared.isNativeGameUrl(gameURL) {
                    print("GamePlayerView: Using NativeGamePlayerView for native game URL")
                } else {
                    print("GamePlayerView: Using WebView for web-based game URL")
                }
                loadGame()
            }
            .onDisappear {
                // Clean up timeout when view disappears
                loadingTimeout?.invalidate()
                loadingTimeout = nil
            }
            .onChange(of: isLoading) { newValue in
                if newValue {
                    print("GamePlayerView: Showing loading overlay")
                } else {
                    print("GamePlayerView: Hiding loading overlay")
                    // Cancel timeout when loading finishes
                    loadingTimeout?.invalidate()
                    loadingTimeout = nil
                }
            }
            .onChange(of: errorMessage) { newValue in
                if let error = newValue {
                    print("GamePlayerView: Showing error overlay: \(error)")
                }
            }
        }
    }
    
    private func loadGame() {
        print("GamePlayerView: loadGame called")
        isLoading = true
        errorMessage = nil

        // Set up a timeout for webview loading (60 seconds to allow for slow networks)
        loadingTimeout = Timer.scheduledTimer(withTimeInterval: 60.0, repeats: false) { _ in
            print("GamePlayerView: Loading timeout reached")
            // Only show error if still loading and no error already shown
            if self.isLoading && self.errorMessage == nil {
                self.isLoading = false
                self.errorMessage = "Game took too long to load. Please check your internet connection and try again."
            }
        }
    }
}

// MARK: - Game WebView
struct GameWebView: UIViewRepresentable {
    let url: String
    @Binding var isLoading: Bool
    @Binding var errorMessage: String?
    
    func makeUIView(context: Context) -> WKWebView {
        print("GameWebView: makeUIView called with URL: \(url)")
        
        // Configure WKWebView for fullscreen experience with better network settings
        let configuration = WKWebViewConfiguration()
        configuration.allowsInlineMediaPlayback = true
        configuration.mediaTypesRequiringUserActionForPlayback = []
        
        print("GameWebView: Basic configuration set")
        
        // Add better network configuration
        let preferences = WKWebpagePreferences()
        preferences.allowsContentJavaScript = true
        configuration.defaultWebpagePreferences = preferences
        
        print("GameWebView: Webpage preferences configured")
        
        // Configure process pool for better network handling
        let processPool = WKProcessPool()
        configuration.processPool = processPool
        
        print("GameWebView: Process pool configured")
        
        let webView = WKWebView(frame: .zero, configuration: configuration)
        print("GameWebView: WKWebView created")
        
        webView.navigationDelegate = context.coordinator
        webView.scrollView.contentInsetAdjustmentBehavior = .never
        webView.scrollView.bounces = false
        webView.scrollView.showsVerticalScrollIndicator = false
        webView.scrollView.showsHorizontalScrollIndicator = false
        webView.backgroundColor = .black
        webView.isOpaque = false
        
        print("GameWebView: WebView properties configured")
        
        // Additional fullscreen optimizations
        webView.scrollView.contentInset = .zero
        webView.scrollView.scrollIndicatorInsets = .zero
        webView.scrollView.automaticallyAdjustsScrollIndicatorInsets = false
        
        print("GameWebView: makeUIView completed successfully")
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        print("GameWebView: updateUIView called with URL: \(url)")
        guard let url = URL(string: url) else {
            print("GameWebView: ERROR - Invalid URL: \(url)")
            errorMessage = "Invalid URL format: \(url)"
            isLoading = false
            return
        }
        
        print("GameWebView: URL is valid, creating request")
        
        // Create a more robust URL request with better timeout and cache settings
        var request = URLRequest(url: url)
        request.cachePolicy = .reloadIgnoringLocalCacheData
        request.timeoutInterval = 60 // Increased timeout
        request.httpShouldHandleCookies = true
        
        // Add headers for better compatibility
        request.setValue("Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1", forHTTPHeaderField: "User-Agent")
        
        print("GameWebView: Request configured, about to load URL: \(url)")
        print("GameWebView: Request headers: \(request.allHTTPHeaderFields ?? [:])")
        
        // Move the webView.load operation to a background thread to avoid blocking the main thread
        DispatchQueue.global(qos: .userInitiated).async {
            DispatchQueue.main.async {
                webView.load(request)
                print("GameWebView: load() called on WebView")
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        print("GameWebView: makeCoordinator called")
        return Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate {
        let parent: GameWebView
        
        init(_ parent: GameWebView) {
            self.parent = parent
            print("GameWebView Coordinator: Initialized")
        }
        
        func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
            print("GameWebView: didStartProvisionalNavigation - Navigation started")
        }
        
        func webView(_ webView: WKWebView, didCommit navigation: WKNavigation!) {
            print("GameWebView: didCommit - Navigation committed")
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            print("GameWebView: didFinish - Page loaded successfully")
            parent.isLoading = false
            parent.errorMessage = nil
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            print("GameWebView: didFail - Page load failed: \(error.localizedDescription)")
            print("GameWebView: Error domain: \(error._domain), code: \(error._code)")
            parent.isLoading = false
            
            // Provide more specific error messages
            if error.localizedDescription.contains("timed out") {
                parent.errorMessage = "Game took too long to load. Please check your internet connection."
            } else if error.localizedDescription.contains("not found") {
                parent.errorMessage = "Game not found. The URL may be incorrect or the game has been removed."
            } else if error.localizedDescription.contains("cannot connect") {
                parent.errorMessage = "Cannot connect to game server. Please check your internet connection."
            } else if error.localizedDescription.contains("network") {
                parent.errorMessage = "Network error. Please check your internet connection and try again."
            } else {
                parent.errorMessage = "Failed to load game: \(error.localizedDescription)"
            }
        }
        
        func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
            print("GameWebView: didFailProvisionalNavigation - Provisional navigation failed: \(error.localizedDescription)")
            print("GameWebView: Error domain: \(error._domain), code: \(error._code)")
            parent.isLoading = false
            
            // Provide more specific error messages
            if error.localizedDescription.contains("timed out") {
                parent.errorMessage = "Game took too long to load. Please check your internet connection."
            } else if error.localizedDescription.contains("not found") {
                parent.errorMessage = "Game not found. The URL may be incorrect or the game has been removed."
            } else if error.localizedDescription.contains("cannot connect") {
                parent.errorMessage = "Cannot connect to game server. Please check your internet connection."
            } else if error.localizedDescription.contains("network") {
                parent.errorMessage = "Network error. Please check your internet connection and try again."
            } else {
                parent.errorMessage = "Failed to load game: \(error.localizedDescription)"
            }
        }
        
        // Handle authentication challenges
        func webView(_ webView: WKWebView, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
            print("GameWebView: didReceive challenge - Authentication challenge received")
            print("GameWebView: Challenge type: \(challenge.protectionSpace.authenticationMethod)")
            
            // Accept all certificates for development/testing
            if challenge.protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
                if let trust = challenge.protectionSpace.serverTrust {
                    // Perform trust evaluation on background queue to avoid blocking main thread
                    DispatchQueue.global(qos: .userInitiated).async {
                        let credential = URLCredential(trust: trust)
                        print("GameWebView: Using credential for server trust")
                        
                        // Call completion handler on main queue
                        DispatchQueue.main.async {
                            completionHandler(.useCredential, credential)
                        }
                    }
                    return
                }
            }
            print("GameWebView: Using default handling for challenge")
            completionHandler(.performDefaultHandling, nil)
        }
    }
}

// MARK: - Native Game Debug View
struct NativeGameDebugView: View {
    @State private var selectedGameName: String = ""
    @State private var showingSelectedGame = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "gamecontroller.fill")
                        .font(.system(size: 50))
                        .foregroundColor(.blue)
                    
                    Text("Native Games Debug")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Available Games: \(NativeGameRegistry.getAvailableGameNames().count)")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
                .padding(.top)
                
                // Game List
                ScrollView {
                    LazyVStack(spacing: 12) {
                        ForEach(NativeGameRegistry.getAvailableGameNames(), id: \.self) { gameName in
                            Button(action: {
                                selectedGameName = gameName
                                showingSelectedGame = true
                            }) {
                                HStack {
                                    Image(systemName: "gamecontroller")
                                        .foregroundColor(.blue)
                                    
                                    Text(gameName.capitalized)
                                        .font(.body)
                                        .foregroundColor(.primary)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .foregroundColor(.gray)
                                        .font(.caption)
                                }
                                .padding()
                                .background(Color(.systemGray6))
                                .cornerRadius(8)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
            }
            .navigationTitle("Native Games")
            .navigationBarTitleDisplayMode(.inline)
        }
        .fullScreenCover(isPresented: $showingSelectedGame) {
            if let gameView = NativeGameRegistry.createGameView(from: "funma-game://\(selectedGameName)") {
                ZStack {
                    gameView
                    
                    VStack {
                        HStack {
                            Button("Close") {
                                showingSelectedGame = false
                            }
                            .padding()
                            .background(Color.black.opacity(0.7))
                            .foregroundColor(.white)
                            .cornerRadius(8)
                            
                            Spacer()
                        }
                        .padding()
                        
                        Spacer()
                    }
                }
            }
        }
    }
}

struct StudentGameJoinView_Previews: PreviewProvider {
    static var previews: some View {
        StudentGameJoinView()
    }
}

// MARK: - Native Game Registry
struct NativeGameRegistry {
    /// Create a native game view based on the funma-game:// URL
    static func createGameView(from url: String) -> AnyView? {
        // Extract the game name from funma-game:// URL
        guard let gameName = extractGameName(from: url) else {
            print("NativeGameRegistry: Invalid URL format: \(url)")
            return nil
        }
        
        print("NativeGameRegistry: Creating view for game: \(gameName)")
        
        // Handle game views with simple case statements
        switch gameName.lowercased() {
        case "trianglefoldingview", "trianglefolding", "triangle":
            return AnyView(TriangleFoldingView())
        case "coneview", "cone":
            return AnyView(ConeView())
        case "cubeview", "cube":
            return AnyView(CubeView())
        case "sphereview", "sphere":
            return AnyView(SphereView())
        case "arsphereview", "arsphere":
            return AnyView(ARSphereView())
        case "arcubeview", "arcube":
            return AnyView(ARCubeView())
        case "arview":
            return AnyView(ARView())
        case "shapeselectionview", "shapeselection", "shapes":
            return AnyView(ShapeSelectionView())
        case "arcontainerview", "arcontainer":
            return AnyView(ARContainerView())
        case "questionview", "question":
            return AnyView(QuestionView(selectedShape: "Cube"))
        case "unfoldgameview", "unfoldgame", "unfold":
            return AnyView(UnfoldGameView())
        case "volumeexplorerview", "volumeexplorer", "volume":
            return AnyView(VolumeExplorerView())
        default:
            print("NativeGameRegistry: Unknown game name: \(gameName)")
            return createFallbackView(for: gameName)
        }
    }
    
    /// Extract game name from funma-game:// URL
    private static func extractGameName(from url: String) -> String? {
        guard url.hasPrefix("funma-game://") else { return nil }
        let gameName = url.replacingOccurrences(of: "funma-game://", with: "")
        return gameName.isEmpty ? nil : gameName
    }
    
    /// Create a fallback view for unknown games
    private static func createFallbackView(for gameName: String) -> AnyView {
        return AnyView(
            VStack(spacing: 20) {
                Image(systemName: "gamecontroller")
                    .font(.system(size: 60))
                    .foregroundColor(.blue)
                
                Text("Game Not Found")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("The game '\(gameName)' is not available.")
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                
                Text("Available games: Triangle, Cone, Cube, Sphere, Unfold, Volume, Shapes")
                    .font(.caption)
                    .foregroundColor(.orange)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(.systemBackground))
        )
    }
    
    /// Get list of available game names
    static func getAvailableGameNames() -> [String] {
        return [
            "trianglefolding", "cone", "cube", "sphere", 
            "arsphere", "arcube", "arview", "shapes", 
            "arcontainer", "question", "unfold", "volume"
        ]
    }
    
    /// Check if a game is available
    static func isGameAvailable(_ gameName: String) -> Bool {
        return getAvailableGameNames().contains(gameName.lowercased())
    }
}
