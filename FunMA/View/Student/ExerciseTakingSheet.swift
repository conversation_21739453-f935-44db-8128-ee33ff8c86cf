import SwiftUI
import PencilKit

struct ExerciseTakingSheet: View {
    let exercise: Exercise
    @ObservedObject var viewModel: ExerciseViewModel
    let onDismiss: () -> Void
    
    @State private var currentQuestionIndex = 0
    @State private var answers: [QuestionSubmission] = []
    @State private var showingConfirmation = false
    @State private var showingResults = false
    @State private var startTime = Date()
    @StateObject private var metamateManager = MetamateManager()
    
    // Draft area state
    @State private var canvasView = PKCanvasView()
    @State private var drawing = PKDrawing()
    @State private var isDraftAreaExpanded = false
    @State private var answerImage: UIImage?
    @State private var answerDrawing: PKDrawing?
    @State private var answerType: AnswerSubmissionType = .none
    
    var body: some View {
        NavigationStack {
            VStack {
                if showingResults {
                    resultsView
                } else if exercise.questions.isEmpty {
                    emptyQuestionsView
                } else if currentQuestionIndex >= exercise.questions.count {
                    outOfBoundsView
                } else {
                    mainQuestionView
                }
            }
            .onAppear {
                print("🎯 MAIN VSTACK: ExerciseTakingSheet main VStack appeared")
                print("🎯 MAIN VSTACK: Exercise: \(exercise.title)")
                print("🎯 MAIN VSTACK: Questions count: \(exercise.questions.count)")
                print("🎯 MAIN VSTACK: showingResults: \(showingResults)")
                print("🎯 MAIN VSTACK: currentQuestionIndex: \(currentQuestionIndex)")
                
                // Initialize draft area for the first question
                resetDraftAreaForNewQuestion()
                
                // Start tracking the first question when the sheet appears
                if !exercise.questions.isEmpty && currentQuestionIndex < exercise.questions.count {
                    metamateManager.startTrackingQuestion(question: exercise.questions[currentQuestionIndex])
                }
            }
            .background(Color(.systemBackground))
            .navigationTitle(exercise.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarLeading) {
                    Button("Exit") {
                        metamateManager.hideBot()
                        onDismiss()
                    }
                }
            }
            .alert("Submit Exercise?", isPresented: $showingConfirmation) {
                Button("Cancel", role: .cancel) { }
                Button("Submit") {
                    if !exercise.questions.isEmpty {
                        metamateManager.hideBot()
                        submitExercise()
                    } else {
                        metamateManager.hideBot()
                        onDismiss()
                    }
                }
            } message: {
                Text("Are you sure you want to submit your answers? This action cannot be undone.")
            }
            .overlay(
                // Add Metamate chatbot overlay positioned above navigation buttons
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        if !showingResults {
                            MetamateView(
                                metamateManager: metamateManager,
                                currentQuestion: !exercise.questions.isEmpty && currentQuestionIndex < exercise.questions.count 
                                    ? exercise.questions[currentQuestionIndex] 
                                    : nil
                            )
                            .padding(.trailing, 16)
                            .padding(.bottom, 80) // Position above navigation buttons
                        }
                    }
                }
            )
        }
        .onAppear {
            // Debug logging
            print("📚 ExerciseTakingSheet: Exercise loaded")
            print("📚 Exercise ID: \(exercise.id)")
            print("📚 Exercise Title: \(exercise.title)")
            print("📚 Number of questions: \(exercise.questions.count)")
            print("📚 showingResults: \(showingResults)")
            print("📚 currentQuestionIndex: \(currentQuestionIndex)")
            print("📚 Questions isEmpty: \(exercise.questions.isEmpty)")
            print("📚 Current question index >= count: \(currentQuestionIndex >= exercise.questions.count)")
            
            // Debug the current UI state
            if showingResults {
                print("📚 UI STATE: Showing results view")
            } else if exercise.questions.isEmpty {
                print("📚 UI STATE: Showing empty questions view")
            } else if currentQuestionIndex >= exercise.questions.count {
                print("📚 UI STATE: Showing out of bounds question view")
            } else {
                print("📚 UI STATE: Showing normal question view")
                print("📚 Current question: \(exercise.questions[currentQuestionIndex].questionText)")
            }
            
            // Log each question for debugging
            for (index, question) in exercise.questions.enumerated() {
                print("📚 Question \(index + 1): \(question.questionText)")
                print("📚 Question Type: \(question.questionType)")
                if let options = question.options {
                    print("📚 Options: \(options)")
                } else {
                    print("📚 Options: nil")
                }
            }
        }
        .onDisappear {
            metamateManager.hideBot()
        }
    }
    
    // MARK: - Computed View Properties
    
    private var resultsView: some View {
        ExerciseResultsView(
            exercise: exercise,
            submissions: answers,
            submission: nil, // No full submission available in taking sheet
            onDismiss: onDismiss
        )
        .onAppear {
            print("🎯 Showing results view")
        }
    }
    
    private var emptyQuestionsView: some View {
        VStack(spacing: 20) {
            Image(systemName: "questionmark.circle")
                .font(.system(size: 50))
                .foregroundColor(.orange)
            
            Text("No Questions Available")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("This exercise doesn't have any questions yet. Please contact your teacher.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Close") {
                metamateManager.hideBot()
                onDismiss()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
        .onAppear {
            print("🎯 Exercise has no questions - showing empty state")
        }
    }
    
    private var outOfBoundsView: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle")
                .font(.system(size: 50))
                .foregroundColor(.red)
            
            Text("Question Not Found")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("There was an error loading this question. Please try again.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            Button("Close") {
                metamateManager.hideBot()
                onDismiss()
            }
            .buttonStyle(.borderedProminent)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
        .onAppear {
            print("🎯 Question index out of bounds - showing error state")
        }
    }
    
    private var mainQuestionView: some View {
        VStack(spacing: 16) {
            progressView
            questionScrollView
            navigationView
        }
    }
    
    private var progressView: some View {
        ProgressView(value: Double(currentQuestionIndex + 1), total: Double(exercise.questions.count))
            .padding(.horizontal)
            .onAppear {
                print("🎯 PROGRESS: Showing progress view")
                print("🎯 PROGRESS: \(currentQuestionIndex + 1) of \(exercise.questions.count)")
            }
    }
    
    private var questionScrollView: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                questionHeader
                questionContent
                answerSection
                draftArea
            }
            .padding()
            .onAppear {
                print("🎯 QUESTION CONTAINER: Showing question container VStack")
            }
        }
        .onAppear {
            print("🎯 SCROLL VIEW: Showing question ScrollView")
            print("🎯 Current question: \(exercise.questions[currentQuestionIndex].questionText)")
            print("🎯 Question type: \(exercise.questions[currentQuestionIndex].questionType.rawValue)")
        }
    }
    
    private var questionHeader: some View {
        Text("Question \(currentQuestionIndex + 1) of \(exercise.questions.count)")
            .font(.headline)
            .foregroundColor(.gray)
            .onAppear {
                print("🎯 QUESTION HEADER: Showing question header")
            }
    }
    
    private var questionContent: some View {
        Group {
            let question = exercise.questions[currentQuestionIndex]
            QuestionTextRenderer(text: question.questionText, fontSize: 20)
                .onAppear {
                    print("🎯 QUESTION TEXT: Showing question text: \(question.questionText)")
                }
        }
    }
    
    private var answerSection: some View {
        Group {
            let question = exercise.questions[currentQuestionIndex]
            
            if question.questionType == .multipleChoice {
                multipleChoiceSection(question: question)
            } else if question.questionType == .longQuestion {
                longAnswerSection(question: question)
            }
        }
    }
    
    private func multipleChoiceSection(question: Question) -> some View {
        MultipleChoiceView(
            options: question.options ?? [],
            selectedAnswer: binding(for: question.id),
            correctAnswerIndex: question.correctAnswerIndex,
            onAnswerChanged: {
                metamateManager.onAnswerChanged(question: question)
            }
        )
        .onAppear {
            print("🎯 MULTIPLE CHOICE: Showing multiple choice view")
            print("🎯 MULTIPLE CHOICE: Options: \(question.options ?? [])")
            print("🎯 MULTIPLE CHOICE: Correct Answer Index: \(question.correctAnswerIndex)")
        }
    }
    
    private func longAnswerSection(question: Question) -> some View {
        LongAnswerView(
            answer: binding(for: question.id),
            questionId: question.id,
            answerType: $answerType,
            answerImage: $answerImage,
            answerDrawing: $answerDrawing
        )
        .onAppear {
            print("🎯 LONG QUESTION: Showing long answer view")
        }
    }
    
    private var draftArea: some View {
        let question = exercise.questions[currentQuestionIndex]
        
        let baseView = StudentDraftAreaView(
            canvasView: $canvasView,
            drawing: $drawing,
            answerImage: $answerImage,
            answerDrawing: $answerDrawing,
            answerType: $answerType,
            questionType: question.questionType,
            isExpanded: isDraftAreaExpanded,
            onToggleExpanded: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isDraftAreaExpanded.toggle()
                }
            }
        )
        
        let viewWithAnswerTypeChange = baseView
            .onChange(of: answerType) { newValue in
                if question.questionType == .longQuestion {
                    updateLongQuestionAnswer(for: question.id, type: newValue)
                }
            }
        
        let viewWithDrawingChange = viewWithAnswerTypeChange
            .onChange(of: answerDrawing) { newValue in
                if question.questionType == .longQuestion && answerType == .drawing {
                    updateLongQuestionAnswer(for: question.id, type: .drawing)
                }
            }
        
        return viewWithDrawingChange
            .onChange(of: answerImage) { newValue in
                if question.questionType == .longQuestion && answerType == .photo {
                    updateLongQuestionAnswer(for: question.id, type: .photo)
                }
            }
    }
    
    private var navigationView: some View {
        HStack {
            if currentQuestionIndex > 0 {
                Button("Previous") {
                    navigateToPrevious()
                }
            }
            
            Spacer()
            
            if currentQuestionIndex < exercise.questions.count - 1 {
                Button("Next") {
                    navigateToNext()
                }
                .buttonStyle(.borderedProminent)
            } else {
                Button("Submit") {
                    showingConfirmation = true
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .padding()
    }
    
    // MARK: - Navigation Helper Functions
    
    private func navigateToPrevious() {
        withAnimation {
            currentQuestionIndex -= 1
        }
        resetDraftAreaForNewQuestion()
        trackCurrentQuestion()
    }
    
    private func navigateToNext() {
        withAnimation {
            currentQuestionIndex += 1
        }
        resetDraftAreaForNewQuestion()
        trackCurrentQuestion()
    }
    
    private func trackCurrentQuestion() {
        if !exercise.questions.isEmpty && currentQuestionIndex < exercise.questions.count {
            metamateManager.startTrackingQuestion(question: exercise.questions[currentQuestionIndex])
        }
    }
    
    private func binding(for questionId: UUID) -> Binding<String> {
        Binding(
            get: {
                answers.first { $0.questionId == questionId }?.answer ?? ""
            },
            set: { newValue in
                updateAnswer(for: questionId, with: newValue)
            }
        )
    }
    
    private func updateAnswer(for questionId: UUID, with newValue: String) {
        if let index = answers.firstIndex(where: { $0.questionId == questionId }) {
            answers[index].answer = newValue
        } else {
            answers.append(QuestionSubmission(questionId: questionId, answer: newValue))
        }
    }
    
    private func submitExercise() {
        print("📝 ExerciseTakingView: Starting submitExercise")
        print("📝 ExerciseTakingView: Exercise ID: \(exercise.id.uuidString)")
        print("📝 ExerciseTakingView: Student ID: \(UserManager.shared.currentUser.id)")
        print("📝 ExerciseTakingView: Number of answers: \(answers.count)")
        print("📝 ExerciseTakingView: Start time: \(startTime)")
        print("📝 ExerciseTakingView: End time: \(Date())")
        
        // Log each answer for debugging
        for (index, answer) in answers.enumerated() {
            print("📝 ExerciseTakingView: Answer \(index + 1):")
            print("  - Question ID: \(answer.questionId.uuidString)")
            print("  - Answer: \(answer.answer)")
        }
        
        let submission = StudentSubmission(
            exerciseId: exercise.id,
            studentId: UserManager.shared.currentUser.id,
            answers: answers,
            startTime: startTime
        )
        
        // Set endTime after initialization
        var submissionWithEndTime = submission
        submissionWithEndTime.endTime = Date()
        
        print("📝 ExerciseTakingView: Created submission with ID: \(submissionWithEndTime.id.uuidString)")
        print("📝 ExerciseTakingView: Calling viewModel.submitSubmission...")
        
        Task {
            do {
                try await viewModel.submitSubmission(submissionWithEndTime)
                print("📝 ExerciseTakingView: submitSubmission completed successfully")
                await MainActor.run {
                    showingResults = true
                    print("📝 ExerciseTakingView: Showing results view")
                }
            } catch {
                print("❌ ExerciseTakingView: submitSubmission failed with error: \(error.localizedDescription)")
                await MainActor.run {
                    // You might want to show an error alert here
                    print("❌ ExerciseTakingView: Error occurred during submission")
                }
            }
        }
    }
    
    private func timeString(from timeInterval: TimeInterval) -> String {
        let minutes = Int(timeInterval) / 60
        let seconds = Int(timeInterval) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    // MARK: - Draft Area Helper Functions
    
    private func updateLongQuestionAnswer(for questionId: UUID, type: AnswerSubmissionType) {
        var answerText = ""
        
        switch type {
        case .drawing:
            if let drawing = answerDrawing {
                do {
                    let drawingData = try drawing.dataRepresentation()
                    answerText = "DRAWING:" + drawingData.base64EncodedString()
                } catch {
                    print("❌ Failed to encode drawing: \(error)")
                    answerText = ""
                }
            }
        case .photo:
            if let image = answerImage,
               let imageData = image.jpegData(compressionQuality: 0.8) {
                answerText = "PHOTO:" + imageData.base64EncodedString()
            }
        case .none:
            answerText = ""
        }
        
        updateAnswer(for: questionId, with: answerText)
        print("📝 Updated long question answer for \(questionId): \(type.rawValue)")
    }
    
    private func resetDraftAreaForNewQuestion() {
        // Reset draft area state when changing questions
        drawing = PKDrawing()
        canvasView.drawing = PKDrawing()
        
        // For long questions, also reset answer submission state
        if !exercise.questions.isEmpty && currentQuestionIndex < exercise.questions.count {
            let currentQuestion = exercise.questions[currentQuestionIndex]
            if currentQuestion.questionType == .longQuestion {
                // Check if this question already has an answer
                if let existingAnswer = answers.first(where: { $0.questionId == currentQuestion.id })?.answer {
                    // Load existing answer if available
                    if existingAnswer.hasPrefix("DRAWING:") {
                        let drawingData = String(existingAnswer.dropFirst("DRAWING:".count))
                        if let data = Data(base64Encoded: drawingData),
                           let loadedDrawing = try? PKDrawing(data: data) {
                            answerDrawing = loadedDrawing
                            answerType = .drawing
                        }
                    } else if existingAnswer.hasPrefix("PHOTO:") {
                        let imageData = String(existingAnswer.dropFirst("PHOTO:".count))
                        if let data = Data(base64Encoded: imageData),
                           let image = UIImage(data: data) {
                            answerImage = image
                            answerType = .photo
                        }
                    } else {
                        answerType = .none
                        answerImage = nil
                        answerDrawing = nil
                    }
                } else {
                    // No existing answer, reset to clean state
                    answerType = .none
                    answerImage = nil
                    answerDrawing = nil
                }
            } else {
                // For multiple choice, reset answer submission state
                answerType = .none
                answerImage = nil
                answerDrawing = nil
            }
        }
    }
}

// MARK: - Long Answer View Component
struct LongAnswerView: View {
    @Binding var answer: String
    let questionId: UUID
    @Binding var answerType: AnswerSubmissionType
    @Binding var answerImage: UIImage?
    @Binding var answerDrawing: PKDrawing?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Long Answer")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)
            
            // Show current answer submission status
            switch answerType {
            case .none:
                Text("Use the draft area below to submit your answer as a drawing or photo.")
                    .font(.caption)
                    .foregroundColor(.gray)
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
            case .drawing:
                HStack {
                    Image(systemName: "pencil.and.outline")
                        .foregroundColor(.blue)
                    Text("Answer will be submitted as drawing")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(8)
            case .photo:
                HStack {
                    Image(systemName: "camera")
                        .foregroundColor(.green)
                    Text("Answer will be submitted as photo")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.green)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(8)
            }
        }
    }
}

struct MultipleChoiceView: View {
    let options: [String]
    @Binding var selectedAnswer: String
    let correctAnswerIndex: Int
    let onAnswerChanged: (() -> Void)?
    
    init(options: [String], selectedAnswer: Binding<String>, correctAnswerIndex: Int, onAnswerChanged: (() -> Void)? = nil) {
        self.options = options
        self._selectedAnswer = selectedAnswer
        self.correctAnswerIndex = correctAnswerIndex
        self.onAnswerChanged = onAnswerChanged
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            if options.isEmpty {
                Text("No options available for this question")
                    .font(.body)
                    .foregroundColor(.orange)
                    .padding()
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
            } else {
                ForEach(Array(options.enumerated()), id: \.offset) { index, option in
                    Button(action: {
                        selectedAnswer = String(index)
                        print("📚 Selected answer index: \(index) - \(option)")
                        onAnswerChanged?()
                    }) {
                        HStack {
                            Image(systemName: selectedAnswer == String(index) ? "circle.fill" : "circle")
                                .foregroundColor(.blue)
                            QuestionTextRenderer(text: option, fontSize: 16)
                                .foregroundColor(.primary)
                            Spacer()
                        }
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedAnswer == String(index) ? Color.blue.opacity(0.1) : Color.clear)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(selectedAnswer == String(index) ? Color.blue : Color.gray.opacity(0.3), lineWidth: 1)
                        )
                        .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
    }
} 
