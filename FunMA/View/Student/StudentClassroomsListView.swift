import SwiftUI

struct StudentClassroomsListView: View {
    @StateObject private var userManager = UserManager.shared
    @State private var studentClassrooms: [Classroom] = []
    @State private var isLoading = false
    @State private var error: String?
    @State private var showingErrorAlert = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("My Classrooms")
                    .font(.title)
                    .fontWeight(.bold)
                
                Spacer()
            }
            .padding()
            .background(Color(.systemBackground))
            
            Divider()
            
            // Content
            if isLoading {
                loadingView
            } else if studentClassrooms.isEmpty {
                emptyStateView
            } else {
                classroomsList
            }
        }
        .background(Color(.systemGroupedBackground))
        .alert("Error", isPresented: $showingErrorAlert) {
            Button("OK") {
                showingErrorAlert = false
                error = nil
            }
        } message: {
            if let error = error {
                Text(error)
            }
        }
        .onChange(of: error) { errorMessage in
            if errorMessage != nil && !showingErrorAlert {
                showingErrorAlert = true
            }
        }
        .task {
            print("📱 StudentClassroomsListView: Initial load triggered")
            await loadStudentClassrooms()
        }
        .refreshable {
            print("🔄 StudentClassroomsListView: Pull-to-refresh triggered")
            await loadStudentClassrooms()
        }
        .onAppear {
            print("🏫 StudentClassroomsListView: Appearing with \(studentClassrooms.count) classrooms")
        }
    }
    
    // MARK: - Loading View
    
    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            Text("Loading your classrooms...")
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - Empty State View
    
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "building.2")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            VStack(spacing: 8) {
                Text("No Classrooms Yet")
                    .font(.title2)
                    .fontWeight(.semibold)
                
                Text("You haven't been assigned to any classrooms yet. Contact your teacher to get added to a classroom.")
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }
    
    // MARK: - Classrooms List
    
    private var classroomsList: some View {
        ScrollView {
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 16) {
                ForEach(studentClassrooms) { classroom in
                    Button(action: {
                        print("🏫 StudentClassroomsListView: Tapping classroom '\(classroom.name)' with ID: \(classroom.id)")
                        // TODO: Navigate to classroom detail view
                    }) {
                        StudentClassroomCard(classroom: classroom)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding()
        }
    }
    
    // MARK: - Data Loading
    
    private func loadStudentClassrooms() async {
        await MainActor.run {
            isLoading = true
            error = nil
        }
        
        do {
            print("📚 StudentClassroomsListView: Loading student classrooms")
            let classrooms = try await userManager.getStudentClassrooms()
            
            await MainActor.run {
                self.studentClassrooms = classrooms
                self.isLoading = false
                print("📚 StudentClassroomsListView: Successfully loaded \(classrooms.count) student classrooms")
                
                // Log classroom details
                for (index, classroom) in classrooms.enumerated() {
                    print("📚 Classroom \(index + 1): \(classroom.name) (ID: \(classroom.id))")
                }
            }
        } catch {
            await MainActor.run {
                let errorMsg: String
                
                // Provide more specific error messages based on the type of error
                if let apiError = error as? APIError {
                    switch apiError {
                    case .noData:
                        errorMsg = "No classroom data received from server. Please check your internet connection and try again."
                    case .unauthorized:
                        errorMsg = "Authentication failed. Please log in again."
                    case .networkError(let message):
                        errorMsg = "Network error: \(message). Please check your internet connection."
                    case .decodingError(let message):
                        errorMsg = "Data format error: \(message). Please try again later."
                    case .serverError(let message):
                        errorMsg = "Server error: \(message). Please try again later."
                    case .invalidURL:
                        errorMsg = "Invalid request. Please try again."
                    }
                } else {
                    errorMsg = "Failed to load classrooms: \(error.localizedDescription)"
                }
                
                self.error = errorMsg
                self.isLoading = false
                print("❌ StudentClassroomsListView: \(errorMsg)")
                print("🔍 StudentClassroomsListView: Error details: \(error)")
            }
        }
    }
}



#Preview {
    StudentClassroomsListView()
}
