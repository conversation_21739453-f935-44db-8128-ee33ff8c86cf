//
//  MetamateView.swift
//  FunMA
//
//  Created by Assistant on 2024-01-XX.
//

import SwiftUI
import Foundation
import WebKit

// MARK: - LLM API Models

struct LLMHintRequest: Codable {
    let message: String
}

struct LLMHintResponse: Codable {
    let response: String?
    let message: String?
    let error: String?
}

// MARK: - Metamate Manager

class MetamateManager: ObservableObject {
    @Published var isVisible = false
    @Published var isExpanded = false
    @Published var messages: [MetamateMessage] = []
    @Published var isTyping = false
    
    // Confusion detection variables
    @Published var questionStartTime: Date?
    @Published var answerChangeCount = 0
    @Published var timeSpentOnQuestion: TimeInterval = 0
    @Published var hasShownHint = false
    
    private var confusionTimer: Timer?
    private let confusionThreshold: TimeInterval = 45 // 45 seconds
    private let maxAnswerChanges = 2
    
    func startTrackingQuestion(question: Question) {
        questionStartTime = Date()
        answerChangeCount = 0
        timeSpentOnQuestion = 0 // Reset timer to 0 when navigating to another question
        hasShownHint = false

        // Start confusion detection timer
        confusionTimer?.invalidate()
        confusionTimer = Timer.scheduledTimer(withTimeInterval: confusionThreshold, repeats: false) { _ in
            Task { @MainActor in
                self.detectConfusion(question: question)
            }
        }
    }
    
    func onAnswerChanged(question: Question) {
        answerChangeCount += 1
        
        // Detect confusion if too many answer changes
        if answerChangeCount >= maxAnswerChanges && !hasShownHint {
            Task { @MainActor in
                self.detectConfusion(question: question)
            }
        }
    }
    
    private func detectConfusion(question: Question) {
        guard !hasShownHint else { return }
        
        hasShownHint = true
        isVisible = true
        isExpanded = true
        
        // Generate AI-powered hint using LLM
        Task {
            await generateAIHint(for: question, confusionType: timeSpentOnQuestion >= confusionThreshold ? "time_based" : "answer_changes")
        }
    }
    
    func addMessage(_ content: String, isFromUser: Bool) {
        let message = MetamateMessage(content: content, isFromUser: isFromUser)
        messages.append(message)
        
        if !isFromUser {
            // Simulate typing delay for AI responses
            isTyping = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.isTyping = false
            }
        }
    }
    
    func askForHelp(question: Question) {
        isVisible = true
        Task {
            await generateAIHint(for: question, confusionType: "explicit_help")
        }
    }
    
    // private func generateDetailedHint(for question: Question) -> String {
    //     // More detailed hints when explicitly asked
    //     if question.questionText.lowercased().contains("factor") {
    //         return "🔍 To factor expressions, look for common factors first. Then check if it's a special form like difference of squares (a² - b²) or perfect square trinomial."
    //     } else if question.questionText.lowercased().contains("derivative") {
    //         return "📈 For derivatives, remember the basic rules: power rule (d/dx[xⁿ] = nxⁿ⁻¹), product rule, and chain rule. What type of function are you working with?"
    //     } else if question.questionText.lowercased().contains("triangle") {
    //         return "📐 For triangle problems, consider what information you have: sides, angles, or both? Think about which theorem applies: Pythagorean, law of sines, or law of cosines."
    //     } else {
    //         return "🎯 Let's approach this systematically: 1) What is the question asking? 2) What information do you have? 3) What formula or concept might apply?"
    //     }
    // }
    
    func hideBot() {
        isVisible = false
        confusionTimer?.invalidate()
    }
    
    func showBot() {
        isVisible = true
        if messages.isEmpty {
            addMessage("👋 Hi! I'm your study buddy. I'm here to help if you get stuck on any questions!", isFromUser: false)
        }
    }
    
    // MARK: - LLM Integration
    
    @MainActor
    private func generateAIHint(for question: Question, confusionType: String) async {
        // Show typing indicator
        isTyping = true
        
        // Calculate time spent on current question
        let currentTimeSpent = questionStartTime.map { Date().timeIntervalSince($0) } ?? 0
        
        // Create a comprehensive prompt for the LLM
        var prompt = "You are Metamate, a helpful AI tutor. A student needs help with this question:\n\n"
        prompt += "Question: \(question.questionText)\n"
        prompt += "Type: \(question.questionType.rawValue)\n"
        
        if let options = question.options, !options.isEmpty {
            prompt += "Options:\n"
            for (index, option) in options.enumerated() {
                prompt += "\(index + 1). \(option)\n"
            }
        }
        
        // Add confusion context
        prompt += "\nStudent Context:\n"
        switch confusionType {
        case "time_based":
            prompt += "- Student has been working on this question for \(Int(currentTimeSpent)) seconds\n"
        case "answer_changes":
            prompt += "- Student has changed their answer \(answerChangeCount) times\n"
        case "explicit_help":
            prompt += "- Student explicitly asked for help\n"
        default:
            prompt += "- Student appears to be struggling\n"
        }
        
        // Add conversation history if exists
        if !messages.isEmpty {
            prompt += "\nPrevious conversation:\n"
            for message in messages.suffix(5) { // Only include last 5 messages
                let role = message.isFromUser ? "Student" : "Metamate"
                prompt += "\(role): \(message.content)\n"
            }
        }
        
        prompt += "\nPlease provide a helpful, encouraging hint that guides the student without giving away the answer. Keep it concise and friendly."
        
        // Create request
        let request = LLMHintRequest(message: prompt)
        
        // Call LLM API
        do {
            let response = try await callLLMAPI(request: request)
            
            // Hide typing indicator and add response
            isTyping = false
            addMessage(response, isFromUser: false)
        } catch {
            print("❌ MetamateManager: LLM API call failed: \(error)")
            
            // Hide typing indicator and show fallback message
            isTyping = false
            let fallbackMessage = generateFallbackHint(for: question, confusionType: confusionType)
            addMessage(fallbackMessage, isFromUser: false)
        }
    }
    
    @MainActor
    func generateAIResponse(for userMessage: String, question: Question?) async {
        // Show typing indicator
        isTyping = true
        
        // Create a comprehensive prompt for the LLM
        var prompt = "You are Metamate, a helpful AI tutor. A student just asked you: '\(userMessage)'\n\n"
        
        if let question = question {
            prompt += "Current Question Context:\n"
            prompt += "Question: \(question.questionText)\n"
            prompt += "Type: \(question.questionType.rawValue)\n"
            
            if let options = question.options, !options.isEmpty {
                prompt += "Options:\n"
                for (index, option) in options.enumerated() {
                    prompt += "\(index + 1). \(option)\n"
                }
            }
        }
        
        // Add conversation history if exists
        if !messages.isEmpty {
            prompt += "\nPrevious conversation:\n"
            for message in messages.suffix(5) { // Only include last 5 messages
                let role = message.isFromUser ? "Student" : "Metamate"
                prompt += "\(role): \(message.content)\n"
            }
        }
        
        prompt += "\nPlease respond to the student's question in a helpful, encouraging way. If they need help with the current question, provide hints without giving away the answer."
        
        // Create request
        let request = LLMHintRequest(message: prompt)
        
        // Call LLM API
        do {
            let response = try await callLLMAPI(request: request)
            
            // Hide typing indicator and add response
            isTyping = false
            addMessage(response, isFromUser: false)
        } catch {
            print("❌ MetamateManager: LLM API call failed: \(error)")
            
            // Hide typing indicator and show fallback message
            isTyping = false
            let fallbackMessage = generateFallbackResponse(for: userMessage, question: question)
            addMessage(fallbackMessage, isFromUser: false)
        }
    }
    
    private func callLLMAPI(request: LLMHintRequest) async throws -> String {
        print("🤖 MetamateManager: Calling LLM API for hint generation")
        print("🤖 MetamateManager: Request message: \(request.message.prefix(100))...")
        
        // Go directly to custom implementation to handle streaming response
        return try await callLLMAPIDirectly(request: request)
    }
    
    private func callLLMAPIDirectly(request: LLMHintRequest) async throws -> String {
        let fullURL = "\(APIConfig.baseURL)/llm/hint"
        guard let url = URL(string: fullURL) else {
            throw APIError.invalidURL
        }
        
        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        // Add authentication headers
        if let authHeaders = UserManager.shared.getAuthorizationHeader() {
            for (key, value) in authHeaders {
                urlRequest.addValue(value, forHTTPHeaderField: key)
            }
        }
        
        // Encode request body
        do {
            let encoder = JSONEncoder()
            let jsonData = try encoder.encode(request)
            urlRequest.httpBody = jsonData
        } catch {
            throw APIError.networkError(error.localizedDescription)
        }
        
        // Make the request
        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.noData
            }
            
            if httpResponse.statusCode >= 400 {
                throw APIError.serverError("HTTP \(httpResponse.statusCode)")
            }
            
            // Parse the streaming response
            guard let responseString = String(data: data, encoding: .utf8) else {
                throw APIError.noData
            }
            
            return try parseStreamingResponse(responseString)
            
        } catch {
            throw APIError.networkError(error.localizedDescription)
        }
    }
    
    private func parseStreamingResponse(_ responseString: String) throws -> String {
        var fullResponse = ""
        
        // Split by "data: " to get individual chunks
        let chunks = responseString.components(separatedBy: "data: ")
        
        for chunk in chunks {
            let trimmedChunk = chunk.trimmingCharacters(in: .whitespacesAndNewlines)
            
            // Skip empty chunks
            if trimmedChunk.isEmpty {
                continue
            }
            
            // Try to parse each chunk as JSON
            if let chunkData = trimmedChunk.data(using: .utf8) {
                do {
                    if let json = try JSONSerialization.jsonObject(with: chunkData) as? [String: Any] {
                        
                        // Check if this is a text response chunk
                        if let type = json["type"] as? String,
                           type == "textResponseChunk",
                           let textResponse = json["textResponse"] as? String {
                            fullResponse += textResponse
                        }
                        
                        // Check if this is the close chunk
                        if let close = json["close"] as? Bool, close == true {
                            break
                        }
                        
                        // Check for errors
                        if let error = json["error"] as? Bool, error == true {
                            throw APIError.serverError("API Error: 400")
                        }
                    }
                } catch {
                    // Skip chunks that aren't valid JSON (like empty lines)
                    continue
                }
            }
        }
        
        if fullResponse.isEmpty {
            throw APIError.noData
        }
        
        print("✅ MetamateManager: Parsed streaming response successfully")
        print("🤖 MetamateManager: Full response: \(fullResponse.prefix(200))...")
        
        return fullResponse
    }
    
    private func generateFallbackHint(for question: Question, confusionType: String) -> String {
        let hints = [
            "🤔 Take your time to read the question carefully. What is it asking for?",
            "💡 Try breaking down the problem into smaller parts.",
            "📝 What information do you have, and what do you need to find?",
            "🎯 Consider what mathematical concepts or formulas might apply here.",
            "🔍 Look for key words in the question that might give you clues."
        ]
        
        let randomHint = hints.randomElement() ?? hints.first!
        let encouragement = " Remember, making mistakes is part of learning!"
        
        return randomHint + encouragement
    }
    
    private func generateFallbackResponse(for userMessage: String, question: Question?) -> String {
        if userMessage.lowercased().contains("help") {
            return "📋 I'm here to help! Try reading the question step by step and identify what it's asking for."
        } else if userMessage.lowercased().contains("hint") {
            return "💡 Here's a hint: look for key mathematical terms or concepts in the question."
        } else if userMessage.lowercased().contains("confused") {
            return "🤗 It's okay to feel confused sometimes! Let's work through this together."
        } else {
            return "🤖 I'm having trouble connecting right now, but don't give up! Try approaching the problem from a different angle."
        }
    }
    
    func cleanup() {
        confusionTimer?.invalidate()
        confusionTimer = nil
    }
}

// MARK: - Message Model

struct MetamateMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp = Date()
}

// MARK: - Metamate View

struct MetamateView: View {
    @ObservedObject var metamateManager: MetamateManager
    @State private var inputText = ""
    @Environment(\.colorScheme) private var colorScheme

    var currentQuestion: Question? = nil
    
    var body: some View {
        VStack(spacing: 0) {
            if metamateManager.isExpanded {
                chatView
                 .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(colorScheme == .dark ? Color.black.opacity(0.8) : Color.white)
                        .shadow(radius: 10)
                )
            } else {
                collapsedView
            }
        }
       
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: metamateManager.isExpanded)
        .onAppear {
            if metamateManager.messages.isEmpty {
                metamateManager.addMessage("👋 Hi! I'm your study buddy. I'm here to help if you get stuck on any questions!", isFromUser: false)
            }
        }
    }
    
    private var collapsedView: some View {
        Image("metamate")
            .resizable()
            .aspectRatio(contentMode: .fit)
            .frame(width: 100, height: 100)
            .clipShape(Circle())
            .contentShape(Circle())
            .shadow(radius: 10)
            .onTapGesture {
                withAnimation {
                    metamateManager.isExpanded.toggle()
                }
            }
    }
    
    private var chatView: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("🤖 Metamate")
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()

                Button(action: {
                    withAnimation {
                        metamateManager.isExpanded.toggle()
                    }
                }) {
                    Image(systemName: "xmark")
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical)
            .background(colorScheme == .dark ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1))
            
            // Messages
            ScrollViewReader { proxy in
                ScrollView {
                    LazyVStack(spacing: 8) {
                        ForEach(metamateManager.messages) { message in
                            MessageBubble(message: message, isFromUser: message.isFromUser)
                                .id(message.id)
                        }

                        if metamateManager.isTyping {
                            TypingIndicator()
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 8)
                }
                .frame(maxHeight: 280)
                .id("messages-\(metamateManager.isExpanded ? "expanded" : "collapsed")")
                .onAppear {
                    // Scroll to bottom when messages view appears (when chat is opened)
                    if let lastMessage = metamateManager.messages.last {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
                .onChange(of: metamateManager.messages.count) { _ in
                    if let lastMessage = metamateManager.messages.last {
                        withAnimation {
                            proxy.scrollTo(lastMessage.id, anchor: .bottom)
                        }
                    }
                }
            }
            
            // Input area
            HStack {
                TextField("Type a message...", text: $inputText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .font(.system(size: 14))
                
                Button(action: {
                    sendMessage()
                }) {
                    Image(systemName: "paperplane.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.blue)
                }
                .disabled(inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 8)
            .background(colorScheme == .dark ? Color.gray.opacity(0.1) : Color.gray.opacity(0.05))
        }
        .frame(width: 320, height: 380)
    }
    
    private func sendMessage() {
        let message = inputText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !message.isEmpty else { return }
        
        metamateManager.addMessage(message, isFromUser: true)
        inputText = ""
        
        // Generate AI response
        Task {
            await metamateManager.generateAIResponse(for: message, question: currentQuestion)
        }
    }
}

// MARK: - Message Bubble with LaTeX Support

struct MessageBubble: View {
    let message: MetamateMessage
    let isFromUser: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack {
            if isFromUser {
                Spacer()
            }
            
            VStack(alignment: isFromUser ? .trailing : .leading, spacing: 4) {
                // Use MessageContentView for LaTeX support
                MessageContentView(content: message.content, isFromUser: isFromUser)
                
                Text(message.timestamp, style: .time)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            if !isFromUser {
                Spacer()
            }
        }
    }
}

// MessageContentView is already defined in AIStudyAssistantView.swift - using that existing implementation

// MarkdownMathView is already defined in MarkdownMathView.swift - using that existing implementation

// All LaTeX rendering components are already defined in other files:
// - KaTeXWebView is in AIStudyAssistantView.swift  
// - TypingIndicator is in FunMA/View/LLM/TypingIndicator.swift
// - MessageContentView is in AIStudyAssistantView.swift
// - MarkdownMathView is in MarkdownMathView.swift

// Using existing components for LaTeX support in metamate messages 