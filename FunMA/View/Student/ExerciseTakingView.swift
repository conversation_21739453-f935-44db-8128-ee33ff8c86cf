import SwiftUI
import PencilKit

// Math symbol conversion function has been moved to FunMA/View/Components/LaTeXTextRenderer.swift
// DrawingPreviewView is defined in FunMA/View/Components/LaTeXTextRenderer.swift

enum ExerciseSheetType: Identifiable, Equatable {
    case taking(Exercise)
    case review(Exercise)
    var id: UUID {
        switch self {
        case .taking(let exercise), .review(let exercise):
            return exercise.id
        }
    }
    static func == (lhs: ExerciseSheetType, rhs: ExerciseSheetType) -> Bool {
        lhs.id == rhs.id
    }
}

struct ExerciseTakingView: View {
    @StateObject private var viewModel = ExerciseViewModel()
    @StateObject private var userManager = UserManager.shared
    @State private var selectedTab = 0
    @State private var activeSheet: ExerciseSheetType?
    @State private var debugSelectedExerciseTitle = "None"
    @State private var studentClassrooms: [Classroom] = []
    @State private var isLoadingClassrooms = false
    @State private var classroomError: String?
    @State private var isInitialLoading = false
    @State private var loadingTask: Task<Void, Never>?
    
    // Filter exercises to only show those assigned to student's classrooms
    private var filteredExercises: [Exercise] {
        let studentClassroomIds = Set(studentClassrooms.map { $0.id })
        
        return viewModel.exercises.filter { exercise in
            // Check if exercise is assigned to any of the student's classrooms
            return exercise.classroomIds.contains { classroomId in
                studentClassroomIds.contains(classroomId)
            }
        }
    }
    
    // Compute filtered list for the selected tab
    private var filteredExercisesForSelectedTab: [Exercise] {
        if selectedTab == 0 {
            // Available: due in future, not completed, canSubmit == true
            return filteredExercises.filter { exercise in
                exercise.dueDate >= Date() && !hasCompletedExercise(exercise.id) && (exercise.canSubmit ?? true)
            }
        } else {
            // Completed: completed OR (not completed AND isPastDue == true)
            let completed = filteredExercises.filter { exercise in
                hasCompletedExercise(exercise.id) || (!hasCompletedExercise(exercise.id) && (exercise.isPastDue ?? false))
            }
            // Sort by submission endTime (descending), fallback to dueDate (descending)
            return completed.sorted { lhs, rhs in
                let lhsSubmission = viewModel.studentSubmissions.first { $0.exerciseId == lhs.id && $0.studentId == userManager.currentUser.id }
                let rhsSubmission = viewModel.studentSubmissions.first { $0.exerciseId == rhs.id && $0.studentId == userManager.currentUser.id }
                let lhsDate = lhsSubmission?.endTime ?? lhs.dueDate
                let rhsDate = rhsSubmission?.endTime ?? rhs.dueDate
                return lhsDate > rhsDate
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            VStack {
                HStack {
                    Text("Exercises")
                        .font(.title)
                        .fontWeight(.bold)
                    Spacer()
                }
                
                // Classroom context info
                HStack {
                    if isLoadingClassrooms {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                            Text("Loading classrooms...")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    } else if studentClassrooms.isEmpty {
                        Text("No classroom assigned")
                            .font(.caption)
                            .foregroundColor(.orange)
                    } else {
                        VStack(alignment: .leading, spacing: 2) {
                            Text("My Classrooms: \(studentClassrooms.map { $0.name }.joined(separator: ", "))")
                                .font(.caption)
                                .foregroundColor(.blue)
                            Text("Showing exercises for \(filteredExercises.count) of \(viewModel.exercises.count) total")
                                .font(.caption2)
                                .foregroundColor(.gray)
                        }
                    }
                    Spacer()
                }
            }
            .padding()
            
            // Tab View
            Picker("View", selection: $selectedTab) {
                Text("Available").tag(0)
                Text("Completed").tag(1)
            }
            .pickerStyle(.segmented)
            .padding()
            
            // Exercise List
            if viewModel.isLoading || isLoadingClassrooms {
                ProgressView("Loading exercises...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if studentClassrooms.isEmpty {
                VStack(spacing: 20) {
                    Image(systemName: "building.2")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    
                    Text("No Classroom Assigned")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("You are not assigned to any classroom yet. Please contact your teacher to be added to a classroom.")
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding()
            } else if filteredExercisesForSelectedTab.isEmpty {
                VStack(spacing: 24) {
                    Image(systemName: selectedTab == 0 ? "tray" : "checkmark.circle")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    Text(selectedTab == 0 ? "No Available Exercises" : "No Completed Exercises")
                        .font(.title2)
                        .fontWeight(.bold)
                    Text(selectedTab == 0 ?
                        "There are currently no exercises assigned to you. \nPlease check back later or ask your teacher if you should have an assignment here." :
                        "You haven't completed any exercises yet. Try some available exercises or ask your teacher for more assignments!")
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    if selectedTab == 0 {
                        Button(action: {
                            Task {
                                await loadStudentData()
                            }
                        }) {
                            Label("Refresh", systemImage: "arrow.clockwise")
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding()
            } else {
                ScrollView {
                    // Always use single column layout for exercises
                    LazyVStack(spacing: 16) {
                        ForEach(filteredExercisesForSelectedTab) { exercise in
                            StudentExerciseCard(
                                exercise: exercise,
                                studentClassrooms: studentClassrooms,
                                onTap: {
                                    print("🔄 StudentExerciseCard tapped!")
                                    print("🔄 Exercise: \(exercise.title)")
                                    print("🔄 Exercise ID: \(exercise.id)")
                                    print("🔄 Questions count: \(exercise.questions.count)")
                                    print("🔄 Assigned to classrooms: \(exercise.classroomIds)")
                                    let isCompleted = hasCompletedExercise(exercise.id)
                                    let isPastDue = exercise.isPastDue ?? false
                                    print("🔄 Exercise completion status: \(isCompleted ? "COMPLETED" : "NOT COMPLETED")")
                                    debugSelectedExerciseTitle = exercise.title
                                    DispatchQueue.main.async {
                                        if isCompleted || (isPastDue && !isCompleted) {
                                            print("🔄 Opening exercise review for completed or past due exercise (results only)")
                                            activeSheet = .review(exercise)
                                        } else {
                                            print("🔄 Opening exercise taking view for available exercise")
                                            activeSheet = .taking(exercise)
                                        }
                                        print("🔄 debugSelectedExerciseTitle: \(debugSelectedExerciseTitle)")
                                    }
                                },
                                showPastDueLabel: selectedTab == 1 && !hasCompletedExercise(exercise.id) && (exercise.isPastDue ?? false)
                            )
                        }
                    }
                    .padding()
                }
            }
        }
        .fullScreenCover(item: $activeSheet) { sheet in
            switch sheet {
            case .taking(let exercise):
                ExerciseTakingSheet(
                    exercise: exercise,
                    viewModel: viewModel,
                    onDismiss: {
                        print("📋 ExerciseTakingSheet dismissed")
                        activeSheet = nil
                    }
                )
                .onAppear {
                    print("🔍 Sheet closure executing...")
                    print("✅ Exercise found in sheet closure: \(exercise.title)")
                    print("📋 Sheet is being presented! activeSheet: \(exercise.title)")
                    print("📋 Exercise has \(exercise.questions.count) questions")
                    print("📋 Exercise ID: \(exercise.id.uuidString)")
                    print("📋 Current question index will be: 0")
                    for (index, question) in exercise.questions.enumerated() {
                        print("📋 Question \(index): \(question.questionText)")
                        print("📋 Question Type: \(question.questionType.rawValue)")
                        if let options = question.options {
                            print("📋 Question Options: \(options)")
                        }
                    }
                }
            case .review(let exercise):
                ExerciseReviewSheet(
                    exercise: exercise,
                    viewModel: viewModel,
                    onDismiss: {
                        print("📋 ExerciseReviewSheet dismissed")
                        activeSheet = nil
                    }
                )
            }
        }
        .task {
            // Cancel any existing loading task
            loadingTask?.cancel()
            
            // Create new loading task
            loadingTask = Task {
                // Load student classrooms and exercises
                await loadStudentData()
            }
        }
        .onDisappear {
            // Cancel loading task when view disappears
            loadingTask?.cancel()
            loadingTask = nil
        }
    }
    
    private func loadStudentData() async {
        // Prevent multiple concurrent loads
        guard !isInitialLoading else {
            print("📚 ExerciseTakingView: Already loading data, skipping duplicate call")
            return
        }
        
        await MainActor.run {
            isInitialLoading = true
        }
        
        // First load student's classrooms
        await loadStudentClassrooms()
        
        // Then load exercises (which will be filtered by classroom membership)
        await loadExercises()
        
        // Finally load student submissions to determine completion status
        await loadStudentSubmissions()
        
        await MainActor.run {
            isInitialLoading = false
        }
    }
    
    private func loadStudentClassrooms() async {
        await MainActor.run {
            isLoadingClassrooms = true
            classroomError = nil
            print("[DEBUG] isLoadingClassrooms set to true")
        }
        
        do {
            print("📚 ExerciseTakingView: Loading student classrooms")
            let classrooms = try await userManager.getStudentClassrooms()
            
            await MainActor.run {
                self.studentClassrooms = classrooms
                isLoadingClassrooms = false
                print("[DEBUG] isLoadingClassrooms set to false (success)")
                print("📚 ExerciseTakingView: Successfully loaded \(classrooms.count) student classrooms")
                
                // Log classroom details
                for (index, classroom) in classrooms.enumerated() {
                    print("📚 Classroom \(index + 1): \(classroom.name) (ID: \(classroom.id))")
                }
            }
        } catch {
            await MainActor.run {
                self.classroomError = error.localizedDescription
                isLoadingClassrooms = false
                print("[DEBUG] isLoadingClassrooms set to false (error)")
                print("❌ ExerciseTakingView: Failed to load student classrooms: \(error.localizedDescription)")
                
                // If this is an authentication error, don't create fallback data
                if let nsError = error as NSError?, nsError.domain == "AuthenticationError" {
                    print("🔐 ExerciseTakingView: Authentication error - user needs to log in")
                    return
                }
                
                // Fallback: try to get single classroom
                print("📚 ExerciseTakingView: Trying single classroom fallback")
            }
            
            // Fallback: try to get single classroom
            do {
                if let singleClassroom = try await userManager.getStudentClassroom() {
                    await MainActor.run {
                        self.studentClassrooms = [singleClassroom]
                        self.classroomError = nil
                        print("📚 ExerciseTakingView: Fallback successful - found single classroom: \(singleClassroom.name)")
                    }
                }
            } catch {
                print("❌ ExerciseTakingView: Fallback also failed: \(error.localizedDescription)")
            }
        }
    }
    
    private func loadExercises() async {
        await MainActor.run {
            viewModel.isLoading = true
            print("[DEBUG] viewModel.isLoading set to true")
        }
        do {
            print("📚 ExerciseTakingView: Loading student exercises using JWT authentication")
            try await viewModel.getStudentExercises()
            print("📚 ExerciseTakingView: Successfully loaded \(viewModel.exercises.count) exercises")
            
            // Log exercise details and classroom assignments
            for (index, exercise) in viewModel.exercises.enumerated() {
                print("📚 Exercise \(index + 1): \(exercise.title)")
                print("📚   - Questions: \(exercise.questions.count)")
                print("📚   - Topic: \(exercise.topic)")
                print("📚   - Due: \(exercise.dueDate)")
                print("📚   - Assigned to classrooms: \(exercise.classroomIds)")
            }
            
            // Log filtering results
            await MainActor.run {
                print("📚 ExerciseTakingView: Filtering results:")
                print("📚   - Total exercises: \(viewModel.exercises.count)")
                print("📚   - Student classrooms: \(studentClassrooms.count)")
                print("📚   - Filtered exercises: \(filteredExercises.count)")
                print("📚   - Student classroom IDs: \(studentClassrooms.map { $0.id })")
            }
            
            // If no exercises found, create a sample one for testing
            if viewModel.exercises.isEmpty {
                print("📚 ExerciseTakingView: No exercises found, creating sample exercise")
                await createSampleExercise()
            } else {
                print("📚 ExerciseTakingView: Found \(viewModel.exercises.count) exercises, filtered to \(filteredExercises.count) for student's classrooms")
            }
            await MainActor.run {
                viewModel.isLoading = false
                print("[DEBUG] viewModel.isLoading set to false (success)")
            }
        } catch {
            await MainActor.run {
                viewModel.isLoading = false
                print("[DEBUG] viewModel.isLoading set to false (error)")
                print("❌ ExerciseTakingView: Failed to load student exercises: \(error.localizedDescription)")
            }
            
            // Check if this is an authentication error
            if let nsError = error as NSError?, nsError.domain == "AuthenticationError" {
                print("🔐 ExerciseTakingView: Authentication error detected - user needs to log in")
                // Don't attempt fallback for authentication errors
                return
            }
            
            // Create a local sample exercise for testing when student exercises are not available
            print("📚 ExerciseTakingView: Creating local sample exercise for testing")
            await createSampleExercise()
        }
    }
    
    private func loadStudentSubmissions() async {
        do {
            print("📚 ExerciseTakingView: Loading student submissions to determine completion status")
            
            // Use only the optimized single API call method
            try await viewModel.getAllStudentSubmissions()
            
            print("📚 ExerciseTakingView: Successfully loaded \(viewModel.studentSubmissions.count) student submissions")
            
            // Log submission details for debugging
            await MainActor.run {
                let completedExerciseIds = Set(viewModel.studentSubmissions.map { $0.exerciseId })
                print("📚 ExerciseTakingView: Completed exercise IDs: \(completedExerciseIds.map { $0.uuidString })")
                
                // Update completion status for debugging
                for exercise in viewModel.exercises {
                    let isCompleted = hasCompletedExercise(exercise.id)
                    print("📚 Exercise '\(exercise.title)': \(isCompleted ? "COMPLETED" : "AVAILABLE")")
                }
            }
            
        } catch {
            print("❌ ExerciseTakingView: Failed to load student submissions: \(error.localizedDescription)")
            print("📚 ExerciseTakingView: Completion status may not persist across sessions")
            
            // Keep existing submissions if any - no fallback to prevent multiple API calls
            // The single getAllStudentSubmissions call should handle all cases
        }
    }
    
    private func createSampleExercise() async {
        // This function is removed as per the instructions
    }
    
    private func hasCompletedExercise(_ exerciseId: UUID) -> Bool {
        viewModel.studentSubmissions.contains { submission in
            submission.exerciseId == exerciseId && submission.studentId == userManager.currentUser.id
        }
    }
}

struct StudentExerciseCard: View {
    let exercise: Exercise
    let studentClassrooms: [Classroom]
    let onTap: () -> Void
    var showPastDueLabel: Bool = false
    
    // Get classroom names for this exercise
    private var assignedClassroomNames: [String] {
        studentClassrooms.compactMap { classroom in
            exercise.classroomIds.contains(classroom.id) ? classroom.name : nil
        }
    }
    
    var body: some View {
        Button(action: {
            print("🔘 StudentExerciseCard Button tapped for: \(exercise.title)")
            onTap()
        }) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(exercise.title)
                            .font(.headline)
                        Text(exercise.topic)
                            .font(.subheadline)
                            .foregroundColor(.gray)
                        if !exercise.subtopic.isEmpty {
                            Text(exercise.subtopic)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        // Show assigned classrooms (only student's classrooms) - changed to black color and removed background
                        if !assignedClassroomNames.isEmpty {
                            HStack {
                                ForEach(assignedClassroomNames, id: \.self) { classroomName in
                                    Text(classroomName)
                                        .font(.caption)
                                        .foregroundColor(.black)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 2)
                                }
                            }
                        }
                        
                        // Warning for exercises with no questions - removed background
                        if exercise.questions.isEmpty {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                Text("No questions available")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                        if showPastDueLabel {
                            HStack(spacing: 4) {
                                Image(systemName: "clock.arrow.circlepath")
                                    .font(.caption)
                                    .foregroundColor(.red)
                                Text("Past Due")
                                    .font(.caption2)
                                    .foregroundColor(.red)
                            }
                        }
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("\(exercise.questions.count) Questions")
                            .font(.caption)
                            .foregroundColor(exercise.questions.isEmpty ? .orange : .gray)
                        
                        // Due date
                        Text(exercise.dueDate, style: .date)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                
                HStack {
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.blue)
                }
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .shadow(radius: 2)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(exercise.questions.isEmpty ? Color.orange.opacity(0.3) : Color.clear, lineWidth: 1)
            )
        }
        .buttonStyle(.plain)
    }
}

// ExerciseTakingSheet has been moved to ExerciseTakingSheet.swift

// MultipleChoiceView has been moved to ExerciseTakingSheet.swift

// Note: ExerciseResultsView, QuestionReviewCard, and ExerciseReviewSheet have been moved to ExerciseReviewSheet.swift

// MARK: - Long Answer View Component has been moved to ExerciseTakingSheet.swift

#Preview {
    ExerciseTakingView()
} 