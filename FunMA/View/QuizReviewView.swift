//
//  QuizReviewView.swift
//  Luminous Education
//
//  Created by <PERSON> on 13/8/2024.
//

import SwiftUI


struct QuizReviewView: View {
    var questions: [QuizQuestion]
    var userAnswers: [Int?]
    var lessonId: String
    
    @State private var currentQuestionIndex = 0
    @State private var showExplanation = false
    @State private var correctAnswers: [Int] = []
    
    init(questions: [QuizQuestion], userAnswers: [Int?], lessonId: String) {
        self.questions = questions
        self.userAnswers = userAnswers
        self.lessonId = lessonId
        
        // Pre-calculate correct answers
        _correctAnswers = State(initialValue: questions.compactMap { $0.correctAnswerIndex })
    }
    
    var body: some View {
        VStack {
            // Header
            HStack {
                Text("Quiz Review")
                    .font(.title)
                    .fontWeight(.bold)
                Spacer()
                // Question indicator
                VStack(alignment: .leading) {
                    Text("Question \(currentQuestionIndex + 1)/\(questions.count)")
                        .font(.headline)
                        .fontWeight(.bold)
                    
                    // Question number indicators
                    HStack(spacing: 5) {
                        ForEach(1...questions.count, id: \.self) { index in
                            Text("\(index)")
                                .frame(width: 30, height: 30)
                                .background(index == currentQuestionIndex + 1 ? Color.blue : Color.gray)
                                .foregroundColor(.white)
                                .cornerRadius(5)
                        }
                    }
                }
            }
            .padding()

            // Question Section
            VStack(alignment: .leading) {
                Text("Question:")
                    .font(.title2)
                    .bold()
                    .padding(.bottom, 10)
                
                if currentQuestionIndex < questions.count {
                    Text(questions[currentQuestionIndex].question)
                        .font(.title3)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.leading)
                        .padding()
                        .frame(minHeight: 100)
                }
            }
            .padding()
            .frame(maxWidth: .infinity, alignment: .leading)

            // Response Section
            VStack(alignment: .leading, spacing: 10) {
                Text("Your response:")
                    .font(.title2)
                    .bold()
                    .padding(.bottom, 10)
                
                if currentQuestionIndex < questions.count {
                    VStack(spacing: 8) {
                        ForEach(0..<questions[currentQuestionIndex].options.count, id: \.self) { index in
                            HStack {
                                Text(questions[currentQuestionIndex].options[index])
                                Spacer()
                                if let userAnswer = userAnswers[currentQuestionIndex],
                                   let correctAnswer = questions[currentQuestionIndex].correctAnswerIndex {
                                    if index == userAnswer {
                                        Image(systemName: index == correctAnswer ? "checkmark.circle.fill" : "record.circle.fill")
                                            .foregroundColor(index == correctAnswer ? .green : .red)
                                    }
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 10)
                                    .fill(Color.white)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 10)
                                            .stroke(
                                                getAnswerBorderColor(index: index),
                                                lineWidth: 2
                                            )
                                    )
                            )
                            .foregroundColor(.black)
                            .opacity(userAnswers[currentQuestionIndex] != nil && userAnswers[currentQuestionIndex] != index ? 0.7 : 1.0)
                        }
                    }
                }
            }
            .padding()
            
            // Answer status
            if currentQuestionIndex < questions.count {
                HStack {
                    if let userAnswer = userAnswers[currentQuestionIndex], 
                       let correct = questions[currentQuestionIndex].correctAnswerIndex {
                        if userAnswer == correct {
                            Label("Correct", systemImage: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.headline)
                        } else {
                            Label("Incorrect", systemImage: "xmark.circle.fill")
                                .foregroundColor(.red)
                                .font(.headline)
                        }
                    } else {
                        Label("Not answered", systemImage: "questionmark.circle.fill")
                            .foregroundColor(.orange)
                            .font(.headline)
                    }
                    
                    Spacer()
                    
                    Button(action: {
                        showExplanation.toggle()
                    }) {
                        Label(showExplanation ? "Hide Explanation" : "Show Explanation", 
                              systemImage: showExplanation ? "chevron.up" : "chevron.down")
                    }
                    .buttonStyle(.bordered)
                }
                .padding(.horizontal)
            }
            
            // Explanation section
            if showExplanation && currentQuestionIndex < questions.count, 
               let correctIndex = questions[currentQuestionIndex].correctAnswerIndex {
                VStack(alignment: .leading) {
                    Text("Explanation:")
                        .font(.headline)
                        .padding(.bottom, 5)
                    
                    Text("The correct answer is: \(questions[currentQuestionIndex].options[correctIndex])")
                        .padding(.bottom, 10)
                    
                    if let explanation = getExplanation(for: currentQuestionIndex) {
                        Text(explanation)
                            .frame(maxWidth: .infinity, alignment: .leading)
                    } else {
                        Text("No explanation available for this question.")
                            .frame(maxWidth: .infinity, alignment: .leading)
                    }
                }
                .padding()
                .background(Color.blue.opacity(0.1))
                .cornerRadius(10)
                .padding(.horizontal)
            }

            // Navigation Buttons
            HStack {
                Button {
                    if currentQuestionIndex > 0 {
                        currentQuestionIndex -= 1
                    }
                } label: {
                    Text("Prev")
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(currentQuestionIndex > 0 ? Color.orange : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .disabled(currentQuestionIndex == 0)
                
                Button {
                    if currentQuestionIndex < questions.count - 1 {
                        currentQuestionIndex += 1
                    }
                } label: {
                    Text("Next")
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(currentQuestionIndex < questions.count - 1 ? Color.blue : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .disabled(currentQuestionIndex >= questions.count - 1)
            }
            .padding()
            
            NavigationLink(destination: Text("Return to Lesson \(lessonId)")) {
                HStack {
                    Text("Return to Lesson")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(10)
                }
            }
            .padding(.horizontal)
        }
        .padding()
    }
    
    // Helper function to get explanation text if available
    private func getExplanation(for questionIndex: Int) -> String? {
        // This is a placeholder since QuizQuestion doesn't have an explanation field
        // In a real app, you would fetch explanations from your data model
        return questionIndex == 0 ? 
            "This question tests your understanding of the main topic covered in the video." :
            "This question checks your comprehension of specific details presented in the lesson."
    }
    
    // Helper function to get border color for answer options
    private func getAnswerBorderColor(index: Int) -> Color {
        guard let userAnswer = userAnswers[currentQuestionIndex],
              let correctAnswer = questions[currentQuestionIndex].correctAnswerIndex else {
            return Color.gray
        }
        
        if index == correctAnswer {
            return Color.green
        } else if index == userAnswer {
            return Color.red
        }
        return Color.gray
    }
}

#Preview {
    NavigationView {
        QuizReviewView(
            questions: [
                QuizQuestion(
                    question: "What is the main concept discussed in the video?",
                    options: [
                        "A: The water cycle and its impact on ecosystems",
                        "B: Climate change and global warming effects",
                        "C: Biodiversity in rainforest environments",
                        "D: Conservation methods for endangered species"
                    ],
                    correctAnswerIndex: 1,
                    imageName: nil
                ),
                QuizQuestion(
                    question: "Which of the following was NOT mentioned as a consequence of deforestation?",
                    options: [
                        "A: Increased carbon dioxide in the atmosphere",
                        "B: Loss of habitat for wildlife",
                        "C: Improved water quality in nearby rivers",
                        "D: Soil erosion"
                    ],
                    correctAnswerIndex: 2,
                    imageName: nil
                )
            ],
            userAnswers: [0, nil],
            lessonId: "L101"
        )
    }
}
