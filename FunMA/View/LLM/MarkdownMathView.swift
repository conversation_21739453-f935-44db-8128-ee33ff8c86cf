//
//  MarkdownMathView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
// import MarkdownUI  // Will be added when we add the package
// import SwiftMath   // Will be added when we add the package

// MARK: - Temporary Implementation (until packages are added)

/// A view that renders content with both markdown and math support
struct MarkdownMathView: View {
    let content: String
    let isFromUser: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            ForEach(MarkdownMathRenderer.shared.parseContent(content)) { segment in
                switch segment.type {
                case .markdown:
                    // Temporary: Use MessageFormatter until MarkdownUI is added
                    Text(MessageFormatter.parseMarkdown(segment.content, isFromUser: isFromUser, colorScheme: colorScheme))
                        .textSelection(.enabled)
                
                case .inlineMath, .displayMath:
                    // Temporary: Use existing KaTeX approach until SwiftMath is added
                    TemporaryMathView(
                        expression: segment.content,
                        isDisplayMode: segment.type == .displayMath
                    )
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(messageBackgroundColor)
        )
    }
    
    private var messageBackgroundColor: Color {
        if isFromUser {
            return Color.blue
        } else {
            return colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.1)
        }
    }
}

// MARK: - Temporary Math View (until SwiftMath is added)

struct TemporaryMathView: View {
    let expression: String
    let isDisplayMode: Bool
    let fontSize: CGFloat
    @State private var height: CGFloat = 40
    @Environment(\.colorScheme) private var colorScheme
    
    init(expression: String, isDisplayMode: Bool, fontSize: CGFloat = 16) {
        self.expression = expression
        self.isDisplayMode = isDisplayMode
        self.fontSize = fontSize
    }
    
    var body: some View {
        let cleanedExpression = MarkdownMathRenderer.shared.cleanMathExpression(expression)
        let mathExpression = isDisplayMode ? "$$\(cleanedExpression)$$" : "$\(cleanedExpression)$"
        
        FastKaTeXWebView(
            expression: mathExpression,
            textColor: colorScheme == .dark ? Color.white : Color.black,
            height: $height,
            fontSize: fontSize
        )
        .frame(height: height)
        .frame(maxWidth: .infinity, alignment: isDisplayMode ? .center : .leading)
    }
}

// MARK: - Future Implementation (when packages are added)

/*
/// A view that renders content with both markdown and math support using MarkdownUI + SwiftMath
struct MarkdownMathView: View {
    let content: String
    let isFromUser: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            ForEach(MarkdownMathRenderer.shared.parseContent(content)) { segment in
                switch segment.type {
                case .markdown:
                    // Use MarkdownUI for markdown content
                    Markdown(segment.content)
                        .markdownTextStyle(\.text) { config in
                            config.foregroundColor = messageTextColor
                        }
                        .textSelection(.enabled)
                
                case .inlineMath:
                    // Use SwiftMath for inline math
                    SwiftMathView(
                        latex: MarkdownMathRenderer.shared.cleanMathExpression(segment.content),
                        displayMode: false
                    )
                
                case .displayMath:
                    // Use SwiftMath for display math
                    SwiftMathView(
                        latex: MarkdownMathRenderer.shared.cleanMathExpression(segment.content),
                        displayMode: true
                    )
                    .frame(maxWidth: .infinity, alignment: .center)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(messageBackgroundColor)
        )
    }
    
    private var messageTextColor: Color {
        isFromUser ? Color.white : (colorScheme == .dark ? Color.white : Color.black)
    }
    
    private var messageBackgroundColor: Color {
        if isFromUser {
            return Color.blue
        } else {
            return colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.1)
        }
    }
}

// MARK: - SwiftMath Integration View

struct SwiftMathView: UIViewRepresentable {
    let latex: String
    let displayMode: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    func makeUIView(context: Context) -> MTMathUILabel {
        let view = MTMathUILabel()
        return view
    }
    
    func updateUIView(_ view: MTMathUILabel, context: Context) {
        view.latex = latex
        view.fontSize = 16
        view.textAlignment = displayMode ? .center : .left
        view.labelMode = displayMode ? .display : .text
        view.textColor = colorScheme == .dark ? .white : .black
        
        // Use a high-quality math font
        if let font = MTFontManager().latinModernFont(withSize: 16) {
            view.font = font
        }
    }
}
*/

#Preview {
    VStack(spacing: 16) {
        MarkdownMathView(
            content: "This is **bold text** with an equation $E = mc^2$ and more text.",
            isFromUser: false
        )
        
        MarkdownMathView(
            content: "Here's a display equation: $$\\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$",
            isFromUser: true
        )
        
        MarkdownMathView(
            content: """
            # Quadratic Formula
            
            The quadratic formula is: $ax^2 + bx + c = 0$
            
            The solution is:
            $$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$
            
            Where $a \\neq 0$.
            """,
            isFromUser: false
        )
    }
    .padding()
} 