//
//  AIStudyAssistantView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import UniformTypeIdentifiers
import Combine
import WebKit
import Foundation

// Simplified message structure for persistence (without @Published properties and attachments)
struct SaveableMessage: Codable {
    let content: String
    let isUser: Bool
    let timestamp: Date
    // Note: Attachments are excluded from persistence for now since FileAttachment is not Codable
}

// Simple message model with persistence support
class SimpleMessage: Identifiable, Equatable, ObservableObject {
    let id = UUID()
    @Published var content: String
    let isUser: Bool
    let timestamp: Date
    let attachments: [FileAttachment]

    init(content: String, isUser: Bool, timestamp: Date, attachments: [FileAttachment]) {
        self.content = content
        self.isUser = isUser
        self.timestamp = timestamp
        self.attachments = attachments
    }

    // Implement Equatable
    static func == (lhs: SimpleMessage, rhs: SimpleMessage) -> Bool {
        return lhs.id == rhs.id
    }

    // Convert to ChatMessage for API calls
    func toChatMessage() -> ChatMessage {
        return ChatMessage(
            content: content,
            isUser: isUser,
            role: isUser ? .user : .assistant
        )
    }


}

// Simple chat view model
class SimpleChatViewModel: ObservableObject {
    @Published var messages: [SimpleMessage] = []
    @Published var newMessageText: String = ""
    @Published var isLoading: Bool = false
    @Published var error: String? = nil
    @Published var isStreaming: Bool = false
    @Published var currentStreamingResponse: String = ""
    @Published var streamingMessageId: UUID? = nil

    // Token tracking
    @Published var lastInputTokens: Int = 0
    @Published var lastOutputTokens: Int = 0
    @Published var totalTokensUsed: Int = 0

    // Credit tracking
    @Published var userCredit: Int = 0
    @Published var showingInsufficientCreditAlert = false

    private let anythingLLMService = AnythingLLMService()
    private var cancellables = Set<AnyCancellable>()

    // Minimum credit required to send a message (estimated cost)
    private let minimumCreditRequired = 1

    // UserDefaults key for persisting chat history
    private var chatHistoryKey: String {
        return "AIStudyAssistant_ChatHistory_\(UserManager.shared.currentUser.id)"
    }

    var visibleMessages: [SimpleMessage] {
        return messages
    }

    init() {
        // Try to restore chat history first
        restoreChatHistory()

        // If no messages were restored, add default welcome message
        if messages.isEmpty {
            let welcomeMessage = SimpleMessage(
                content: "Hello! I'm your AI study assistant. I can help you with:\n\n• Explaining complex concepts\n• Solving math problems\n• Reviewing documents and images\n• Answering questions about your studies\n\nWhat would you like to learn today?",
                isUser: false,
                timestamp: Date(),
                attachments: []
            )
            messages.append(welcomeMessage)
            saveChatHistory()
        }

        // Subscribe to token metrics
        anythingLLMService.getTokenMetricsPublisher()
            .sink { [weak self] tokenMetrics in
                print("DEBUG: 🎯 UI received token metrics - Input: \(tokenMetrics.inputTokens), Output: \(tokenMetrics.outputTokens), Total: \(tokenMetrics.totalTokens)")
                self?.lastInputTokens = tokenMetrics.inputTokens
                self?.lastOutputTokens = tokenMetrics.outputTokens
                self?.totalTokensUsed += tokenMetrics.totalTokens
                print("DEBUG: 🎯 UI updated - lastInput: \(self?.lastInputTokens ?? 0), lastOutput: \(self?.lastOutputTokens ?? 0), totalUsed: \(self?.totalTokensUsed ?? 0)")
            }
            .store(in: &cancellables)

        // Subscribe to credit update notifications
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleCreditUpdate),
            name: NSNotification.Name("UserCreditUpdated"),
            object: nil
        )

        // Subscribe to app lifecycle notifications to save chat history
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidEnterBackground),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )

        // Get user's current credit
        updateUserCredit()
    }

    // Handle credit update notifications
    @objc func handleCreditUpdate(_ notification: Notification) {
        if let newCredit = notification.userInfo?["newCredit"] as? Int {
            print("DEBUG: 💰 Received credit update notification: \(newCredit)")
            DispatchQueue.main.async {
                self.userCredit = newCredit
            }
        }
    }

    // Handle app lifecycle notifications
    @objc func appWillResignActive(_ notification: Notification) {
        print("DEBUG: 💾 App will resign active - saving chat history")
        saveChatHistory()
    }

    @objc func appDidEnterBackground(_ notification: Notification) {
        print("DEBUG: 💾 App entered background - saving chat history")
        saveChatHistory()
    }

    deinit {
        // Save chat history before deallocation
        saveChatHistory()
        // Remove notification observer when view model is deallocated
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Chat Persistence Methods

    func saveChatHistory() {
        do {
            // Create a simplified structure for saving (without @Published wrapper and attachments)
            let messagesToSave = messages.map { message in
                SaveableMessage(
                    content: message.content,
                    isUser: message.isUser,
                    timestamp: message.timestamp
                )
            }

            let data = try JSONEncoder().encode(messagesToSave)
            UserDefaults.standard.set(data, forKey: chatHistoryKey)
            print("DEBUG: 💾 Saved \(messages.count) messages to chat history")
        } catch {
            print("DEBUG: ❌ Failed to save chat history: \(error)")
        }
    }

    private func restoreChatHistory() {
        guard let data = UserDefaults.standard.data(forKey: chatHistoryKey) else {
            print("DEBUG: 📂 No saved chat history found")
            return
        }

        do {
            let savedMessages = try JSONDecoder().decode([SaveableMessage].self, from: data)
            messages = savedMessages.map { savedMessage in
                SimpleMessage(
                    content: savedMessage.content,
                    isUser: savedMessage.isUser,
                    timestamp: savedMessage.timestamp,
                    attachments: [] // Attachments are not persisted
                )
            }
            print("DEBUG: 📂 Restored \(messages.count) messages from chat history")
        } catch {
            print("DEBUG: ❌ Failed to restore chat history: \(error)")
            // If restoration fails, start with empty messages array
            messages = []
        }
    }

    private func clearChatHistory() {
        UserDefaults.standard.removeObject(forKey: chatHistoryKey)
        print("DEBUG: 🗑️ Cleared chat history from storage")
    }

    // Update user credit from UserManager
    func updateUserCredit() {
        userCredit = UserManager.shared.currentUser.credit
        print("DEBUG: 💰 User credit: \(userCredit)")

        // If credit is low, refresh from server to get the most up-to-date value
        if userCredit < 5 {
            Task {
                await refreshUserProfile()
            }
        }
    }

    // Simple credit check that doesn't rely on full profile parsing
    func checkCreditBalance() async -> Int {
        guard let url = URL(string: "\(APIConfig.baseURL)/user/credit") else {
            print("DEBUG: 💰 Invalid credit check URL")
            return userCredit
        }

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("Bearer \(UserManager.shared.currentUser.id)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200,
               let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let credit = json["credit"] as? Int {

                print("DEBUG: 💰 Credit check successful: \(credit)")

                await MainActor.run {
                    userCredit = credit

                    // Update UserManager using the convenience method
                    UserManager.shared.updateUserCredit(credit)
                }

                return credit
            } else {
                print("DEBUG: 💰 Credit check failed or invalid response")
                return userCredit
            }
        } catch {
            print("DEBUG: 💰 Credit check error: \(error)")
            return userCredit
        }
    }

    // Refresh user profile from server to get the most up-to-date credit information
    func refreshUserProfile() async {
        print("DEBUG: 💰 Refreshing user profile to get updated credit information")
        let success = await UserManager.shared.fetchUserProfile()

        if success {
            await MainActor.run {
                userCredit = UserManager.shared.currentUser.credit
                print("DEBUG: 💰 Updated user credit from server: \(userCredit)")
            }
        } else {
            print("DEBUG: 💰 Failed to refresh user profile, trying credit check fallback")
            // If profile refresh fails, try the simpler credit check
            let creditFromServer = await checkCreditBalance()
            print("DEBUG: 💰 Credit from fallback check: \(creditFromServer)")
        }
    }
    
    func sendMessage() {
        guard !newMessageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        // Check if user has enough credit
        updateUserCredit() // Refresh credit amount

        if userCredit < minimumCreditRequired {
            // Show insufficient credit alert
            error = "You don't have enough credit to use the AI assistant. Please purchase more credit."
            showingInsufficientCreditAlert = true
            print("DEBUG: 💰 Insufficient credit: \(userCredit). Required: \(minimumCreditRequired)")
            return
        }

        let userMessage = SimpleMessage(
            content: newMessageText,
            isUser: true,
            timestamp: Date(),
            attachments: []
        )

        messages.append(userMessage)
        saveChatHistory() // Save after adding user message
        let userText = newMessageText
        newMessageText = ""

        // Convert messages to ChatMessage format for API
        let chatMessages = messages.map { $0.toChatMessage() }

        // Call real AI service
        isLoading = true
        isStreaming = true
        currentStreamingResponse = ""

        // Create a temporary streaming message
        let streamingMessage = SimpleMessage(
            content: "",
            isUser: false,
            timestamp: Date(),
            attachments: []
        )
        streamingMessageId = streamingMessage.id
        messages.append(streamingMessage)
        print("DEBUG: 📝 Created streaming message with ID: \(streamingMessageId?.uuidString ?? "nil")")
        print("DEBUG: 📝 Total messages: \(messages.count)")
        
        anythingLLMService.sendStreamingMessage(messages: chatMessages)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    self.isStreaming = false

                    switch completion {
                    case .finished:
                        // Message content is already updated, just clean up
                        self.currentStreamingResponse = ""
                        self.streamingMessageId = nil

                        // Save chat history after successful completion
                        self.saveChatHistory()

                        // Update user credit after successful completion
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            self.updateUserCredit()
                        }

                    case .failure(let error):
                        // Remove the streaming message on error
                        self.messages.removeAll { $0.id == self.streamingMessageId }
                        self.error = error.localizedDescription
                        self.currentStreamingResponse = ""
                        self.streamingMessageId = nil
                        print("DEBUG: ❌ AI Service Error: \(error)")
                    }
                },
                receiveValue: { chunk in
                    print("DEBUG: 📥 Received chunk: '\(chunk)'")
                    self.currentStreamingResponse += chunk
                    print("DEBUG: 📝 Total response so far: '\(self.currentStreamingResponse)'")
                    
                    // Update the streaming message content directly
                    if let index = self.messages.firstIndex(where: { $0.id == self.streamingMessageId }) {
                        print("DEBUG: 🔄 Updating message at index \(index)")
                        // Update the message content - class will automatically trigger UI updates
                        self.messages[index].content = self.currentStreamingResponse
                        print("DEBUG: ✅ Message updated. New content: '\(self.messages[index].content)'")
                    } else {
                        print("DEBUG: ❌ Could not find streaming message with ID: \(self.streamingMessageId?.uuidString ?? "nil")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func sendMessageWithAttachments(_ attachments: [FileAttachment]) {
        guard !newMessageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        // Check if user has enough credit
        updateUserCredit() // Refresh credit amount

        // Attachments typically require more tokens, so we require more credits
        let attachmentCreditRequirement = minimumCreditRequired + (attachments.count * 2)

        if userCredit < attachmentCreditRequirement {
            // Show insufficient credit alert
            error = "You don't have enough credit to use the AI assistant with attachments. Please purchase more credit."
            showingInsufficientCreditAlert = true
            print("DEBUG: 💰 Insufficient credit for attachments: \(userCredit). Required: \(attachmentCreditRequirement)")
            return
        }

        print("DEBUG: 📎 ViewModel: Processing message with \(attachments.count) attachments")
        for (index, attachment) in attachments.enumerated() {
            print("DEBUG: 📎 Attachment \(index + 1): \(attachment.name) (\(attachment.mimeType)) - \(attachment.contentString.count) chars")
        }

        let userMessage = SimpleMessage(
            content: newMessageText,
            isUser: true,
            timestamp: Date(),
            attachments: attachments
        )

        messages.append(userMessage)
        saveChatHistory() // Save after adding user message
        let userText = newMessageText
        newMessageText = ""

        // Convert messages to ChatMessage format for API
        let chatMessages = messages.map { $0.toChatMessage() }

        print("DEBUG: �� ViewModel: Sending \(chatMessages.count) messages to AI service with \(attachments.count) attachments")

        // Call real AI service with attachments
        isLoading = true
        isStreaming = true
        currentStreamingResponse = ""

        // Create a temporary streaming message
        let streamingMessage = SimpleMessage(
            content: "",
            isUser: false,
            timestamp: Date(),
            attachments: []
        )
        streamingMessageId = streamingMessage.id
        messages.append(streamingMessage)
        print("DEBUG: 📝 Created streaming message with ID: \(streamingMessageId?.uuidString ?? "nil")")
        print("DEBUG: 📝 Total messages: \(messages.count)")
        
        anythingLLMService.sendStreamingMessage(messages: chatMessages, attachments: attachments)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    self.isLoading = false
                    self.isStreaming = false

                    switch completion {
                    case .finished:
                        print("DEBUG: ✅ ViewModel: AI service completed successfully")
                        // Message content is already updated, just clean up
                        self.currentStreamingResponse = ""
                        self.streamingMessageId = nil

                        // Save chat history after successful completion
                        self.saveChatHistory()

                        // Update user credit after successful completion
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            self.updateUserCredit()
                        }

                    case .failure(let error):
                        // Remove the streaming message on error
                        self.messages.removeAll { $0.id == self.streamingMessageId }
                        self.error = error.localizedDescription
                        self.currentStreamingResponse = ""
                        self.streamingMessageId = nil
                        print("DEBUG: ❌ ViewModel: AI Service Error: \(error)")
                    }
                },
                receiveValue: { chunk in
                    print("DEBUG: 📥 Received chunk: '\(chunk)'")
                    self.currentStreamingResponse += chunk
                    print("DEBUG: 📝 Total response so far: '\(self.currentStreamingResponse)'")
                    
                    // Update the streaming message content directly
                    if let index = self.messages.firstIndex(where: { $0.id == self.streamingMessageId }) {
                        print("DEBUG: 🔄 Updating message at index \(index)")
                        // Update the message content - class will automatically trigger UI updates
                        self.messages[index].content = self.currentStreamingResponse
                        print("DEBUG: ✅ Message updated. New content: '\(self.messages[index].content)'")
                    } else {
                        print("DEBUG: ❌ Could not find streaming message with ID: \(self.streamingMessageId?.uuidString ?? "nil")")
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    func clearChat() {
        messages.removeAll()
        error = nil
        currentStreamingResponse = ""
        streamingMessageId = nil
        lastInputTokens = 0
        lastOutputTokens = 0
        totalTokensUsed = 0
        showingInsufficientCreditAlert = false
        anythingLLMService.resetSession()

        // Clear persisted chat history
        clearChatHistory()

        // Add welcome message back
        let welcomeMessage = SimpleMessage(
            content: "Hello! I'm your AI study assistant. I can help you with:\n\n• Explaining complex concepts\n• Solving math problems\n• Reviewing documents and images\n• Answering questions about your studies\n\nWhat would you like to learn today?",
            isUser: false,
            timestamp: Date(),
            attachments: []
        )
        messages.append(welcomeMessage)
        saveChatHistory() // Save the welcome message

        updateUserCredit() // Refresh credit when clearing chat
    }
}

struct AIStudyAssistantView: View {
    @StateObject private var viewModel = SimpleChatViewModel()
    @StateObject private var fileUploadManager = FileUploadManager()
    @FocusState private var isInputFocused: Bool
    @Environment(\.colorScheme) private var colorScheme
    @State private var showingDocumentPicker = false
    @State private var showingImagePicker = false
    @State private var showingCameraPicker = false
    @State private var viewAppearanceId = UUID()

    var body: some View {
        VStack(spacing: 0) {
            headerView
            messageListView
            errorView
            fileAttachmentsView
            inputAreaView
        }
        .sheet(isPresented: $showingDocumentPicker) {
            DocumentPicker(fileUploadManager: fileUploadManager)
        }
        .sheet(isPresented: $showingImagePicker) {
            PhotoPicker(fileUploadManager: fileUploadManager, sourceType: .photoLibrary)
        }
        .fullScreenCover(isPresented: $showingCameraPicker) {
            ZStack {
                CameraPicker(fileUploadManager: fileUploadManager, isPresented: $showingCameraPicker)
                    .ignoresSafeArea()
            }
        }
        .alert("Insufficient Credit", isPresented: $viewModel.showingInsufficientCreditAlert) {
            Button("OK", role: .cancel) {
                viewModel.error = nil
            }
        } message: {
            Text("You don't have enough credit to use the AI assistant. Please purchase more credit to continue.")
        }
        .onAppear {
            // Refresh credit when view appears
            viewModel.updateUserCredit()
            // Save current chat state when view appears (in case app was backgrounded)
            viewModel.saveChatHistory()
            // Force scroll to bottom by regenerating the view ID
            viewAppearanceId = UUID()
        }
    }
    
    // MARK: - View Components
    
    @ViewBuilder
    private var headerView: some View {
        HStack {
            Text("AI Study Assistant")
                .font(.title)
                .fontWeight(.bold)
                .padding(.leading)

            Spacer()

            // Credit display with refresh button
            Button(action: {
                Task {
                    await viewModel.refreshUserProfile()
                }
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "creditcard.fill")
                        .font(.system(size: 14))
                        .foregroundColor(.purple)
                    Text("\(viewModel.userCredit)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                // .background(
                //     RoundedRectangle(cornerRadius: 6)
                //         .fill(Color.orange.opacity(0.1))
                // )
            }

            Button(action: {
                withAnimation {
                    viewModel.clearChat()
                    fileUploadManager.clearAllFiles()
                }
            }) {
                HStack(spacing: 6) {
                    Text("New Chat")
                        .font(.subheadline)
                        .fontWeight(.medium)
                    Image(systemName: "arrow.clockwise")
                        .font(.system(size: 16))
                }
                .foregroundColor(.blue)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.blue.opacity(0.1))
                )
            }
            .padding(.trailing)
        }
        .padding(.vertical, 10)
        .background(headerBackgroundColor)
        .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
    
    @ViewBuilder
    private var messageListView: some View {
        ScrollViewReader { scrollProxy in
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(viewModel.visibleMessages) { message in
                        messageBubble(for: message)
                    }

                    // Automatic scrolling when new messages appear
                    if !viewModel.visibleMessages.isEmpty {
                        Color.clear
                            .frame(height: 1)
                            .id("bottom")
                    }
                }
                .padding(.horizontal)
                .padding(.top, 10)
                .padding(.bottom, 5)
            }
            .id("messages-\(viewAppearanceId)")
            .onAppear {
                // Scroll to bottom when messages view appears (when view is reopened)
                if !viewModel.visibleMessages.isEmpty {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        scrollProxy.scrollTo("bottom", anchor: .bottom)
                    }
                }
            }
            .onChange(of: viewModel.visibleMessages.count) { newCount in
                withAnimation {
                    scrollProxy.scrollTo("bottom", anchor: .bottom)
                }
            }
            .onChange(of: viewModel.currentStreamingResponse) { _ in
                withAnimation {
                    scrollProxy.scrollTo("bottom", anchor: .bottom)
                }
            }
        }
    }
    

    
    @ViewBuilder
    private var errorView: some View {
        if let error = viewModel.error {
            Text(error)
                .foregroundColor(.red)
                .font(.caption)
                .padding(.horizontal)
                .padding(.top, 5)
        }
    }
    
    @ViewBuilder
    private var fileAttachmentsView: some View {
        if !fileUploadManager.selectedFiles.isEmpty {
            FileUploadView(fileUploadManager: fileUploadManager)
                .padding(.horizontal)
                .padding(.top, 5)
        }
    }
    
    @ViewBuilder
    private var inputAreaView: some View {
        VStack(spacing: 8) {
            fileUploadButtonRow
            messageInputRow
        }
        .padding(.vertical, 10)
        .background(inputAreaBackgroundColor)
        .overlay(
            Rectangle()
                .frame(height: 0.5)
                .foregroundColor(Color.gray.opacity(0.3)),
            alignment: .top
        )
    }
    
    @ViewBuilder
    private var fileUploadButtonRow: some View {
        HStack {
            // File type selection buttons
            HStack(spacing: 8) {
                Button(action: {
                    showingImagePicker = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "photo")
                            .font(.system(size: 14))
                        Text("Photo")
                            .font(.caption)
                    }
                    .foregroundColor(.blue)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
                }
                Button(action: {
                    showingCameraPicker = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "camera")
                            .font(.system(size: 14))
                        Text("Camera")
                            .font(.caption)
                    }
                    .foregroundColor(.orange)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.1))
                    .cornerRadius(8)
                }
                Button(action: {
                    showingDocumentPicker = true
                }) {
                    HStack(spacing: 4) {
                        Image(systemName: "doc")
                            .font(.system(size: 14))
                        Text("Document")
                            .font(.caption)
                    }
                    .foregroundColor(.green)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                }
            }
            
            Spacer()
            
            fileCountIndicator
        }
        .padding(.horizontal)
    }
    
    @ViewBuilder
    private var fileCountIndicator: some View {
        if !fileUploadManager.selectedFiles.isEmpty {
            let fileCount = fileUploadManager.selectedFiles.count
            let fileText = fileCount == 1 ? "file" : "files"
            Text("\(fileCount) \(fileText)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    @ViewBuilder
    private var messageInputRow: some View {
        HStack {
            TextField("Ask me anything about your studies...", text: $viewModel.newMessageText)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(inputBackgroundColor)
                )
                .focused($isInputFocused)
            
            Button(action: {
                if viewModel.userCredit < 1 {
                    viewModel.showingInsufficientCreditAlert = true
                    viewModel.error = "You don't have enough credit to use the AI assistant."
                } else {
                    sendMessageWithAttachments()
                    isInputFocused = false
                }
            }) {
                Image(systemName: "arrow.up.circle.fill")
                    .resizable()
                    .frame(width: 30, height: 30)
                    .foregroundColor(viewModel.userCredit < 1 ? .gray : .blue)
            }
            .disabled(isSendButtonDisabled)
        }
        .padding(.horizontal)
    }
    
    @ViewBuilder
    private func messageBubble(for message: SimpleMessage) -> some View {
        let isLastMessage = message == viewModel.visibleMessages.last
        let shouldShowStreaming = viewModel.isStreaming && isLastMessage && !message.isUser
        
        SimpleMessageBubble(message: message, isStreaming: shouldShowStreaming)
            .id(message.id)
    }
    
    // MARK: - Computed Properties
    
    private var headerBackgroundColor: Color {
        colorScheme == .dark ? Color.black.opacity(0.3) : Color.white
    }
    
    private var inputBackgroundColor: Color {
        colorScheme == .dark ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1)
    }
    
    private var inputAreaBackgroundColor: Color {
        colorScheme == .dark ? Color.black.opacity(0.3) : Color.white
    }
    
    private var isSendButtonDisabled: Bool {
        viewModel.isLoading ||
        viewModel.newMessageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ||
        viewModel.userCredit < 1 // Disable if user has insufficient credit
    }
    
    // MARK: - Helper Functions
    
    private func sendMessageWithAttachments() {
        let attachments = fileUploadManager.selectedFiles
        print("DEBUG: 📎 Sending message with \(attachments.count) attachments")
        
        if attachments.isEmpty {
            // No attachments, use regular send
            viewModel.sendMessage()
        } else {
            // Has attachments, use attachment method
            viewModel.sendMessageWithAttachments(attachments)
        }
        
        // Clear files after sending
        fileUploadManager.clearAllFiles()
    }
}

// MARK: - Document Picker
struct DocumentPicker: UIViewControllerRepresentable {
    @ObservedObject var fileUploadManager: FileUploadManager
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIDocumentPickerViewController {
        let picker = UIDocumentPickerViewController(forOpeningContentTypes: [
            .pdf,
            .text,
            .plainText,
            UTType("public.comma-separated-values-text")!, // CSV
            UTType("net.daringfireball.markdown")!, // Markdown
            UTType("org.openxmlformats.wordprocessingml.document")!, // Word DOCX
            UTType("com.microsoft.word.doc")!, // Word DOC
            UTType("org.openxmlformats.spreadsheetml.sheet")!, // Excel XLSX
            UTType("com.microsoft.excel.xls")!, // Excel XLS
            UTType("org.openxmlformats.presentationml.presentation")!, // PowerPoint PPTX
            UTType("com.microsoft.powerpoint.ppt")! // PowerPoint PPT
        ])
        picker.delegate = context.coordinator
        picker.allowsMultipleSelection = true
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIDocumentPickerViewController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIDocumentPickerDelegate {
        let parent: DocumentPicker
        
        init(_ parent: DocumentPicker) {
            self.parent = parent
        }
        
        func documentPicker(_ controller: UIDocumentPickerViewController, didPickDocumentsAt urls: [URL]) {
            print("DEBUG: 📎 DocumentPicker: Selected \(urls.count) documents")
            
            Task {
                for (index, url) in urls.enumerated() {
                    print("DEBUG: 📎 DocumentPicker: Processing document \(index + 1): \(url.lastPathComponent)")
                    
                    do {
                        // Start accessing the security-scoped resource
                        guard url.startAccessingSecurityScopedResource() else {
                            print("DEBUG: ❌ DocumentPicker: Failed to access security-scoped resource for \(url.lastPathComponent)")
                            await MainActor.run {
                                parent.fileUploadManager.error = "Failed to access file: \(url.lastPathComponent)"
                            }
                            continue
                        }
                        
                        defer {
                            // Stop accessing the security-scoped resource
                            url.stopAccessingSecurityScopedResource()
                        }
                        
                        // Create a temporary copy of the file in the app's temporary directory
                        let tempURL = FileManager.default.temporaryDirectory
                            .appendingPathComponent("\(UUID().uuidString)_\(url.lastPathComponent)")
                        
                        do {
                            // Copy the file to temporary location
                            try FileManager.default.copyItem(at: url, to: tempURL)
                            print("DEBUG: ✅ DocumentPicker: Successfully copied file to temp location: \(tempURL.path)")
                            
                            // Process the temporary file
                            let fileAttachment = try await parent.fileUploadManager.processFile(at: tempURL)
                            print("DEBUG: ✅ DocumentPicker: Successfully processed \(fileAttachment.name) (\(fileAttachment.mimeType))")
                            
                            await MainActor.run {
                                parent.fileUploadManager.addFiles([fileAttachment])
                                print("DEBUG: 📎 DocumentPicker: Added file to manager. Total files: \(parent.fileUploadManager.selectedFiles.count)")
                            }
                            
                            // Clean up temporary file
                            try? FileManager.default.removeItem(at: tempURL)
                            
                        } catch {
                            print("DEBUG: ❌ DocumentPicker: Failed to copy file \(url.lastPathComponent): \(error)")
                            await MainActor.run {
                                parent.fileUploadManager.error = "Failed to process file: \(url.lastPathComponent) - \(error.localizedDescription)"
                            }
                        }
                        
                    } catch {
                        print("DEBUG: ❌ DocumentPicker: Failed to process \(url.lastPathComponent): \(error)")
                        await MainActor.run {
                            parent.fileUploadManager.error = error.localizedDescription
                        }
                    }
                }
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// MARK: - Photo Picker
struct PhotoPicker: UIViewControllerRepresentable {
    @ObservedObject var fileUploadManager: FileUploadManager
    @Environment(\.presentationMode) var presentationMode
    var sourceType: UIImagePickerController.SourceType = .photoLibrary
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = sourceType
        picker.allowsEditing = false
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: PhotoPicker
        
        init(_ parent: PhotoPicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage,
               let imageData = image.jpegData(compressionQuality: 0.8) {
                
                // Create a temporary URL for the image
                let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("image_\(Date().timeIntervalSince1970).jpg")
                
                do {
                    try imageData.write(to: tempURL)
                    
                    Task {
                        do {
                            let fileAttachment = try await parent.fileUploadManager.processFile(at: tempURL)
                            await MainActor.run {
                                parent.fileUploadManager.addFiles([fileAttachment])
                            }
                        } catch {
                            await MainActor.run {
                                parent.fileUploadManager.error = error.localizedDescription
                            }
                        }
                    }
                } catch {
                    parent.fileUploadManager.error = "Failed to save image: \(error.localizedDescription)"
                }
            }
            
            parent.presentationMode.wrappedValue.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}

// Add CameraPicker for correct orientation
import UIKit
struct CameraPicker: UIViewControllerRepresentable {
    @ObservedObject var fileUploadManager: FileUploadManager
    @Binding var isPresented: Bool

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = false
        picker.modalPresentationStyle = .fullScreen
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraPicker
        init(_ parent: CameraPicker) {
            self.parent = parent
        }
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage,
               let imageData = image.jpegData(compressionQuality: 0.8) {
                let tempURL = FileManager.default.temporaryDirectory.appendingPathComponent("image_\(Date().timeIntervalSince1970).jpg")
                do {
                    try imageData.write(to: tempURL)
                    Task {
                        do {
                            let fileAttachment = try await parent.fileUploadManager.processFile(at: tempURL)
                            await MainActor.run {
                                parent.fileUploadManager.addFiles([fileAttachment])
                            }
                        } catch {
                            await MainActor.run {
                                parent.fileUploadManager.error = error.localizedDescription
                            }
                        }
                    }
                } catch {
                    parent.fileUploadManager.error = "Failed to save image: \(error.localizedDescription)"
                }
            }
            parent.isPresented = false
        }
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}

// Simple message bubble component
struct SimpleMessageBubble: View {
    @ObservedObject var message: SimpleMessage
    var isStreaming: Bool = false
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack {
            if message.isUser {
                Spacer()
            }
            
            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 5) {
                attachmentsView
                messageContent
                timestampView
            }
            
            if !message.isUser {
                Spacer()
            }
        }
    }
    
    @ViewBuilder
    private var messageContent: some View {
        HStack {
            if isStreaming && !message.isUser && message.content.isEmpty {
                // Show "Thinking..." when streaming starts and no content yet
                HStack(spacing: 8) {
                    Text("Thinking...")
                        .foregroundColor(message.isUser ? .white : .secondary)
                        .font(.subheadline)
                        .italic()
                    
                    // Animated dots
                    HStack(spacing: 2) {
                        ForEach(0..<3) { index in
                            Circle()
                                .fill(message.isUser ? Color.white : Color.gray)
                                .frame(width: 4, height: 4)
                                .scaleEffect(0.8)
                                .animation(
                                    Animation.easeInOut(duration: 0.6)
                                        .repeatForever()
                                        .delay(Double(index) * 0.2),
                                    value: isStreaming
                                )
                        }
                    }
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(messageBackgroundColor)
                )
            } else {
                // Show actual message content with formatted text
                MessageContentView(content: message.content, isFromUser: message.isUser)
            }
        }
    }
    
    @ViewBuilder
    private var attachmentsView: some View {
        if !message.attachments.isEmpty {
            VStack(alignment: message.isUser ? .trailing : .leading, spacing: 4) {
                ForEach(message.attachments) { attachment in
                    MessageAttachmentView(file: attachment, isUser: message.isUser)
                        .frame(maxWidth: 200)
                }
            }
        }
    }
    
    @ViewBuilder
    private var timestampView: some View {
        Text(message.timestamp, style: .time)
            .font(.caption2)
            .foregroundColor(.secondary)
    }
    
    private var messageBackgroundColor: Color {
        if message.isUser {
            return Color.blue
        } else {
            return colorScheme == .dark ? Color.gray.opacity(0.3) : Color.gray.opacity(0.1)
        }
    }
}

// Message attachment view (read-only, no remove button)
struct MessageAttachmentView: View {
    let file: FileAttachment
    let isUser: Bool
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        HStack(spacing: 8) {
            // File icon
            Image(systemName: iconName)
                .foregroundColor(iconColor)
                .font(.system(size: 16))
                .frame(width: 20)
            
            // File info
            VStack(alignment: .leading, spacing: 2) {
                Text(file.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text(file.formattedFileSize)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer(minLength: 0)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 6)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(attachmentBackgroundColor)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(attachmentBorderColor, lineWidth: 1)
        )
    }
    
    private var iconName: String {
        if file.mimeType.hasPrefix("image/") {
            return "photo"
        } else if file.mimeType == "application/pdf" {
            return "doc.text"
        } else if file.mimeType.hasPrefix("text/") {
            return "doc.plaintext"
        } else if file.mimeType.contains("word") {
            return "doc.text"
        } else if file.mimeType.contains("excel") || file.mimeType.contains("spreadsheet") {
            return "tablecells"
        } else if file.mimeType.contains("powerpoint") || file.mimeType.contains("presentation") {
            return "chart.bar.doc.horizontal"
        } else {
            return "doc"
        }
    }
    
    private var iconColor: Color {
        if file.mimeType.hasPrefix("image/") {
            return .green
        } else if file.mimeType == "application/pdf" {
            return .red
        } else if file.mimeType.hasPrefix("text/") {
            return .purple
        } else if file.mimeType.contains("word") {
            return .blue
        } else if file.mimeType.contains("excel") || file.mimeType.contains("spreadsheet") {
            return .green
        } else if file.mimeType.contains("powerpoint") || file.mimeType.contains("presentation") {
            return .orange
        } else {
            return .gray
        }
    }
    
    private var attachmentBackgroundColor: Color {
        if isUser {
            return Color.white.opacity(0.2)
        } else {
            return colorScheme == .dark ? Color.white.opacity(0.1) : Color.gray.opacity(0.1)
        }
    }
    
    private var attachmentBorderColor: Color {
        if isUser {
            return Color.white.opacity(0.3)
        } else {
            return Color.gray.opacity(0.3)
        }
    }
}

// KaTeX-based math view - fast and lightweight
struct KaTeXMathView: View {
    let expression: String
    let isFromUser: Bool
    @State private var height: CGFloat = 40
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        KaTeXWebView(
            expression: expression,
            textColor: isFromUser ? .white : (colorScheme == .dark ? .white : .black),
            height: $height
        )
        .frame(height: height)
        .frame(maxWidth: .infinity, alignment: isFromUser ? .trailing : .leading)
    }
}

// Lightweight WebView specifically for KaTeX
struct KaTeXWebView: UIViewRepresentable {
    let expression: String
    let textColor: Color
    @Binding var height: CGFloat
    
    func makeUIView(context: Context) -> WKWebView {
        let config = WKWebViewConfiguration()
        config.userContentController.add(context.coordinator, name: "heightUpdate")
        
        let webView = WKWebView(frame: .zero, configuration: config)
        webView.navigationDelegate = context.coordinator
        webView.isOpaque = false
        webView.backgroundColor = UIColor.clear
        webView.scrollView.isScrollEnabled = false
        webView.scrollView.bounces = false
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        let cleanedExpression = cleanLatexExpression(expression)
        let textColorHex = hexString(from: textColor)
        
        // Properly escape the LaTeX for JavaScript
        let escapedExpression = cleanedExpression
            .replacingOccurrences(of: "\\", with: "\\\\")
            .replacingOccurrences(of: "'", with: "\\'")
            .replacingOccurrences(of: "\"", with: "\\\"")
            .replacingOccurrences(of: "\n", with: "\\n")
            .replacingOccurrences(of: "\r", with: "\\r")
        
        let htmlContent = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
            <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
            <style>
                body {
                    margin: 0;
                    padding: 8px;
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                    background-color: transparent;
                    color: \(textColorHex);
                    line-height: 1.4;
                }
                * {
                    color: \(textColorHex) !important;
                }
                .math {
                    display: inline-block;
                    font-size: 16px;
                }
                .katex {
                    color: \(textColorHex) !important;
                }
                .katex * {
                    color: \(textColorHex) !important;
                }
                .katex .mord, .katex .mop, .katex .mbin, .katex .mrel, .katex .mopen, .katex .mclose, .katex .mpunct {
                    color: \(textColorHex) !important;
                }
                .katex .mfrac, .katex .mfrac > *, .katex .mroot, .katex .mroot > * {
                    color: \(textColorHex) !important;
                }
                .katex .msqrt, .katex .msqrt > *, .katex .msubsup, .katex .msubsup > * {
                    color: \(textColorHex) !important;
                }
                .katex .base, .katex .strut, .katex .mathdefault, .katex .mathit, .katex .mathrm {
                    color: \(textColorHex) !important;
                }
                .katex span, .katex .vlist, .katex .vlist-t, .katex .vlist-r {
                    color: \(textColorHex) !important;
                }
                .katex .frac-line, .katex .sqrt-line, .katex .overline-line, .katex .underline-line {
                    border-color: \(textColorHex) !important;
                    background-color: \(textColorHex) !important;
                }
                .katex-display {
                    margin: 0.5em 0;
                }
                .fallback {
                    font-family: serif;
                    font-style: italic;
                    color: \(textColorHex);
                }
            </style>
        </head>
        <body>
            <div id="math-container" class="math"></div>
            
            <script>
                function renderMath() {
                    try {
                        const container = document.getElementById('math-container');
                        const expression = '\(escapedExpression)';
                        
                        console.log('Rendering expression:', expression);
                        
                        // Determine if it's display mode (block math)
                        const isDisplayMode = expression.startsWith('$$') && expression.endsWith('$$') ||
                                            expression.startsWith('\\\\[') && expression.endsWith('\\\\]');
                        
                        let mathExpression = expression;
                        if (isDisplayMode) {
                            // Remove display delimiters
                            if (mathExpression.startsWith('$$') && mathExpression.endsWith('$$')) {
                                mathExpression = mathExpression.slice(2, -2);
                            } else if (mathExpression.startsWith('\\\\[') && mathExpression.endsWith('\\\\]')) {
                                mathExpression = mathExpression.slice(3, -3);
                            }
                        } else {
                            // Remove inline delimiters
                            if (mathExpression.startsWith('$') && mathExpression.endsWith('$')) {
                                mathExpression = mathExpression.slice(1, -1);
                            } else if (mathExpression.startsWith('\\\\(') && mathExpression.endsWith('\\\\)')) {
                                mathExpression = mathExpression.slice(3, -3);
                            }
                        }
                        
                        katex.render(mathExpression, container, {
                            throwOnError: false,
                            displayMode: isDisplayMode,
                            output: 'html',
                            trust: false,
                            strict: false
                        });
                        
                        // Update height after rendering
                        setTimeout(() => {
                            const height = Math.max(document.body.scrollHeight, 30);
                            window.webkit.messageHandlers.heightUpdate.postMessage(height);
                        }, 150);
                        
                    } catch (error) {
                        console.log('KaTeX error:', error);
                        // Fallback to plain text
                        const container = document.getElementById('math-container');
                        container.innerHTML = '<span class="fallback">' + '\(escapedExpression)' + '</span>';
                        
                        setTimeout(() => {
                            const height = Math.max(document.body.scrollHeight, 30);
                            window.webkit.messageHandlers.heightUpdate.postMessage(height);
                        }, 100);
                    }
                }
                
                // Wait for KaTeX to load, then render
                function checkKaTeXAndRender() {
                    if (typeof katex !== 'undefined') {
                        renderMath();
                    } else {
                        setTimeout(checkKaTeXAndRender, 100);
                    }
                }
                
                if (document.readyState === 'loading') {
                    document.addEventListener('DOMContentLoaded', checkKaTeXAndRender);
                } else {
                    checkKaTeXAndRender();
                }
            </script>
        </body>
        </html>
        """
        
        webView.loadHTMLString(htmlContent, baseURL: nil)
    }
    
    // Local clean LaTeX function - more robust than the extension
    private func cleanLatexExpression(_ expression: String) -> String {
        var cleaned = expression.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove common malformed patterns first
        cleaned = cleaned.replacingOccurrences(of: "$}$", with: "")
        cleaned = cleaned.replacingOccurrences(of: "$$}$$", with: "")
        cleaned = cleaned.replacingOccurrences(of: "$$}}", with: "")
        cleaned = cleaned.replacingOccurrences(of: "$}}", with: "")
        
        // Fix multiple consecutive dollar signs aggressively
        cleaned = cleaned.replacingOccurrences(of: "$$$$", with: "$$")
        cleaned = cleaned.replacingOccurrences(of: "$$$", with: "$")
        
        // Remove any remaining redundant dollar signs within the expression
        // This handles cases like $5 + 3 $ becoming $ + 3 $ after processing
        let redundantDollarPattern = #"\$+([^$]*?)\$+"#
        do {
            let regex = try NSRegularExpression(pattern: redundantDollarPattern, options: [])
            let range = NSRange(cleaned.startIndex..<cleaned.endIndex, in: cleaned)
            let matches = regex.matches(in: cleaned, options: [], range: range)
            
            // Work backwards to avoid index shifting
            for match in matches.reversed() {
                if let matchRange = Range(match.range, in: cleaned),
                   let captureRange = Range(match.range(at: 1), in: cleaned) {
                    let capturedContent = String(cleaned[captureRange])
                    // Only replace if the captured content doesn't look like it should have dollars
                    if !capturedContent.contains("\\") && !capturedContent.contains("{") {
                        cleaned.replaceSubrange(matchRange, with: capturedContent)
                    }
                }
            }
        } catch {
            print("Error processing redundant dollars: \(error)")
        }
        
        // Fix common LaTeX command issues
        cleaned = cleaned.replacingOccurrences(of: "\\\\frac", with: "\\frac")
        cleaned = cleaned.replacingOccurrences(of: "\\\\sqrt", with: "\\sqrt")
        cleaned = cleaned.replacingOccurrences(of: "\\\\text", with: "\\text")
        
        // Remove extra closing braces at the end
        while cleaned.hasSuffix("}") && !cleaned.hasPrefix("{") {
            let openBraces = cleaned.filter { $0 == "{" }.count
            let closeBraces = cleaned.filter { $0 == "}" }.count
            if closeBraces > openBraces {
                cleaned = String(cleaned.dropLast())
            } else {
                break
            }
        }
        
        return cleaned
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate, WKScriptMessageHandler {
        var parent: KaTeXWebView
        
        init(_ parent: KaTeXWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            // Navigation completed
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            print("WebView navigation failed: \(error)")
        }
        
        func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
            if message.name == "heightUpdate", let height = message.body as? CGFloat {
                DispatchQueue.main.async {
                    self.parent.height = max(height, 30) // Minimum height
                }
            }
        }
    }
    
    private func hexString(from color: Color) -> String {
        // Convert SwiftUI Color to hex string
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        return String(
            format: "#%02X%02X%02X",
            Int(red * 255),
            Int(green * 255),
            Int(blue * 255)
        )
    }
}

// ContentSegment is now defined in LaTeXProcessor

// Modern message content view that handles both markdown and math
struct MessageContentView: View {
    let content: String
    let isFromUser: Bool
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        MarkdownMathView(content: content, isFromUser: isFromUser)
    }
    

    
    // Legacy LaTeX validation methods removed - now handled by LaTeXProcessor
}

#Preview {
    AIStudyAssistantView()
} 

