//
//  TypingIndicator.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI

struct TypingIndicator: View {
    @State private var phase = 0.0
    
    var body: some View {
        HStack(spacing: 3) {
            ForEach(0..<3) { index in
                Circle()
                    .fill(Color.gray.opacity(0.5))
                    .frame(width: 4, height: 4)
                    .scaleEffect(getScale(for: index))
                    .animation(.easeInOut(duration: 0.3).repeatForever(), value: phase)
            }
        }
        .padding(.vertical, 2)
        .onAppear {
            withAnimation {
                phase = 1.0
            }
        }
    }
    
    private func getScale(for index: Int) -> CGFloat {
        let delay = 0.2
        let durationPerDot = 0.6
        let currentTime = phase.truncatingRemainder(dividingBy: durationPerDot * 3)
        let dotTime = index * Int(delay)
        
        let cyclePosition = currentTime / durationPerDot
        let dotCycle = cyclePosition - Double(dotTime)
        
        if (0.0...1.0).contains(dotCycle) {
            return 1.0 + 0.5 * sin(dotCycle * .pi)
        }
        
        return 1.0
    }
} 