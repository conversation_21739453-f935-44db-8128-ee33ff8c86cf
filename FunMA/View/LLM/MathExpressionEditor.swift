//
//  MathExpressionEditor.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI

struct MathExpressionEditor: View {
    let expression: String
    let isBlock: Bool
    
    @State private var showingOptions = false
    @State private var showingSource = false
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(isBlock ? "DISPLAY MATH" : "INLINE MATH")
                    .font(.system(size: 10, weight: .semibold))
                    .foregroundColor(colorScheme == .dark ? .gray : .secondary)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                
                Spacer()
                
                Menu {
                    Button(action: {
                        UIPasteboard.general.string = expression
                    }) {
                        Label("Copy LaTeX", systemImage: "doc.on.doc")
                    }
                    
                    Button(action: {
                        showingSource.toggle()
                    }) {
                        Label("View Source", systemImage: "chevron.left.forwardslash.chevron.right")
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .font(.system(size: 14))
                        .foregroundColor(colorScheme == .dark ? .gray : .secondary)
                        .padding(8)
                }
            }
            .background(colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
            .clipShape(MathExpressionUI.MathRoundedCorner(radius: 4, corners: [.topLeft, .topRight]))
            
            if showingSource {
                Text(expression)
                    .font(.system(.body, design: .monospaced))
                    .padding(8)
                    .foregroundColor(colorScheme == .dark ? .gray : .secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(colorScheme == .dark ? Color.black.opacity(0.2) : Color.gray.opacity(0.05))
                    .textSelection(.enabled)
            }
        }
    }
}

// Extension to apply corner radius to specific corners
// Use MathExpressionUI namespace to avoid conflicts
enum MathExpressionUI {
    
    // Extension to apply corner radius to specific corners
    static func cornerRadius(_ view: some View, radius: CGFloat, corners: UIRectCorner) -> some View {
        view.clipShape(MathRoundedCorner(radius: radius, corners: corners))
    }
    
    // RoundedCorner shape with unique name
    struct MathRoundedCorner: Shape {
        var radius: CGFloat = .infinity
        var corners: UIRectCorner = .allCorners
        
        func path(in rect: CGRect) -> Path {
            let path = UIBezierPath(roundedRect: rect, byRoundingCorners: corners, cornerRadii: CGSize(width: radius, height: radius))
            return Path(path.cgPath)
        }
    }
}

struct MathExpressionEditor_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            MathExpressionEditor(expression: "E = mc^2", isBlock: false)
            
            MathExpressionEditor(expression: "\\int_{0}^{\\infty} e^{-x^2} dx = \\frac{\\sqrt{\\pi}}{2}", isBlock: true)
        }
        .padding()
    }
} 