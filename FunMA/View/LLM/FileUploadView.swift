import SwiftUI
import PhotosUI

// Import the Models to access FileUploadManager and FileAttachment
// Note: In Swift, we don't need explicit imports for files in the same module,
// but we need to ensure the Models are accessible

struct FileUploadView: View {
    @ObservedObject var fileUploadManager: FileUploadManager
    @State private var showingDocumentPicker = false
    @State private var showingImagePicker = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Text("Attachments")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
                
                // File type selection buttons
                HStack(spacing: 8) {
                    Button(action: {
                        showingImagePicker = true
                    }) {
                        Label("Photo", systemImage: "photo")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(6)
                    }
                    
                    Button(action: {
                        showingDocumentPicker = true
                    }) {
                        Label("Document", systemImage: "doc")
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(Color.green.opacity(0.1))
                            .foregroundColor(.green)
                            .cornerRadius(6)
                    }
                }
            }
            
            // Selected files list
            if !fileUploadManager.selectedFiles.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    ForEach(fileUploadManager.selectedFiles) { file in
                        FileAttachmentRow(file: file) {
                            fileUploadManager.removeFile(file)
                        }
                    }
                }
                .padding(.vertical, 4)
            } else {
                Text("No files attached")
                    .foregroundColor(.secondary)
                    .italic()
                    .padding(.vertical, 8)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
        .sheet(isPresented: $showingImagePicker) {
            Text("Image Picker - To be implemented")
                .padding()
        }
        .sheet(isPresented: $showingDocumentPicker) {
            Text("Document Picker - To be implemented")
                .padding()
        }
    }
}

struct FileAttachmentRow: View {
    let file: FileAttachment
    let onRemove: () -> Void
    
    var body: some View {
        HStack {
            // File icon
            Image(systemName: iconName)
                .foregroundColor(.blue)
                .font(.title2)
                .frame(width: 30)
            
            // File info
            VStack(alignment: .leading, spacing: 2) {
                Text(file.displayName)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .lineLimit(1)
                
                Text("\(file.mimeType) • \(file.formattedFileSize)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            // Remove button
            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(.red)
                    .font(.title3)
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color.white)
        .cornerRadius(8)
    }
    
    private var iconName: String {
        if file.mimeType.hasPrefix("image/") {
            return "photo"
        } else if file.mimeType == "application/pdf" {
            return "doc.text"
        } else if file.mimeType.hasPrefix("text/") {
            return "doc.plaintext"
        } else if file.mimeType.contains("word") {
            return "doc.text"
        } else if file.mimeType.contains("excel") || file.mimeType.contains("spreadsheet") {
            return "tablecells"
        } else if file.mimeType.contains("powerpoint") || file.mimeType.contains("presentation") {
            return "chart.bar.doc.horizontal"
        } else {
            return "doc"
        }
    }
}

#Preview {
    FileUploadView(fileUploadManager: FileUploadManager())
        .padding()
} 