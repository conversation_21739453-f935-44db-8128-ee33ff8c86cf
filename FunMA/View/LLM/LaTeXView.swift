//
//  LaTeXView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import WebKit
import UIKit

struct LaTeXView: UIViewRepresentable {
    let latexString: String
    let textColor: UIColor
    let backgroundColor: UIColor
    let padding: CGFloat
    @Binding var dynamicHeight: CGFloat
    private let offlineMode: Bool
    
    init(latexString: String, 
         textColor: UIColor = .black, 
         backgroundColor: UIColor = .clear,
         padding: CGFloat = 10,
         dynamicHeight: Binding<CGFloat> = .constant(40),
         offlineMode: Bool = false) {
        // Clean the input string by escaping special characters
        self.latexString = latexString
            .replacingOccurrences(of: "\\", with: "\\\\")
            .replacingOccurrences(of: "\"", with: "\\\"")
            .replacingOccurrences(of: "\n", with: " ")
        self.textColor = textColor
        self.backgroundColor = backgroundColor
        self.padding = padding
        self._dynamicHeight = dynamicHeight
        self.offlineMode = offlineMode
    }
    
    func makeUIView(context: Context) -> WKWebView {
        let config = WKWebViewConfiguration()
        config.userContentController.add(context.coordinator, name: "heightUpdate")
        
        let webView = WKWebView(frame: .zero, configuration: config)
        webView.backgroundColor = backgroundColor
        webView.isOpaque = false
        webView.scrollView.isScrollEnabled = false
        webView.scrollView.bounces = false
        webView.navigationDelegate = context.coordinator
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        let textColorString = hexString(from: textColor)
        
        // The HTML template with MathJax for rendering LaTeX
        let htmlContent = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">
            <script src=\"https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js\"></script>
            <style>
                body {
                    margin: 0;
                    padding: \(padding)px;
                    font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                    color: \(textColorString);
                    font-size: 16px;
                    background-color: transparent;
                }
                .math-container {
                    display: inline-block;
                }
                .fallback {
                    font-family: monospace;
                    white-space: pre-wrap;
                }
            </style>
        </head>
        <body>
            <div class=\"math-container\">
                \\(\(latexString)\\)
            </div>
            
            <script>
                // Fallback if MathJax fails to load after a timeout
                var mathJaxTimeout = setTimeout(function() {
                    document.querySelector('.math-container').innerHTML = 
                        '<div class="fallback">' + '\(latexString)' + '</div>';
                    window.webkit.messageHandlers.heightUpdate.postMessage(document.body.scrollHeight);
                }, 3000);
                
                // Adjust webview height after content loads
                window.addEventListener('load', function() {
                    if (typeof MathJax !== 'undefined') {
                        clearTimeout(mathJaxTimeout);
                        // Wait for MathJax to finish rendering
                        MathJax.startup.promise.then(() => {
                            const height = document.body.scrollHeight;
                            window.webkit.messageHandlers.heightUpdate.postMessage(height);
                        });
                    }
                });
            </script>
        </body>
        </html>
        """
        
        // Use a simple fallback for offline mode
        let offlineContent = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">
            <style>
                body {
                    margin: 0;
                    padding: \(padding)px;
                    font-family: monospace;
                    color: \(textColorString);
                    background-color: transparent;
                }
                .math {
                    white-space: pre-wrap;
                    font-style: italic;
                }
            </style>
        </head>
        <body>
            <div class=\"math\">\(latexString)</div>
            <script>
                window.webkit.messageHandlers.heightUpdate.postMessage(document.body.scrollHeight);
            </script>
        </body>
        </html>
        """
        
        webView.loadHTMLString(offlineMode ? offlineContent : htmlContent, baseURL: nil)
    }
    
    // Clean up resources when the view disappears
    static func dismantleUIView(_ webView: WKWebView, coordinator: Coordinator) {
        webView.configuration.userContentController.removeScriptMessageHandler(forName: "heightUpdate")
        webView.navigationDelegate = nil
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate, WKScriptMessageHandler {
        var parent: LaTeXView
        
        init(_ parent: LaTeXView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            // Navigation completed
        }
        
        func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
            if message.name == "heightUpdate", let height = message.body as? CGFloat {
                // Update the parent view height
                DispatchQueue.main.async {
                    self.parent.dynamicHeight = height + 5 // Add a small buffer
                }
            }
        }
    }
    
    // Helper function to convert UIColor to hex string
    private func hexString(from color: UIColor) -> String {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        return String(
            format: "#%02X%02X%02X",
            Int(red * 255),
            Int(green * 255),
            Int(blue * 255)
        )
    }
}

// Legacy LaTeX extension - deprecated in favor of LaTeXProcessor
extension String {
    var isBlockDisplayLatex: Bool {
        return hasPrefix("$$") || (hasPrefix("\\[") && hasSuffix("\\]"))
    }
    
    func cleanLatex() -> String {
        // Deprecated: Use LaTeXProcessor.shared.cleanLaTeX() instead
        return LaTeXProcessor.shared.cleanLaTeX(self)
    }
}

// A simpler SwiftUI wrapper around LaTeXView
struct MathView: View {
    let latex: String
    let isBlockDisplay: Bool
    
    @Environment(\.colorScheme) private var colorScheme
    @State private var height: CGFloat = 40
    @Environment(\.isEnabled) private var isEnabled
    
    init(latex: String, isBlockDisplay: Bool = false) {
        self.latex = latex
        self.isBlockDisplay = isBlockDisplay
    }
    
    var body: some View {
        LaTeXView(
            latexString: latex,
            textColor: UIColor(colorScheme == .dark ? .white : .black),
            backgroundColor: UIColor(colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1)),
            padding: 8,
            dynamicHeight: $height,
            offlineMode: !isEnabled // Use offline mode when the view is disabled
        )
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
        )
        .cornerRadius(4)
    }
}

// MARK: - Optimized Math View

// Optimized Math View using fast KaTeX rendering
struct OptimizedMathView: View {
    let latex: String
    let type: LaTeXType
    let fontSize: CGFloat
    @State private var height: CGFloat = 40
    @Environment(\.colorScheme) private var colorScheme
    
    init(latex: String, type: LaTeXType, fontSize: CGFloat = 16) {
        self.latex = latex
        self.type = type
        self.fontSize = fontSize
    }
    
    var body: some View {
        let cleanedLatex = LaTeXProcessor.shared.cleanLaTeX(latex)
        let isBlockDisplay = type.isDisplayMode
        
        // Use the fast KaTeX renderer instead of slow MathJax
        FastKaTeXWebView(
            expression: isBlockDisplay ? "$$\(cleanedLatex)$$" : "$\(cleanedLatex)$",
            textColor: colorScheme == .dark ? Color.white : Color.black,
            height: $height,
            fontSize: fontSize
        )
        .frame(height: height)
        .frame(maxWidth: .infinity, alignment: isBlockDisplay ? .center : .leading)
        .background(
            RoundedRectangle(cornerRadius: isBlockDisplay ? 8 : 4)
                .fill(colorScheme == .dark ? Color.black.opacity(0.2) : Color.gray.opacity(0.05))
                .opacity(isBlockDisplay ? 1 : 0)
        )
    }
}

// Fast KaTeX WebView with optimizations for speed
struct FastKaTeXWebView: UIViewRepresentable {
    let expression: String
    let textColor: Color
    @Binding var height: CGFloat
    let fontSize: CGFloat
    
    init(expression: String, textColor: Color, height: Binding<CGFloat>, fontSize: CGFloat = 16) {
        self.expression = expression
        self.textColor = textColor
        self._height = height
        self.fontSize = fontSize
    }
    
    func makeUIView(context: Context) -> WKWebView {
        let config = WKWebViewConfiguration()
        config.userContentController.add(context.coordinator, name: "heightUpdate")
        
        let webView = WKWebView(frame: .zero, configuration: config)
        webView.navigationDelegate = context.coordinator
        webView.isOpaque = false
        webView.backgroundColor = UIColor.clear
        webView.scrollView.isScrollEnabled = false
        webView.scrollView.bounces = false
        
        return webView
    }
    
    func updateUIView(_ webView: WKWebView, context: Context) {
        let cleanedExpression = cleanLatexExpression(expression)
        let textColorHex = hexString(from: textColor)
        
        // Properly escape the LaTeX for JavaScript
        let escapedExpression = cleanedExpression
            .replacingOccurrences(of: "\\", with: "\\\\")
            .replacingOccurrences(of: "'", with: "\\'")
            .replacingOccurrences(of: "\"", with: "\\\"")
            .replacingOccurrences(of: "\n", with: "\\n")
            .replacingOccurrences(of: "\r", with: "\\r")
        
        // Optimized HTML for faster KaTeX loading with proper font sizing
        let htmlContent = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
            <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
                         <style>
                 body {
                     margin: 0;
                     padding: 4px;
                     font-family: -apple-system, BlinkMacSystemFont, sans-serif;
                     background-color: transparent;
                     color: \(textColorHex);
                     line-height: 1.2;
                    font-size: \(fontSize)px;
                 }
                 * {
                     color: \(textColorHex) !important;
                 }
                .math {
                    display: inline-block;
                    font-size: \(fontSize)px;
                }
                                                  .katex {
                     color: \(textColorHex) !important;
                    font-size: \(fontSize)px !important;
                 }
                 .katex * {
                     color: \(textColorHex) !important;
                 }
                 .katex .mord, .katex .mop, .katex .mbin, .katex .mrel, .katex .mopen, .katex .mclose, .katex .mpunct {
                     color: \(textColorHex) !important;
                 }
                 .katex .mfrac, .katex .mfrac > *, .katex .mroot, .katex .mroot > * {
                     color: \(textColorHex) !important;
                 }
                 .katex .msqrt, .katex .msqrt > *, .katex .msubsup, .katex .msubsup > * {
                     color: \(textColorHex) !important;
                 }
                 .katex .base, .katex .strut, .katex .mathdefault, .katex .mathit, .katex .mathrm {
                     color: \(textColorHex) !important;
                 }
                 .katex span, .katex .vlist, .katex .vlist-t, .katex .vlist-r {
                     color: \(textColorHex) !important;
                 }
                 .katex .frac-line, .katex .sqrt-line, .katex .overline-line, .katex .underline-line {
                     border-color: \(textColorHex) !important;
                     background-color: \(textColorHex) !important;
                 }
                 .katex-display {
                     margin: 0.3em 0;
                 }
                 .fallback {
                     font-family: serif;
                     font-style: italic;
                     color: \(textColorHex);
                    font-size: \(fontSize)px;
                 }
            </style>
        </head>
        <body>
            <div id="math-container" class="math"></div>
            
            <script>
                function renderMath() {
                    try {
                        const container = document.getElementById('math-container');
                        const expression = '\(escapedExpression)';
                        
                        console.log('Rendering expression:', expression);
                        
                        // Determine if it's display mode (block math)
                        const isDisplayMode = expression.startsWith('$$') && expression.endsWith('$$');
                        
                        let mathExpression = expression;
                        if (isDisplayMode) {
                            mathExpression = mathExpression.slice(2, -2);
                        } else if (mathExpression.startsWith('$') && mathExpression.endsWith('$')) {
                            mathExpression = mathExpression.slice(1, -1);
                        }
                        
                        console.log('Cleaned expression:', mathExpression);
                        
                        katex.render(mathExpression, container, {
                            throwOnError: false,
                            displayMode: isDisplayMode,
                            output: 'html',
                            trust: false,
                            strict: false,
                            macros: {
                                '\\times': '×',
                                '\\cdot': '·',
                                '\\div': '÷',
                                '\\circ': '°'
                            }
                        });
                        
                        // Quick height update
                        setTimeout(() => {
                            const height = Math.max(document.body.scrollHeight, Math.max(\(fontSize) * 1.5, 25));
                            window.webkit.messageHandlers.heightUpdate.postMessage(height);
                        }, 50);
                        
                    } catch (error) {
                        console.log('KaTeX error:', error);
                        const container = document.getElementById('math-container');
                        container.innerHTML = '<span class="fallback">' + '\(escapedExpression)' + '</span>';
                        
                        setTimeout(() => {
                            const height = Math.max(document.body.scrollHeight, Math.max(\(fontSize) * 1.5, 25));
                            window.webkit.messageHandlers.heightUpdate.postMessage(height);
                        }, 25);
                    }
                }
                
                // Faster KaTeX loading check
                if (typeof katex !== 'undefined') {
                    renderMath();
                } else {
                    document.addEventListener('DOMContentLoaded', function() {
                        const checkKaTeX = setInterval(() => {
                            if (typeof katex !== 'undefined') {
                                clearInterval(checkKaTeX);
                                renderMath();
                            }
                        }, 50);
                        
                        // Fallback after 2 seconds
                        setTimeout(() => {
                            clearInterval(checkKaTeX);
                            if (typeof katex === 'undefined') {
                                document.getElementById('math-container').innerHTML = 
                                    '<span class="fallback">' + '\(escapedExpression)' + '</span>';
                                const height = Math.max(\(fontSize) * 1.5, 25);
                                window.webkit.messageHandlers.heightUpdate.postMessage(height);
                            }
                        }, 2000);
                    });
                }
            </script>
        </body>
        </html>
        """
        
        webView.loadHTMLString(htmlContent, baseURL: nil)
    }
    
    private func cleanLatexExpression(_ expression: String) -> String {
        var cleaned = expression.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove common malformed patterns first
        cleaned = cleaned.replacingOccurrences(of: "$}$", with: "")
        cleaned = cleaned.replacingOccurrences(of: "$$}$$", with: "")
        cleaned = cleaned.replacingOccurrences(of: "$$}}", with: "")
        cleaned = cleaned.replacingOccurrences(of: "$}}", with: "")
        
        // Fix multiple consecutive dollar signs aggressively
        cleaned = cleaned.replacingOccurrences(of: "$$$$", with: "$$")
        cleaned = cleaned.replacingOccurrences(of: "$$$", with: "$")
        
        // Remove any remaining redundant dollar signs within the expression
        let redundantDollarPattern = #"\$+([^$]*?)\$+"#
        do {
            let regex = try NSRegularExpression(pattern: redundantDollarPattern, options: [])
            let range = NSRange(cleaned.startIndex..<cleaned.endIndex, in: cleaned)
            let matches = regex.matches(in: cleaned, options: [], range: range)
            
            // Work backwards to avoid index shifting
            for match in matches.reversed() {
                if let matchRange = Range(match.range, in: cleaned),
                   let captureRange = Range(match.range(at: 1), in: cleaned) {
                    let capturedContent = String(cleaned[captureRange])
                    // Only replace if the captured content doesn't look like it should have dollars
                    if !capturedContent.contains("\\") && !capturedContent.contains("{") {
                        cleaned.replaceSubrange(matchRange, with: capturedContent)
                    }
                }
            }
        } catch {
            print("Error processing redundant dollars: \(error)")
        }
        
        // Fix common LaTeX command issues
        cleaned = cleaned.replacingOccurrences(of: "\\\\frac", with: "\\frac")
        cleaned = cleaned.replacingOccurrences(of: "\\\\sqrt", with: "\\sqrt")
        cleaned = cleaned.replacingOccurrences(of: "\\\\text", with: "\\text")
        
        return cleaned
    }
    
    private func hexString(from color: Color) -> String {
        // Force proper black/white colors to avoid any red coloring
        if color == Color.black {
            return "#000000"
        } else if color == Color.white {
            return "#FFFFFF"
        }
        
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        
        let hexString = String(
            format: "#%02X%02X%02X",
            Int(red * 255),
            Int(green * 255),
            Int(blue * 255)
        )
        
        // Debug: Print the color being used
        print("KaTeX color: \(hexString) for color: \(color)")
        
        return hexString
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, WKNavigationDelegate, WKScriptMessageHandler {
        var parent: FastKaTeXWebView
        
        init(_ parent: FastKaTeXWebView) {
            self.parent = parent
        }
        
        func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
            // Navigation completed
        }
        
        func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
            print("FastKaTeXWebView navigation failed: \(error)")
        }
        
        func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
            if message.name == "heightUpdate", let height = message.body as? CGFloat {
                DispatchQueue.main.async {
                    self.parent.height = max(height, 25)
                }
            }
        }
    }
} 