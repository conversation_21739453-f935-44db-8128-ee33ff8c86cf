//
//  CodeBlockView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI
import UIKit

struct CodeBlockView: View {
    let code: String
    let language: String?
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            VStack(alignment: .leading, spacing: 2) {
                if let language = language {
                    HStack {
                        Text(language.uppercased())
                            .font(.system(size: 10, weight: .semibold, design: .monospaced))
                            .foregroundColor(colorScheme == .dark ? .gray : .secondary)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                        
                        Spacer()
                        
                        Button(action: {
                            UIPasteboard.general.string = code
                        }) {
                            Image(systemName: "doc.on.doc")
                                .font(.system(size: 12))
                                .foregroundColor(colorScheme == .dark ? .gray : .secondary)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .padding(.trailing, 8)
                    }
                    .background(colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
                }
                
                Text(code)
                    .font(.system(.body, design: .monospaced))
                    .foregroundColor(getSyntaxColor(for: code, language: language))
                    .padding(10)
                    .textSelection(.enabled)
                    .multilineTextAlignment(.leading)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }
            .background(colorScheme == .dark ? Color.black.opacity(0.3) : Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
        .frame(maxWidth: .infinity)
    }
    
    // Simple syntax color highlighting based on language
    private func getSyntaxColor(for code: String, language: String?) -> Color {
        if colorScheme == .dark {
            // Dark mode colors
            return language == nil ? .white : .green
        } else {
            // Light mode colors
            return language == nil ? .black : .blue
        }
    }
}

// Mathematical expression view for rendering equations
struct MathExpressionView: View {
    let expression: String
    let originalText: String // The original text with delimiters
    @Environment(\.colorScheme) private var colorScheme
    @State private var showEditor = false
    
    init(expression: String, originalText: String? = nil) {
        self.expression = expression
        self.originalText = originalText ?? expression
    }
    
    private var isBlockDisplay: Bool {
        return originalText.contains("$$") || 
               (originalText.hasPrefix("\\[") && originalText.hasSuffix("\\]"))
    }
    
    private var displayLatex: String {
        // If it's already a block display format, keep the delimiters
        if isBlockDisplay {
            // For block display, use the appropriate delimiters
            if originalText.contains("$$") {
                return expression // Already in the right format
            } else {
                // Convert \[...\] format to $$...$$ for MathJax
                return expression
            }
        } else {
            // For inline display, we need to add delimiters if they're missing
            return expression
        }
    }
    
    var body: some View {
        // Clean the expression by removing any delimiters
        let cleanExpression = cleanMathExpression(expression)
        
        VStack(alignment: .leading, spacing: 0) {
            if showEditor {
                MathExpressionEditor(expression: expression, isBlock: isBlockDisplay)
            }
            
            // Use KaTeX renderer for proper formula display
            FastKaTeXWebView(
                expression: isBlockDisplay ? "$$\(cleanExpression)$$" : "$\(cleanExpression)$",
                textColor: colorScheme == .dark ? Color.white : Color.black,
                height: .constant(isBlockDisplay ? 60 : 40),
                fontSize: 16
            )
            .padding(.vertical, isBlockDisplay ? 8 : 4)
            .frame(maxWidth: .infinity, alignment: .leading)
            .onTapGesture {
                withAnimation {
                    showEditor.toggle()
                }
            }
        }
    }
    
    // Helper method to clean math expressions
    private func cleanMathExpression(_ expression: String) -> String {
        var cleaned = expression.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Remove delimiters
        if cleaned.hasPrefix("$$") && cleaned.hasSuffix("$$") && cleaned.count > 4 {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        } else if cleaned.hasPrefix("$") && cleaned.hasSuffix("$") && cleaned.count > 2 {
            cleaned = String(cleaned.dropFirst().dropLast())
        } else if cleaned.hasPrefix("\\[") && cleaned.hasSuffix("\\]") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        } else if cleaned.hasPrefix("\\(") && cleaned.hasSuffix("\\)") {
            cleaned = String(cleaned.dropFirst(2).dropLast(2))
        }
        
        return cleaned.trimmingCharacters(in: .whitespacesAndNewlines)
    }
}

struct CodeBlockView_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            CodeBlockView(code: "func example() {\n    print(\"Hello, world!\")\n}", language: "swift")
            
            CodeBlockView(code: "x^2 + y^2 = r^2", language: "math")
        }
        .padding()
    }
} 