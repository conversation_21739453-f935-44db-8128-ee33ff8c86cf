//
//  ARContainerView.swift
//  Luminous Education
//
//  Created by <PERSON> on 21/2/2025.
//

import SwiftUI

struct ARContainerView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        ZStack {
            VStack {
                HStack{
                    Text("Augmented Reality")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.leading)
                    Spacer()
                    
                    // Close button
                    Button(action: {
                        dismiss()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white)
                            .background(Color.black.opacity(0.6))
                            .clipShape(Circle())
                    }
                    .padding(.trailing)
                }
                .padding(.top, 50)
                
                ARView()
                    .edgesIgnoringSafeArea(.all) // Make the AR view full screen

                Spacer()
            }
        }
    }
}

struct ARContainerView_Previews: PreviewProvider {
    static var previews: some View {
        ARContainerView()
    }
}
