import SwiftUI
import ARKit
import SceneKit


// MARK: - ARSphereView

struct ARSphereView: View {
    @State private var cutPosition: Float = 0.0
    @State private var isCutMode: Bool = false
    @State private var selectedAxis: Int? // 0: X, 1: Y, 2: Z
    @State private var scaleText: String = "Scale: 100%"
    
    var body: some View {
        ZStack {
            ARSphereSceneView(cutPosition: $cutPosition,
                              isCutMode: $isCutMode,
                              selectedAxis: $selectedAxis)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                Spacer()
                HStack {
                    Button(action: {
                        print("Cross Section button pressed")
                        if let coordinator = ARSphereSceneView.coordinator {
                            print("Found coordinator, selected lines: \(coordinator.selectedLines.count)")
                            if coordinator.selectedLines.count > 0 {
                                coordinator.applyCrossSection()
                            }
                        } else {
                            print("No coordinator found")
                        }
                    }) {
                        Text("Cross Section")
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .disabled(ARSphereSceneView.coordinator?.selectedLines.count == 0)
                }
                .padding()
            }
        }
    }
}

// MARK: - ARSphereSceneView

struct ARSphereSceneView: UIViewRepresentable {
    @Binding var cutPosition: Float
    @Binding var isCutMode: Bool
    @Binding var selectedAxis: Int?
    var arView = ARSCNView()
    
    // Constants for the dotted lines
    static let numberOfLines = 9 // 3 lines per axis
    static let lineSegments = 10 // Number of dots per line
    
    static var coordinator: Coordinator?
    
    static func createDottedGridPlane(size: CGFloat) -> SCNNode {
        let containerNode = SCNNode()
        
        // Create a transparent backing plane
        let plane = SCNPlane(width: size, height: size)
        plane.firstMaterial?.diffuse.contents = UIColor.clear
        plane.firstMaterial?.transparency = 0.01
        
        let planeNode = SCNNode(geometry: plane)
        containerNode.addChildNode(planeNode)
        
        // Constants for dotted lines
        let dotRadius: CGFloat = 0.002
        let spacing = size / 3
        let dotsPerLine = 15
        
        // Create lines container
        let linesContainer = SCNNode()
        containerNode.addChildNode(linesContainer)
        
        // Function to create a dotted line
        func createDottedLine(isHorizontal: Bool, position: Float) -> SCNNode {
            let lineNode = SCNNode()
            lineNode.name = isHorizontal ? "horizontalLine" : "verticalLine"
            let halfSize = Float(size / 2)
            
            for i in 0..<dotsPerLine {
                let t = Float(i) / Float(dotsPerLine - 1)
                let coord = -halfSize + (2 * halfSize * t)
                
                let dot = SCNSphere(radius: dotRadius)
                dot.firstMaterial?.diffuse.contents = isHorizontal ? UIColor.purple : UIColor.blue
                dot.firstMaterial?.emission.contents = isHorizontal ? UIColor.purple : UIColor.blue
                dot.firstMaterial?.lightingModel = .constant
                
                let dotNode = SCNNode(geometry: dot)
                if isHorizontal {
                    dotNode.position = SCNVector3(coord, position, 0)
                } else {
                    dotNode.position = SCNVector3(position, coord, 0)
                }
                // Set constraint to maintain look direction
                let constraint = SCNBillboardConstraint()
                constraint.freeAxes = [.X, .Y]
                dotNode.constraints = [constraint]
                
                lineNode.addChildNode(dotNode)
            }
            
            return lineNode
        }
        
        for i in -1...1 {
            // Add vertical dotted lines
            let x = Float(spacing) * Float(i)
            let lineX = createDottedLine(isHorizontal: false, position: x)
            linesContainer.addChildNode(lineX)
        
            // Add horizontal dotted lines
            let y = Float(spacing) * Float(i)
            let lineY = createDottedLine(isHorizontal: true, position: y)
            linesContainer.addChildNode(lineY)
        }
        
        return containerNode
    }
    
    func makeCoordinator() -> Coordinator {
        let coordinator = Coordinator(self)
        ARSphereSceneView.coordinator = coordinator
        return coordinator
    }
    
    func makeUIView(context: Context) -> ARSCNView {
        arView.delegate = context.coordinator
        arView.showsStatistics = true
        arView.autoenablesDefaultLighting = true
        
        // Configure AR session
        let configuration = ARWorldTrackingConfiguration()
        configuration.planeDetection = [.horizontal]
        
        // Enable better world tracking and lighting
        configuration.environmentTexturing = .automatic
        if ARWorldTrackingConfiguration.supportsFrameSemantics(.sceneDepth) {
            configuration.frameSemantics.insert(.sceneDepth)
        }
        
        arView.session.run(configuration)
        
        // Add instruction label
        context.coordinator.setupInstructionLabel(in: arView)
        
        // Add gesture recognizers
        let tapGesture = UITapGestureRecognizer(target: context.coordinator,
                                                action: #selector(Coordinator.handleTap(_:)))
        arView.addGestureRecognizer(tapGesture)
        
        let panGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                action: #selector(Coordinator.handlePan(_:)))
        arView.addGestureRecognizer(panGesture)
        
        let pinchGesture = UIPinchGestureRecognizer(target: context.coordinator,
                                                    action: #selector(Coordinator.handlePinch(_:)))
        arView.addGestureRecognizer(pinchGesture)
        
        return arView
    }
    
    func updateUIView(_ uiView: ARSCNView, context: Context) {
        // No automatic cut plane updates.
    }
    
    // MARK: - Coordinator
    
    class Coordinator: NSObject, ARSCNViewDelegate {
        var parent: ARSphereSceneView
        var currentSphereNode: SCNNode?
        var isObjectCreated = false
        var lineNodes: [SCNNode] = []
        var selectedLines: [SCNNode] = []
        var gridNode: SCNNode?
        var instructionLabel: UILabel!
        
        init(_ parent: ARSphereSceneView) {
            self.parent = parent
            super.init()
        }
        
        func setupInstructionLabel(in view: ARSCNView) {
            instructionLabel = UILabel()
            instructionLabel.translatesAutoresizingMaskIntoConstraints = false
            instructionLabel.textColor = .white
            instructionLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
            instructionLabel.layer.cornerRadius = 10
            instructionLabel.clipsToBounds = true
            instructionLabel.textAlignment = .center
            instructionLabel.font = UIFont.systemFont(ofSize: 16)
            instructionLabel.text = "Move your device around to detect surfaces.\nTap on any surface to place the sphere."
            instructionLabel.numberOfLines = 0
            
            view.addSubview(instructionLabel)
            
            NSLayoutConstraint.activate([
                instructionLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                instructionLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
                instructionLabel.widthAnchor.constraint(equalToConstant: 350),
                instructionLabel.heightAnchor.constraint(equalToConstant: 50)
            ])
        }
        
        @objc func handleTap(_ gestureRecognize: UIGestureRecognizer) {
            guard let arView = gestureRecognize.view as? ARSCNView else { return }
            let touchLocation = gestureRecognize.location(in: arView)
            
            if !isObjectCreated {
                if let query = arView.raycastQuery(from: touchLocation,
                                                   allowing: .existingPlaneGeometry,
                                                   alignment: .any),
                   let raycastResult = arView.session.raycast(query).first {
                    
                    let sphereRadius: CGFloat = 0.05
                    let sphere = SCNSphere(radius: sphereRadius)
                    
                    // Create a material with properties for cross-sectioning.
                    let material = SCNMaterial()
                    material.diffuse.contents = UIColor.red
                    material.specular.contents = UIColor.white
                    material.locksAmbientWithDiffuse = true
                    material.lightingModel = .physicallyBased
                    material.transparency = 1.0
                    sphere.firstMaterial = material
                    
                    // Create a container node for the sphere and grid.
                    let containerNode = SCNNode()
                    
                    let sphereNode = SCNNode(geometry: sphere)
                    containerNode.addChildNode(sphereNode)
                    
                    // Create and add the dotted grid plane.
                    let gridSize = CGFloat(sphereRadius * 2.2)
                    gridNode = ARSphereSceneView.createDottedGridPlane(size: gridSize)
                    
                    if let gridNode = gridNode {
                        // Store line nodes for selection.
                        lineNodes = gridNode.childNodes.last?.childNodes ?? []
                        
                        // Position grid in front and higher than the sphere.
                        gridNode.position = SCNVector3(
                            0,                              // x: centered
                            Float(sphereRadius) * 0.2,      // y: higher
                            Float(sphereRadius) * 1.0       // z: in front
                        )
                        containerNode.addChildNode(gridNode)
                    }
                    
                    // Get the world position and orientation for the object.
                    let worldTransform = raycastResult.worldTransform
                    
                    // Create an anchor at this position with proper orientation.
                    let anchorTransform = worldTransform.columns.3
                    let anchor = ARAnchor(name: "sphereAnchor", transform: simd_float4x4(
                        simd_float4(1, 0, 0, 0),
                        simd_float4(0, 1, 0, 0),
                        simd_float4(0, 0, 1, 0),
                        simd_float4(anchorTransform.x, anchorTransform.y + Float(sphereRadius), anchorTransform.z, 1)
                    ))
                    arView.session.add(anchor: anchor)
                    
                    // Associate the container node with the anchor.
                    containerNode.name = anchor.identifier.uuidString
                    
                    // Set the initial position.
                    containerNode.position = SCNVector3(
                        worldTransform.columns.3.x,
                        worldTransform.columns.3.y + Float(sphereRadius),
                        worldTransform.columns.3.z
                    )
                    
                    arView.scene.rootNode.addChildNode(containerNode)
                    currentSphereNode = sphereNode
                    isObjectCreated = true
                    
                    DispatchQueue.main.async {
                        self.instructionLabel.isHidden = true
                    }
                }
            } else {
                let hitResults = arView.hitTest(touchLocation, options: [:])
                if let hit = hitResults.first {
                    // Find the parent line node.
                    var currentNode = hit.node
                    while let parentNode = currentNode.parent {
                        if let name = parentNode.name,
                           (name == "horizontalLine" || name == "verticalLine") {
                            handleLineSelection(parentNode)
                            break
                        }
                        currentNode = parentNode
                    }
                }
            }
        }
        
        func handleLineSelection(_ selectedLine: SCNNode) {
            let isHorizontal = selectedLine.name == "horizontalLine"
            
            // If the line is already selected, deselect it.
            if selectedLines.contains(selectedLine) {
                selectedLines.removeAll { $0 == selectedLine }
                setLineColor(selectedLine, color: isHorizontal ? UIColor.purple : UIColor.blue)
            } else {
                // If more than two lines are selected, remove the oldest.
                if selectedLines.count >= 2 {
                    let oldestLine = selectedLines.removeFirst()
                    let oldestIsHorizontal = oldestLine.name == "horizontalLine"
                    setLineColor(oldestLine, color: oldestIsHorizontal ? UIColor.purple : UIColor.blue)
                }
                selectedLines.append(selectedLine)
                setLineColor(selectedLine, color: UIColor.yellow)
            }
            
            // Clear any existing cutting planes when selection changes.
            if let sphereNode = currentSphereNode {
                sphereNode.parent?.childNodes.filter { $0 != sphereNode && $0 != gridNode }.forEach { $0.removeFromParentNode() }
                sphereNode.geometry?.firstMaterial?.shaderModifiers = [:]
            }
            
            // Update the parent view with the selection state.
            if let lastSelected = selectedLines.last,
               let index = lineNodes.firstIndex(of: lastSelected) {
                parent.selectedAxis = index
            }
        }
        
        private func setLineColor(_ line: SCNNode, color: UIColor) {
            line.childNodes.forEach { dotNode in
                if let dot = dotNode.geometry as? SCNSphere {
                    dot.firstMaterial?.diffuse.contents = color
                    dot.firstMaterial?.emission.contents = color
                }
            }
        }
        
        @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
            guard let arView = gesture.view as? ARSCNView,
                  parent.isCutMode,
                  parent.selectedAxis != nil else { return }
            
            let translation = gesture.translation(in: arView)
            let delta = Float(translation.y) * 0.005 // Adjust sensitivity as needed.
            parent.cutPosition = max(-0.5, min(0.5, parent.cutPosition + delta))
            gesture.setTranslation(.zero, in: arView)
        }
        
        @objc func handlePinch(_ gesture: UIPinchGestureRecognizer) {
            guard let containerNode = currentSphereNode?.parent else { return }
            
            if gesture.state == .changed {
                let pinchScale = Float(gesture.scale)
                containerNode.scale = SCNVector3(
                    containerNode.scale.x * pinchScale,
                    containerNode.scale.y * pinchScale,
                    containerNode.scale.z * pinchScale
                )
                gesture.scale = 1.0
            }
        }
        
        // MARK: - Cross Section
        
        // This method now uses the sphere's presentation transform (for an up-to-date world position)
        // and is called on every update so that the cut boundaries remain correct even after yaw rotation.
        func applyCrossSection() {
            print("Applying cross section")
            guard !selectedLines.isEmpty,
                  let sphereNode = currentSphereNode,
                  let sphere = sphereNode.geometry as? SCNSphere else {
                print("Failed to apply cross section: selectedLines count=\(selectedLines.count), sphereNode=\(String(describing: currentSphereNode))")
                return
            }
            
            // Determine if we use a horizontal or vertical cut.
            let isHorizontal = selectedLines[0].name == "horizontalLine"
            // Choose a fixed axis in world space.
            let fixedAxis: SCNVector3 = isHorizontal ? SCNVector3(0, 1, 0) : SCNVector3(1, 0, 0)
            // Use the sphere's presentation world position to get an up-to-date center.
            let sphereCenter = sphereNode.presentation.worldPosition
            
            // Compute the projection (scalar) of each selected line's first dot along the fixed axis.
            var projections: [Float] = []
            for line in selectedLines {
                if let firstDot = line.childNodes.first {
                    let diff = firstDot.worldPosition - sphereCenter
                    // Dot product with the fixed axis.
                    let proj = diff.x * fixedAxis.x + diff.y * fixedAxis.y + diff.z * fixedAxis.z
                    projections.append(proj)
                }
            }
            projections.sort()
            guard let minVal = projections.first, let maxVal = projections.last else {
                print("No valid positions found from selected lines")
                return
            }
            print("minVal:", minVal, ", maxVal:", maxVal)
            // Convert the fixed axis into the sphere's local coordinate system.
            var cutAxisInSphere = sphereNode.convertVector(fixedAxis, from: nil)
            
            let axisLength = sqrt(cutAxisInSphere.x * cutAxisInSphere.x +
                                 cutAxisInSphere.y * cutAxisInSphere.y +
                                 cutAxisInSphere.z * cutAxisInSphere.z)
            if axisLength != 0 {
               cutAxisInSphere.x /= axisLength
               cutAxisInSphere.y /= axisLength
               cutAxisInSphere.z /= axisLength
            }

            print("Cut axis in sphere:", cutAxisInSphere, "min:", minVal, "max:", maxVal)
            
            // Build shader code using the fixed cut axis.
            let shaderCode = """
            #pragma arguments
            float u_min;
            float u_max;
            float3 u_cutAxis;
            #pragma body
            {
                float d = dot(_surface.position, u_cutAxis);
                if (d < u_min || d > u_max) {
                    discard_fragment();
                }
            }
            """
//            cutAxisInSphere = SCNVector3(x: 1.0, y: 1.0, z: 0.0)
            sphereNode.geometry?.firstMaterial?.shaderModifiers = [.surface: shaderCode]
            sphereNode.geometry?.firstMaterial?.setValue(minVal, forKey: "u_min")
            sphereNode.geometry?.firstMaterial?.setValue(maxVal, forKey: "u_max")
            sphereNode.geometry?.firstMaterial?.setValue(NSValue(scnVector3: cutAxisInSphere), forKey: "u_cutAxis")
            
            // Optionally update visual cutting planes for debugging.
            addVisualPlanesForCrossSection(sphere: sphere,
                                           sphereNode: sphereNode,
                                           horizontalPositions: isHorizontal ? projections : [],
                                           verticalPositions: isHorizontal ? [] : projections)
            
            // Apply the same transformation to the sphere as the visual planes
//            sphereNode.position = SCNVector3(0, (minVal + maxVal) / 2, 0) // Center the sphere based on the cut
//            sphereNode.scale = SCNVector3(1, 1, 1) // Reset scale if needed
//            sphereNode.eulerAngles.y = .pi/2
            print("Cross section applied.")
        }
        
        /// Helper method to add visual cutting planes for debugging.
        private func addVisualPlanesForCrossSection(sphere: SCNSphere, sphereNode: SCNNode,
                                                      horizontalPositions: [Float],
                                                      verticalPositions: [Float]) {
            let planeSize = CGFloat(sphere.radius * 2.2)
            
            // Add horizontal cutting planes.
            for position in horizontalPositions {
                let plane = SCNPlane(width: planeSize, height: planeSize)
                let material = SCNMaterial()
                material.diffuse.contents = UIColor(red: 0.9, green: 0.6, blue: 0.6, alpha: 0.8)
                material.transparency = 0.7
                material.isDoubleSided = true
                plane.materials = [material]
                
                let planeNode = SCNNode(geometry: plane)
                planeNode.position = SCNVector3(0, position, 0)
                planeNode.eulerAngles.x = .pi / 2
                
                material.readsFromDepthBuffer = true
                material.writesToDepthBuffer = false
                
                sphereNode.parent?.addChildNode(planeNode)
            }
            
            // Add vertical cutting planes.
            for position in verticalPositions {
                let plane = SCNPlane(width: planeSize, height: planeSize)
                let material = SCNMaterial()
                material.diffuse.contents = UIColor(red: 0.6, green: 0.6, blue: 0.9, alpha: 0.8)
                material.transparency = 0.7
                material.isDoubleSided = true
                plane.materials = [material]
                
                let planeNode = SCNNode(geometry: plane)
                planeNode.position = SCNVector3(position, 0, 0)
                planeNode.eulerAngles.y = .pi / 2
                
                material.readsFromDepthBuffer = true
                material.writesToDepthBuffer = false
                
                sphereNode.parent?.addChildNode(planeNode)
            }
        }
        
        func clearCrossSection() {
            guard let sphereNode = currentSphereNode else { return }
            sphereNode.geometry?.firstMaterial?.shaderModifiers = [:]
            sphereNode.parent?.childNodes.filter { $0 != sphereNode && $0 != gridNode }.forEach { $0.removeFromParentNode() }
        }
        
        // MARK: - ARSCNViewDelegate
        
        //renderer to show detected surface
        func renderer(_ renderer: SCNSceneRenderer, didAdd node: SCNNode, for anchor: ARAnchor) {
            if let planeAnchor = anchor as? ARPlaneAnchor {
                DispatchQueue.main.async { [weak self] in
                    self?.instructionLabel.isHidden = true
                }
                let width = CGFloat(planeAnchor.planeExtent.width)
                let height = CGFloat(planeAnchor.planeExtent.height)
                let plane = SCNPlane(width: width, height: height)
                plane.materials.first?.diffuse.contents = UIColor(white: 1, alpha: 0.7)
                
                let planeNode = SCNNode(geometry: plane)
                planeNode.position = SCNVector3(planeAnchor.center.x, 0, planeAnchor.center.z)
                planeNode.eulerAngles.x = -.pi / 2
                
                node.addChildNode(planeNode)
            }
            else if let containerNode = parent.arView.scene.rootNode.childNode(withName: anchor.identifier.uuidString, recursively: true) {
                containerNode.simdTransform = anchor.transform
            }
        }
//        
//        func renderer(_ renderer: SCNSceneRenderer, didUpdate node: SCNNode, for anchor: ARAnchor) {
//            if let planeAnchor = anchor as? ARPlaneAnchor,
//               let planeNode = node.childNodes.first,
//               let plane = planeNode.geometry as? SCNPlane {
//                plane.width = CGFloat(planeAnchor.planeExtent.width)
//                plane.height = CGFloat(planeAnchor.planeExtent.height)
//                planeNode.position = SCNVector3(planeAnchor.center.x, 0, planeAnchor.center.z)
//            }
//            // For our sphere anchor, update the container node and reapply the cross section.
//            else if anchor.name == "sphereAnchor",
//                    let containerNode = parent.arView.scene.rootNode.childNode(withName: anchor.identifier.uuidString, recursively: true) {
//                let currentScale = containerNode.scale
//                containerNode.simdTransform = anchor.transform
//                containerNode.scale = currentScale
//                // Force an update every time.
//                DispatchQueue.main.async {
//                    self.applyCrossSection()
//                }
//            }
//        }
//        
//        // Optional: You can remove willUpdate to avoid duplicate calls.
//        func renderer(_ renderer: SCNSceneRenderer, willUpdate node: SCNNode, for anchor: ARAnchor) {
//            // No-op (or you may choose to call applyCrossSection() here as well).
//        }
    }
}

struct ARSphereView_Previews: PreviewProvider {
    static var previews: some View {
        ARSphereView()
    }
}
