//
//  ARView.swift
//  Luminous Education
//
//  Created by <PERSON> on 21/2/2025.
//  Updated on 2025-02-25
//

import SwiftUI
import ARKit

struct ARView: UIViewRepresentable {
    @State private var scaleText: String = "Scale: 100%"
    @State private var isSectionViewEnabled = false
    var arView = ARSCNView() // Create an instance of ARSCNView

    func makeUIView(context: Context) -> ARSCNView {
        arView.delegate = context.coordinator
        arView.showsStatistics = true // Show statistics like FPS and timing
        arView.autoenablesDefaultLighting = true // Enable default lighting

        // Configure the AR session to only detect horizontal planes.
        let configuration = ARWorldTrackingConfiguration()
        configuration.planeDetection = [.horizontal]
        arView.session.run(configuration)

        // Set up gesture recognizers.
        let tapGesture = UITapGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handleTap(_:)))
        arView.addGestureRecognizer(tapGesture)

        let panGesture = UIPanGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handlePan(_:)))
        arView.addGestureRecognizer(panGesture)

        let pinchGesture = UIPinchGestureRecognizer(target: context.coordinator, action: #selector(Coordinator.handlePinch(_:)))
        arView.addGestureRecognizer(pinchGesture)

        // Add UI overlays.
        context.coordinator.setupInstructionLabel(in: arView)
        context.coordinator.setupScaleLabel(in: arView)
        context.coordinator.setupSectionViewControls(in: arView)

        return arView
    }

    func updateUIView(_ uiView: ARSCNView, context: Context) {
        // Update the view if needed.
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    // Pause the AR session when the view disappears.
    func onDisappear() {
        arView.session.pause()
    }

    class Coordinator: NSObject, ARSCNViewDelegate {
        var parent: ARView
        var currentBoxNode: SCNNode? // The box representing the object.
        var scaleLabel: UILabel!       // Displays the scaling factor.
        var instructionLabel: UILabel! // Displays instructions.
        var isDragging = false         // For pan gesture tracking.
        var isObjectCreated = false    // Check if object exists.
        private var sectionPlane: SCNNode?    // Visualization node for the cut plane.
        private var sectionViewEnabled = false // Section view mode flag.
        private var sectionSlider: UISlider?    // Slider for optional adjustments.
        private var sectionToggle: UIButton?    // Toggle button for section view.

        init(_ parent: ARView) {
            self.parent = parent
            super.init()
        }

        // MARK: - Gesture Handlers

        @objc func handleTap(_ gestureRecognize: UIGestureRecognizer) {
            let arView = gestureRecognize.view as! ARSCNView
            let touchLocation = gestureRecognize.location(in: arView)
            
            // If the object exists and section view is enabled, perform a hit-test on its surface.
            if isObjectCreated, sectionViewEnabled, let node = currentBoxNode {
                let hitResults = arView.hitTest(touchLocation, options: nil)
                if let hit = hitResults.first(where: { $0.node == node }) {
                    updateSectionPlane(withHit: hit)
                    return
                }
            }
            
            // Otherwise, if the object isn't created, create it.
            if !isObjectCreated {
                if let query = arView.raycastQuery(from: touchLocation, allowing: .existingPlaneGeometry, alignment: .any),
                   let raycastResult = arView.session.raycast(query).first {
                    
                    let boxWidth: CGFloat = 0.1
                    let boxHeight: CGFloat = 0.1
                    let boxLength: CGFloat = 0.1
                    let box = SCNBox(width: boxWidth, height: boxHeight, length: boxLength, chamferRadius: 0)
                    let boxNode = SCNNode(geometry: box)
                    
                    // Set the pivot so that the bottom of the box is the node's origin.
                    boxNode.pivot = SCNMatrix4MakeTranslation(0, -Float(boxHeight) / 2, 0)
                    boxNode.position = SCNVector3(
                        raycastResult.worldTransform.columns.3.x,
                        raycastResult.worldTransform.columns.3.y,
                        raycastResult.worldTransform.columns.3.z
                    )
                    boxNode.geometry?.firstMaterial?.diffuse.contents = UIColor.red
                    arView.scene.rootNode.addChildNode(boxNode)
                    
                    currentBoxNode = boxNode
                    isObjectCreated = true
                    updateScaleLabel()
                    
                    DispatchQueue.main.async {
                        self.instructionLabel.isHidden = true
                        print("Instruction label hidden after object creation.")
                    }
                } else {
                    print("Raycast did not hit any surface.")
                }
            }
        }

        @objc func handlePan(_ gestureRecognize: UIPanGestureRecognizer) {
            let arView = gestureRecognize.view as! ARSCNView
            let touchLocation = gestureRecognize.location(in: arView)
            
            switch gestureRecognize.state {
            case .began:
                let hitResults = arView.hitTest(touchLocation, options: [.boundingBoxOnly: true])
                if let hit = hitResults.first, hit.node == currentBoxNode {
                    isDragging = true
                }
            case .changed:
                guard isDragging, let boxNode = currentBoxNode else { return }
                if let query = arView.raycastQuery(from: touchLocation, allowing: .existingPlaneGeometry, alignment: .any),
                   let raycastResult = arView.session.raycast(query).first {
                    boxNode.position = SCNVector3(
                        raycastResult.worldTransform.columns.3.x,
                        raycastResult.worldTransform.columns.3.y,
                        raycastResult.worldTransform.columns.3.z
                    )
                }
            case .ended, .cancelled:
                isDragging = false
            default:
                break
            }
        }

        @objc func handlePinch(_ gestureRecognize: UIPinchGestureRecognizer) {
            guard let boxNode = currentBoxNode else { return }
            if gestureRecognize.state == .changed {
                let scale = gestureRecognize.scale
                boxNode.scale = SCNVector3(scale, scale, scale)
                updateScaleLabel()
            }
        }

        // MARK: - ARSCNViewDelegate
        
        func renderer(_ renderer: SCNSceneRenderer, didAdd node: SCNNode, for anchor: ARAnchor) {
            if let planeAnchor = anchor as? ARPlaneAnchor {
                DispatchQueue.main.async {
                    self.instructionLabel.isHidden = true
                }
                let width = CGFloat(planeAnchor.planeExtent.width)
                let height = CGFloat(planeAnchor.planeExtent.height)
                let plane = SCNPlane(width: width, height: height)
                plane.materials.first?.diffuse.contents = UIColor(white: 1, alpha: 0.7)
                let planeNode = SCNNode(geometry: plane)
                planeNode.position = SCNVector3(planeAnchor.center.x, 0, planeAnchor.center.z)
                planeNode.eulerAngles.x = -.pi / 2
                node.addChildNode(planeNode)
            }
        }
        
        

        // MARK: - UI Setup
        
        func setupInstructionLabel(in view: ARSCNView) {
            instructionLabel = UILabel()
            instructionLabel.translatesAutoresizingMaskIntoConstraints = false
            instructionLabel.textColor = .white
            instructionLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
            instructionLabel.layer.cornerRadius = 10
            instructionLabel.clipsToBounds = true
            instructionLabel.textAlignment = .center
            instructionLabel.font = UIFont.systemFont(ofSize: 16)
            instructionLabel.text = "Move your device around to detect surfaces.\nTap on any surface to add the object or cut through it."
            instructionLabel.numberOfLines = 0
            
            view.addSubview(instructionLabel)
            
            NSLayoutConstraint.activate([
                instructionLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                instructionLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
                instructionLabel.widthAnchor.constraint(equalToConstant: 350),
                instructionLabel.heightAnchor.constraint(equalToConstant: 50)
            ])
        }
        
        func setupScaleLabel(in view: ARSCNView) {
            scaleLabel = UILabel()
            scaleLabel.translatesAutoresizingMaskIntoConstraints = false
            scaleLabel.textColor = .white
            scaleLabel.backgroundColor = UIColor.black.withAlphaComponent(0.5)
            scaleLabel.layer.cornerRadius = 5
            scaleLabel.clipsToBounds = true
            scaleLabel.textAlignment = .center
            scaleLabel.font = UIFont.systemFont(ofSize: 16)
            scaleLabel.text = "Scale: 100%"
            
            view.addSubview(scaleLabel)
            
            NSLayoutConstraint.activate([
                scaleLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                scaleLabel.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -50),
                scaleLabel.widthAnchor.constraint(equalToConstant: 200),
                scaleLabel.heightAnchor.constraint(equalToConstant: 40)
            ])
        }
        
        func updateScaleLabel() {
            guard let boxNode = currentBoxNode else { return }
            let scaleFactor = boxNode.scale.x // Assuming uniform scaling.
            let percentage = Int(scaleFactor * 100)
            scaleLabel.text = "Scale: \(percentage)%"
        }
        
        func showScaleLabel(_ show: Bool) {
            scaleLabel.isHidden = !show
        }
        
        func setupSectionViewControls(in view: ARSCNView) {
            // Toggle button for section view.
            let toggleButton = UIButton(type: .system)
            toggleButton.translatesAutoresizingMaskIntoConstraints = false
            toggleButton.setTitle("Section View: Off", for: .normal)
            toggleButton.backgroundColor = UIColor.black.withAlphaComponent(0.7)
            toggleButton.layer.cornerRadius = 10
            toggleButton.addTarget(self, action: #selector(toggleSectionView), for: .touchUpInside)
            view.addSubview(toggleButton)
            sectionToggle = toggleButton

            // Slider for adjusting the section cut.
            let slider = UISlider()
            slider.translatesAutoresizingMaskIntoConstraints = false
            slider.minimumValue = -0.5
            slider.maximumValue = 0.5
            slider.value = 0
            slider.isEnabled = false
            slider.addTarget(self, action: #selector(updateSectionPlaneFromSlider(_:)), for: .valueChanged)
            view.addSubview(slider)
            sectionSlider = slider

            NSLayoutConstraint.activate([
                toggleButton.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 20),
                toggleButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
                toggleButton.widthAnchor.constraint(equalToConstant: 150),
                toggleButton.heightAnchor.constraint(equalToConstant: 40),

                slider.topAnchor.constraint(equalTo: toggleButton.bottomAnchor, constant: 10),
                slider.trailingAnchor.constraint(equalTo: toggleButton.trailingAnchor),
                slider.widthAnchor.constraint(equalToConstant: 150)
            ])
        }
        
        @objc func toggleSectionView() {
            sectionViewEnabled.toggle()
            sectionSlider?.isEnabled = sectionViewEnabled
            sectionToggle?.setTitle("Section View: \(sectionViewEnabled ? "On" : "Off")", for: .normal)
            
            if sectionViewEnabled {
                if let slider = sectionSlider {
                    updateSectionPlaneFromSlider(slider)
                }
            } else {
                removeSectionPlane()
            }
        }
        
        // MARK: - Slider-based Section Plane Update
        
        /// Updates the clipping plane based on the slider value (interpreted in the object's local space).
        @objc func updateSectionPlaneFromSlider(_ slider: UISlider) {
            guard let boxNode = currentBoxNode,
                  let material = boxNode.geometry?.firstMaterial else { return }
            
            // The slider value now represents the object's local x-coordinate.
            let localCutValue = slider.value
            
            // Update or create the visualization plane.
            if sectionPlane == nil {
                let plane = SCNPlane(width: 1, height: 1)
                plane.materials.first?.diffuse.contents = UIColor.red.withAlphaComponent(0.3)
                plane.materials.first?.isDoubleSided = true
                sectionPlane = SCNNode(geometry: plane)
                boxNode.addChildNode(sectionPlane!)
            }
            
            sectionPlane?.position = SCNVector3(localCutValue, 0, 0)
            sectionPlane?.eulerAngles.y = Float.pi / 2
            
            if sectionViewEnabled {
                if material.shaderModifiers?[.surface] == nil {
                    let shaderModifier = """
                    #pragma arguments
                    float u_cutPlane;
                    #pragma body
                    if (_surface.position.x < u_cutPlane) {
                        discard_fragment();
                    }
                    """
                    material.shaderModifiers = [.surface: shaderModifier]
                }
                material.setValue(localCutValue, forKey: "u_cutPlane")
            } else {
                material.shaderModifiers = [:]
            }
        }
        
        // MARK: - Hit-Test Based Section Plane Update
        
        /// When the user taps on the object's surface, uses the hit-test result to update the section plane.
        func updateSectionPlane(withHit hit: SCNHitTestResult) {
            guard let boxNode = currentBoxNode,
                  let material = boxNode.geometry?.firstMaterial else { return }
            
            // Convert the hit-test world coordinates into the object's local space.
            let worldPlanePoint = hit.worldCoordinates
            let localPlanePoint = boxNode.convertPosition(worldPlanePoint, from: nil)
            let localPlaneNormal = boxNode.convertVector(self.normalized(hit.worldNormal), from: nil)
            
            // Set up the shader modifier (if not already set) to perform clipping in local space.
            if material.shaderModifiers?[.surface] == nil {
                let shaderModifier = """
                #pragma arguments
                float3 u_planePoint;
                float3 u_planeNormal;
                #pragma body
                if (dot(_surface.position - u_planePoint, u_planeNormal) < 0.0) {
                    discard_fragment();
                }
                """
                material.shaderModifiers = [.surface: shaderModifier]
            }
            
            material.setValue(NSValue(scnVector3: localPlanePoint), forKey: "u_planePoint")
            material.setValue(NSValue(scnVector3: localPlaneNormal), forKey: "u_planeNormal")
            
            // Update (or create) the visualization plane.
            if sectionPlane == nil {
                let visPlane = SCNPlane(width: 1, height: 1)
                visPlane.materials.first?.diffuse.contents = UIColor.red.withAlphaComponent(0.3)
                visPlane.materials.first?.isDoubleSided = true
                sectionPlane = SCNNode(geometry: visPlane)
                boxNode.addChildNode(sectionPlane!)
            }
            
            sectionPlane?.position = localPlanePoint
            // Align the visualization plane: compute rotation from default normal (0,0,1) to the local plane normal.
            let defaultNormal = SCNVector3(0, 0, 1)
            let rotationQuat = quaternionFrom(from: defaultNormal, to: localPlaneNormal)
            sectionPlane?.orientation = rotationQuat
        }
        
        private func removeSectionPlane() {
            sectionPlane?.removeFromParentNode()
            sectionPlane = nil
            currentBoxNode?.geometry?.firstMaterial?.shaderModifiers = [:]
        }
        
        // MARK: - Helper Functions
        
        /// Returns a normalized version of the provided vector.
        private func normalized(_ vector: SCNVector3) -> SCNVector3 {
            let len = sqrt(vector.x * vector.x + vector.y * vector.y + vector.z * vector.z)
            if len == 0 { return vector }
            return SCNVector3(vector.x / len, vector.y / len, vector.z / len)
        }
        
        /// Computes a quaternion that rotates from one vector to another.
        private func quaternionFrom(from: SCNVector3, to: SCNVector3) -> SCNQuaternion {
            let v0 = normalized(from)
            let v1 = normalized(to)
            let dotVal = v0.x * v1.x + v0.y * v1.y + v0.z * v1.z
            if dotVal >= 1.0 {
                return SCNQuaternion(0, 0, 0, 1)
            }
            if dotVal < -0.9999 {
                let axis = abs(v0.x) < 0.1 ? SCNVector3(1, 0, 0) : SCNVector3(0, 1, 0)
                let ortho = normalized(v0.cross(axis))
                return SCNQuaternion(ortho.x, ortho.y, ortho.z, 0)
            }
            let s = sqrt((1 + dotVal) * 2)
            let invs = 1 / s
            let crossVec = v0.cross(v1)
            return SCNQuaternion(crossVec.x * invs, crossVec.y * invs, crossVec.z * invs, s * 0.5)
        }
    }
}

// MARK: - SCNVector3 Extensions for Dot and Cross

extension SCNVector3 {
    func dot(_ v: SCNVector3) -> Float {
        return x * v.x + y * v.y + z * v.z
    }
    
    func cross(_ v: SCNVector3) -> SCNVector3 {
        return SCNVector3(
            y * v.z - z * v.y,
            z * v.x - x * v.z,
            x * v.y - y * v.x
        )
    }
}
