import SwiftUI
import ARKit
import SceneKit

struct ARCubeView: View {
    @State private var cutPosition: Float = 0
    @State private var isCutMode: Bool = false
    @State private var selectedFaceIndex: Int?
    @State private var scaleText: String = "Scale: 100%"
    
    var body: some View {
        ZStack {
            ARCubeSceneView(cutPosition: $cutPosition,
                           isCutMode: $isCutMode,
                           selectedFaceIndex: $selectedFaceIndex)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                Spacer()
                HStack {
                    Button(action: {
                        isCutMode.toggle()
                        if !isCutMode {
                            if let index = selectedFaceIndex {
                                ARCubeSceneView.resetFaceColor(index: index)
                            }
                            selectedFaceIndex = nil
                        }
                    }) {
                        Text(isCutMode ? "Exit Cross Section" : "Cross Section")
                            .font(.headline)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(10)
                    }
                }
                .padding()
            }
        }
    }
}

struct ARCubeSceneView: UIViewRepresentable {
    @Binding var cutPosition: Float
    @Binding var isCutMode: Bool
    @Binding var selectedFaceIndex: Int?
    var arView = ARSCNView()
    
    static var faceMaterials: [SCNMaterial] = {
        (0..<6).map { _ in
            let material = SCNMaterial()
            material.diffuse.contents = UIColor.red
            return material
        }
    }()
    
    static func resetFaceColor(index: Int) {
        faceMaterials[index].diffuse.contents = UIColor.red
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    func makeUIView(context: Context) -> ARSCNView {
        arView.delegate = context.coordinator
        arView.showsStatistics = true
        arView.autoenablesDefaultLighting = true
        
        // Configure AR session
        let configuration = ARWorldTrackingConfiguration()
        configuration.planeDetection = [.horizontal]
        arView.session.run(configuration)
        
        // Add instruction label
        context.coordinator.setupInstructionLabel(in: arView)
        
        // Add gesture recognizers
        let tapGesture = UITapGestureRecognizer(target: context.coordinator,
                                               action: #selector(Coordinator.handleTap(_:)))
        arView.addGestureRecognizer(tapGesture)
        
        let panGesture = UIPanGestureRecognizer(target: context.coordinator,
                                               action: #selector(Coordinator.handlePan(_:)))
        arView.addGestureRecognizer(panGesture)
        
        let pinchGesture = UIPinchGestureRecognizer(target: context.coordinator,
                                                   action: #selector(Coordinator.handlePinch(_:)))
        arView.addGestureRecognizer(pinchGesture)
        
        return arView
    }
    
    func updateUIView(_ uiView: ARSCNView, context: Context) {
        // Update the view if needed based on binding changes
        context.coordinator.updateCutPosition(cutPosition)
    }
    
    class Coordinator: NSObject, ARSCNViewDelegate {
        var parent: ARCubeSceneView
        var currentBoxNode: SCNNode?
        var isObjectCreated = false
        var baselinePosition: SCNVector3?
        var instructionLabel: UILabel!
        
        init(_ parent: ARCubeSceneView) {
            self.parent = parent
            super.init()
        }
        
        func setupInstructionLabel(in view: ARSCNView) {
            instructionLabel = UILabel()
            instructionLabel.translatesAutoresizingMaskIntoConstraints = false
            instructionLabel.textColor = .white
            instructionLabel.backgroundColor = UIColor.black.withAlphaComponent(0.7)
            instructionLabel.layer.cornerRadius = 10
            instructionLabel.clipsToBounds = true
            instructionLabel.textAlignment = .center
            instructionLabel.font = UIFont.systemFont(ofSize: 16)
            instructionLabel.text = "Move your device around to detect surfaces.\nTap on any surface to place the cube."
            instructionLabel.numberOfLines = 0
            
            view.addSubview(instructionLabel)
            
            NSLayoutConstraint.activate([
                instructionLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                instructionLabel.centerYAnchor.constraint(equalTo: view.centerYAnchor),
                instructionLabel.widthAnchor.constraint(equalToConstant: 350),
                instructionLabel.heightAnchor.constraint(equalToConstant: 50)
            ])
        }
        
        @objc func handleTap(_ gestureRecognize: UIGestureRecognizer) {
            guard let arView = gestureRecognize.view as? ARSCNView else { return }
            let touchLocation = gestureRecognize.location(in: arView)
            
            if !isObjectCreated {
                if let query = arView.raycastQuery(from: touchLocation,
                                                 allowing: .existingPlaneGeometry,
                                                 alignment: .any),
                   let raycastResult = arView.session.raycast(query).first {
                    
                    let boxWidth: CGFloat = 0.1
                    let boxHeight: CGFloat = 0.1
                    let boxLength: CGFloat = 0.1
                    let box = SCNBox(width: boxWidth, height: boxHeight, length: boxLength, chamferRadius: 0)
                    box.materials = ARCubeSceneView.faceMaterials
                    let boxNode = SCNNode(geometry: box)
                    
                    // Set the pivot so that the bottom of the box is the node's origin
                    boxNode.pivot = SCNMatrix4MakeTranslation(0, -Float(boxHeight) / 2, 0)
                    boxNode.position = SCNVector3(
                        raycastResult.worldTransform.columns.3.x,
                        raycastResult.worldTransform.columns.3.y,
                        raycastResult.worldTransform.columns.3.z
                    )
                    
                    arView.scene.rootNode.addChildNode(boxNode)
                    currentBoxNode = boxNode
                    isObjectCreated = true
                    
                    DispatchQueue.main.async {
                        self.instructionLabel.isHidden = true
                    }
                }
            } else if parent.isCutMode {
                let hitResults = arView.hitTest(touchLocation, options: nil)
                if let hit = hitResults.first {
                    handleFaceSelection(hit)
                }
            }
        }
        
        @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
            guard let arView = gesture.view as? ARSCNView,
                  parent.isCutMode,
                  parent.selectedFaceIndex != nil else { return }
            
            let translation = gesture.translation(in: arView)
            let delta = Float(translation.y) * 0.005
            parent.cutPosition = max(-0.5, min(0.5, parent.cutPosition + delta))
            updateCutPosition(parent.cutPosition)
            gesture.setTranslation(.zero, in: arView)
        }
        
        @objc func handlePinch(_ gesture: UIPinchGestureRecognizer) {
            guard let node = currentBoxNode else { return }
            
            if gesture.state == .changed {
                let scale = Float(gesture.scale)
                node.scale = SCNVector3(scale, scale, scale)
                gesture.scale = 1.0
            }
        }
        
        func handleFaceSelection(_ hit: SCNHitTestResult) {
            guard let node = currentBoxNode,
                  let box = node.geometry as? SCNBox else { return }
            
            // Reset previous selection
            if let previousIndex = parent.selectedFaceIndex {
                ARCubeSceneView.resetFaceColor(index: previousIndex)
            }
            
            // Determine which face was hit based on the hit point
            let hitPoint = hit.localCoordinates
            let faceIndex = determineFaceIndex(from: hitPoint)
            
            // Set new selection
            parent.selectedFaceIndex = faceIndex
            box.materials[faceIndex].diffuse.contents = UIColor.yellow
        }
        
        func determineFaceIndex(from point: SCNVector3) -> Int {
            let epsilon: Float = 0.001
            let size: Float = 0.05
            
            if abs(point.z - size) < epsilon { return 0 } // Front
            if abs(point.x - size) < epsilon { return 1 } // Right
            if abs(point.z + size) < epsilon { return 2 } // Back
            if abs(point.x + size) < epsilon { return 3 } // Left
            if abs(point.y - size) < epsilon { return 4 } // Top
            return 5 // Bottom
        }
        
        func updateCutPosition(_ position: Float) {
            guard let node = currentBoxNode,
                  let box = node.geometry as? SCNBox,
                  let faceIndex = parent.selectedFaceIndex else { return }
            
            let originalSize: CGFloat = 0.1
            let newSize = CGFloat(1.0 - abs(position)) * originalSize
            
            // Apply the cut based on the selected face
            switch faceIndex {
            case 0, 2: // Front/Back faces
                box.length = newSize
            case 1, 3: // Right/Left faces
                box.width = newSize
            case 4, 5: // Top/Bottom faces
                box.height = newSize
            default:
                break
            }
        }
        
        // MARK: - ARSCNViewDelegate
        
        func renderer(_ renderer: SCNSceneRenderer, didAdd node: SCNNode, for anchor: ARAnchor) {
            guard let planeAnchor = anchor as? ARPlaneAnchor else { return }
            
            DispatchQueue.main.async { [weak self] in
                self?.instructionLabel.isHidden = true
            }
            
            let width = CGFloat(planeAnchor.planeExtent.width)
            let height = CGFloat(planeAnchor.planeExtent.height)
            let plane = SCNPlane(width: width, height: height)
            plane.materials.first?.diffuse.contents = UIColor(white: 1, alpha: 0.7)
            
            let planeNode = SCNNode(geometry: plane)
            planeNode.position = SCNVector3(planeAnchor.center.x, 0, planeAnchor.center.z)
            planeNode.eulerAngles.x = -.pi / 2
            
            node.addChildNode(planeNode)
        }
        
        func renderer(_ renderer: SCNSceneRenderer, didUpdate node: SCNNode, for anchor: ARAnchor) {
            guard let planeAnchor = anchor as? ARPlaneAnchor,
                  let planeNode = node.childNodes.first,
                  let plane = planeNode.geometry as? SCNPlane else {
                return
            }
            
            // Update plane dimensions
            plane.width = CGFloat(planeAnchor.planeExtent.width)
            plane.height = CGFloat(planeAnchor.planeExtent.height)
            
            // Update position
            planeNode.position = SCNVector3(planeAnchor.center.x, 0, planeAnchor.center.z)
        }
    }
}

struct ARCubeView_Previews: PreviewProvider {
    static var previews: some View {
        ARCubeView()
    }
}
