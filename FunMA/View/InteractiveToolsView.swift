import Foundation
import SwiftUI
import WebKit

// Define available interactive tools
struct InteractiveTool: Identifiable {
    let id = UUID()
    let name: String
    let url: URL
    let description: String
    let icon: String  // SF Symbol name
}

struct InteractiveToolsView: View {
    @State private var selectedTool: InteractiveTool?
    
    // List of available tools
    private let tools: [InteractiveTool] = [
        InteractiveTool(
            name: "Directed Numbers Game",
            url: URL(string: "https://scratch.mit.edu/projects/1147850469/embed")!,
            description: "Interactive game to practice adding and subtracting positive and negative numbers",
            icon: "plus.forwardslash.minus"
        ),
        InteractiveTool(
            name: "Pythagoras Theorem Explorer",
            url: URL(string: "https://www.geogebra.org/material/iframe/id/cbwxjxgd/width/1300/height/500/border/888888/sfsb/true/smb/false/stb/false/stbh/false/ai/false/asb/false/sri/false/rc/false/ld/false/sdz/false/ctl/false")!,
            description: "Interactive tool to explore and verify the Pythagorean Theorem with dynamic right triangles",
            icon: "triangle"
        ),
        InteractiveTool(
            name: "Significant Figures & Rounding",
            url: URL(string: "https://scratch.mit.edu/projects/1130100135/embed")!,
            description: "Interactive tool to practice identifying significant figures and rounding numbers to specified decimal places",
            icon: "number.square"
        ),
        InteractiveTool(
            name: "Volume Explorer",
            url: URL(string: "https://local-volume-explorer")!, // Placeholder URL since it's a native view
            description: "Interactive tool to explore the relationship between cylinder and cone volumes",
            icon: "cylinder"
        ),
        InteractiveTool(
            name: "Unfold Game",
            url: URL(string: "https://local-unfold-game")!, // Placeholder, not used
            description: "Visualize and unfold a 3D prism, then answer a surface area question.",
            icon: "cube.transparent"
        ),
//        InteractiveTool(
//            name: "Desmos Geometry",
//            url: URL(string: "https://www.desmos.com/geometry")!,
//            description: "Interactive geometry tool with dynamic constructions",
//            icon: "square.grid.2x2"
//        )
    ]
    
    var body: some View {
        VStack {
            // Header
            HStack {
                Text("Interactive Tools")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                    .padding(.leading)
                Spacer()
            }
            
            if let selectedTool = selectedTool {
                if selectedTool.name == "Unfold Game" {
                    // Show the native UnfoldGameView
                    UnfoldGameView()
                        .frame(minWidth: 600, minHeight: 400)
                        .cornerRadius(10)
                        .padding()
                } else if selectedTool.name == "Volume Explorer" {
                    // Show the native VolumeExplorerView
                    VolumeExplorerView()
                        .frame(minWidth: 600, minHeight: 400)
                        .cornerRadius(10)
                        .padding()
                } else {
                    // Tool header
                    HStack {
                        Image(systemName: selectedTool.icon)
                            .font(.title)
                        Text(selectedTool.name)
                            .font(.title2)
                            .fontWeight(.semibold)
                        Spacer()
                        Button(action: {
                            self.selectedTool = nil
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                                .font(.title2)
                        }
                    }
                    .padding()
                    
                    // Tool description
                    Text(selectedTool.description)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .padding(.horizontal)
                        .padding(.bottom)
                    
                    // WebView for the selected tool
                    WebView(url: selectedTool.url)
                        .frame(minWidth: 600, minHeight: 400)
                        .cornerRadius(10)
                        .padding()
                }
            } else {
                // Tool selection grid
                ScrollView {
                    LazyVGrid(columns: [
                        GridItem(.flexible(), spacing: 16),
                        GridItem(.flexible(), spacing: 16)
                    ], spacing: 16) {
                        ForEach(tools) { tool in
                            Button(action: {
                                selectedTool = tool
                            }) {
                                VStack {
                                    Image(systemName: tool.icon)
                                        .font(.system(size: 40))
                                        .foregroundColor(.blue)
                                        .padding()
                                    
                                    Text(tool.name)
                                        .font(.headline)
                                        .multilineTextAlignment(.center)
                                    
                                    Text(tool.description)
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                        .multilineTextAlignment(.center)
                                        .lineLimit(3)
                                        .padding(.horizontal, 8)
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.white)
                                .cornerRadius(10)
                                .shadow(radius: 2)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding()
                }
            }
        }
    }
}

// WebView to load external URLs
struct WebView: UIViewRepresentable {
    let url: URL

    func makeUIView(context: Context) -> WKWebView {
        return WKWebView()
    }

    func updateUIView(_ uiView: WKWebView, context: Context) {
        let request = URLRequest(url: url)
        uiView.load(request)
    }
}

struct InteractiveToolsView_Previews: PreviewProvider {
    static var previews: some View {
        InteractiveToolsView()
    }
} 
