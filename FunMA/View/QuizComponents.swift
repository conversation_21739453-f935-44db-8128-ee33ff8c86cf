//
//  QuizComponents.swift
//  Luminous Education
//
//  Created by <PERSON> on 6/11/2024.
//

import SwiftUI

// Custom Button Style for Responses
struct ResponseButtonStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.white)
            .overlay(RoundedRectangle(cornerRadius: 10).stroke(Color.gray, lineWidth: 1))
            .foregroundColor(.black)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut, value: configuration.isPressed)
    }
}

struct QuestionIndicatorView: View {
    var currentQuestionIndex: Int
    var totalQuestions: Int
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("Question \(currentQuestionIndex)/\(totalQuestions)")
                .font(.headline)
                .fontWeight(.bold)
            
            // Question number indicators
            HStack(spacing: 5) {
                ForEach(1...totalQuestions, id: \.self) { index in
                    Text("\(index)")
                        .frame(width: 30, height: 30)
                        .background(index == currentQuestionIndex ? Color.blue : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(5)
                }
            }
        }
    }
}

struct QuestionImageTextView: View {
    var questionText: String
    var imageName: String?
    
    var body: some View {
        VStack(spacing: 10) {
            if let imageName = imageName {
                Image(imageName)
                    .resizable()
                    .scaledToFit()
                    .frame(height: 200)
                    .cornerRadius(10)
            } else {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .frame(height: 200)
                    .overlay(Text("(Question Image)").foregroundColor(.white))
                    .cornerRadius(10)
            }
            
            QuestionTextRenderer(text: questionText, fontSize: 18)
                .fontWeight(.medium)
                .multilineTextAlignment(.leading)
                .padding()
        }
    }
}

struct QuestionImageOnlyView: View {
    var imageName: String?
    
    var body: some View {
        if let imageName = imageName {
            Image(imageName)
                .resizable()
                .scaledToFit()
                .frame(height: 200)
                .cornerRadius(10)
        } else {
            Rectangle()
                .fill(Color.gray.opacity(0.3))
                .frame(height: 200)
                .overlay(Text("(Question Image)").foregroundColor(.white))
                .cornerRadius(10)
        }
    }
}

struct QuestionTextOnlyView: View {
    var questionText: String = "What is the main concept explained in this lesson?"
    
    var body: some View {
        QuestionTextRenderer(text: questionText, fontSize: 20)
            .fontWeight(.medium)
            .multilineTextAlignment(.leading)
            .padding()
            .frame(minHeight: 100)
    }
}

struct ResponseMCView: View {
    var options: [String] = [
        "A: First option that could be correct",
        "B: Second option that might be correct",
        "C: Third option as another possibility",
        "D: Fourth option as the final choice"
    ]
    @Binding var selectedOption: Int?
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(0..<options.count, id: \.self) { index in
                Button(action: {
                    selectedOption = index
                }) {
                    HStack {
                        Text(options[index])
                        Spacer()
                        if selectedOption == index {
                            Image(systemName: "checkmark.circle")
                                .foregroundColor(.green)
                        }
                    }
                }
                .buttonStyle(ResponseButtonStyle())
                .opacity(selectedOption != nil && selectedOption != index ? 0.7 : 1.0)
            }
        }
    }
}

struct ResponseSAQView: View {
    @Binding var text: String
    
    var body: some View {
        TextEditor(text: $text)
            .border(Color.gray, width: 1)
            .frame(height: 250)
    }
}

// Preview for the components
struct QuizComponentsPreviews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            QuestionIndicatorView(currentQuestionIndex: 2, totalQuestions: 5)
            
            QuestionTextOnlyView(questionText: "What is the primary function of mitochondria in a cell?")
            
            @State var selectedOption: Int? = 1
            ResponseMCView(
                options: ["A: Cell division", "B: Energy production", "C: Protein synthesis", "D: Waste removal"],
                selectedOption: .constant(1)
            )
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
} 