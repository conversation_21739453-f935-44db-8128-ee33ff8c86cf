//
//  MyCourseView.swift
//  Luminous Education
//
//  Created by <PERSON> on 30/7/2024.
//

import SwiftUI
import Charts // Ensure to import Charts for graphing

struct DashboardView: View {
    @StateObject private var coursesViewModel = CoursesViewModel()
    @State private var searchText = ""
    @State private var isLoading = false
    
    var body: some View {
        NavigationStack {
            ZStack {
                VStack(spacing: 30) {
                    SearchBar(searchText: $searchText)
                    Spacer()
                    
                    if isLoading {
                        ProgressView("Loading your courses...")
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.2)
                    } else {
                        CourseOverview(coursesViewModel: coursesViewModel)
                        HStack(spacing: 20){
                            PerformanceChart()
                            Recommendations()
                        }
                        Spacer()
                        Notifications()
                    }
                }
                .padding()
                .environmentObject(coursesViewModel)
                .navigationDestination(for: String.self) { destination in
                    if destination == "allCourses" {
                        EnrolledCoursesView()
                    } else if destination == "recommendations" {
                        Text("Recommendations View")
                            .font(.largeTitle)
                            .padding()
                    } else if destination == "marketplace" {
                        MarketplaceView()
                    }
                }
                .navigationDestination(for: Course.self) { course in
                    CourseView(course: course)
                        .environmentObject(coursesViewModel)
                }
                // .onAppear {
                //     refreshCoursesData()
                // }
            }
        }
    }
    
    // private func refreshCoursesData() {
    //     isLoading = true
    //     Task {
    //         await coursesViewModel.loadCourses()
    //         await MainActor.run {
    //             isLoading = false
    //         }
    //     }
    // }
}

struct SearchBar: View {
    @Binding var searchText: String // Binding to the parent view

    var body: some View {
        TextField("Search...", text: $searchText)
            .padding(.vertical, 15) // Top and bottom padding
            .padding(.horizontal, 20) // Left and right padding
            .cornerRadius(10)
            .overlay(
                RoundedRectangle(cornerRadius: 5)
                    .stroke(Color.gray, lineWidth: 1) // Border color and width
            )
            .padding(10) // Optional padding around the border
        // Additional content can go here
//        Text("Searching for: \(searchText)")
//            .padding()
    }
}

struct CourseOverview: View {
    @ObservedObject var coursesViewModel: CoursesViewModel
    
    var body: some View {
        VStack(alignment: .leading) {
            HStack{
                Text("My Courses")
                    .font(.title2)
                    .bold()
                Spacer()
                NavigationLink(value: "allCourses") {
                    Text("View All")
                        .font(.headline)
                        .foregroundColor(.blue)
                }
                .buttonStyle(PlainButtonStyle())
            }
            
            Group {
                if coursesViewModel.courses.isEmpty {
                    EmptyCoursesCard()
                } else {
                    ScrollView(.horizontal) {
                        HStack(spacing: 20) {
                            ForEach(coursesViewModel.courses) { course in
                                CourseCard(course: course)
                                    .environmentObject(coursesViewModel)
                            }
                        }
                    }
                }
            }
            .padding(.bottom)
        }
    }
}

struct EmptyCoursesCard: View {
    var body: some View {
        NavigationLink(value: "marketplace") {
            VStack(alignment: .center, spacing: 15) {
                Image(systemName: "books.vertical.fill")
                    .font(.system(size: 50))
                    .foregroundColor(.blue)
                    .padding(.top, 20)
                
                Text("No Courses Yet")
                    .font(.headline)
                    .bold()
                
                Text("Explore our marketplace to find and enroll in exciting courses to start your learning journey.")
                    .font(.subheadline)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                
                Text("Go to Marketplace")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.blue)
                    .cornerRadius(8)
                    .padding(.vertical, 10)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct CourseCard: View {
    let course: Course
    @EnvironmentObject var coursesViewModel: CoursesViewModel
    
    // Get progress value for this course
    private var progress: Int {
        coursesViewModel.getProgressForCourse(courseID: course.courseID)
    }
    
    // Compute progress percentage for visual indicator
    private var progressPercentage: Double {
        Double(progress) / Double(course.total)
    }
    
    var body: some View {
        NavigationLink(value: course) {
            VStack(alignment: .leading) {
                Text(course.name)
                    .font(.headline)
                    .padding(.bottom, 20)
                    
                HStack {
                    Text("\(progress)/\(course.total)")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .foregroundColor(.blue)
                }
                
                ProgressView(value: progressPercentage)
                    .progressViewStyle(LinearProgressViewStyle())
                    .frame(maxWidth: .infinity)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle()) // Use plain style for original appearance
    }
}

struct PerformanceChart: View {
    var body: some View {
        VStack(alignment: .leading) {
            Text("My Performance")
                .font(.title2)
                .bold()
            Chart {
                ForEach(0..<7, id: \.self) { index in
                    let score = Double.random(in: 0...30) // Sample score data
                    LineMark(
                        x: .value("Quiz", index),
                        y: .value("Score", score)
                    )
                }
            }
            .frame(height: 220)
            .padding(.bottom)
        }
    }
}

struct Recommendations: View {
    var body: some View {
        VStack(alignment: .leading) {
            HStack{
                Text("Recommendations")
                    .font(.title2)
                    .bold()
                Spacer()
                NavigationLink(value: "recommendations") {
                    Text("View All")
                        .font(.headline)
                        .foregroundColor(.blue)
                }
                .buttonStyle(PlainButtonStyle())
            }
            ForEach(0..<4) { _ in
                RecommendationItem()
            }
        }
    }
}

struct RecommendationItem: View {
    var body: some View {
        Button(action: {
            // Action to perform when the button is tapped
            print("Recommendation tapped")
        }) {
            HStack {
                Text("Basic Computation")
                Spacer()
                Text("10 mins")
                Image(systemName: "chevron.right")
                    .foregroundColor(.blue)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
        .buttonStyle(PlainButtonStyle()) // Use plain style for original appearance
    }
}

struct Notifications: View {
    var body: some View {
        Button(action: {
            // Action to perform when button is tapped
            print("Notification tapped")
        }) {
            HStack {
                Text("Your homework is now ready for review!")
                    .padding(.vertical, 15) // Top and bottom padding
                    .foregroundColor(.black) // Text color

                Image(systemName: "chevron.right")
                    .foregroundColor(.blue)
                    .padding(.leading, 10) // Space between text and image
            }
            .padding(.horizontal, 25)
            .overlay(
                RoundedRectangle(cornerRadius: 10)
                    .stroke(Color.blue, lineWidth: 1) // Border color and width
            )
        }
        .buttonStyle(PlainButtonStyle()) // Use plain style to avoid default button appearance
    }
}

struct MyCourseView_Previews: PreviewProvider {
    static var previews: some View {
        DashboardView()
            .environmentObject(CoursesViewModel())
    }
}
