//
//  QuizSummaryView.swift
//  Luminous Education
//
//  Created by <PERSON> on 8/8/2024.
//

import SwiftUI

struct QuizSummaryView: View {
    var yourScore: Int = 99
    var totalScore: Int = 100
    var timeTaken: String = "3 mins 15 secs"
    var averageScore: Int = 80
    var averageTime: String = "4 mins 45 secs"
    
    var body: some View {
        
        HStack{
            Text("Pre-quiz summary")
                .font(.largeTitle)
                .fontWeight(.bold)
            Spacer()
        }
        .padding(.leading)
        
        Spacer()
        // Centered Content
        VStack(alignment: .leading, spacing: 20) { // Align left
            // Your Score
            HStack {
                Text("Your score:")
                    .font(.title)
                    .bold()
                Text("\(yourScore)/\(totalScore)")
                    .font(.title)
                    .bold()
                    .foregroundColor(.green)
            }
            
            // Time Taken
            HStack {
                Text("Time taken:")
                    .font(.title2)
                Text(timeTaken)
                    .font(.title2)
                    .foregroundColor(.green)
            }
        }
        .frame(maxWidth: .infinity) // Center the inner VStack
        .padding()
        
        // Question Indicators
        HStack(spacing: 5) {
            ForEach(1...5, id: \.self) { index in
                Text("\(index)")
                    .frame(width: 50, height: 50)
                    .background(index == 1 ? Color.green : (index == 2 ? Color.green : (index == 3 ? Color.orange : (index == 4 ? Color.red : Color.gray))))
                    .foregroundColor(.white)
                    .cornerRadius(5)
            }
        }
        
        //        Divider()
        //            .padding(.vertical, 10)
        //
        //        VStack(alignment: .leading, spacing: 10) { // Align left
        //            // How others are doing
        //            Text("How others are doing?")
        //                .font(.title3)
        //
        //            // Average Score
        //            HStack {
        //                Text("Average score:")
        //                    .font(.title3)
        //                Text("\(averageScore)/\(totalScore)")
        //                    .font(.title3)
        //                    .foregroundColor(.green)
        //            }
        //
        //            // Average Time
        //            HStack {
        //                Text("Average time:")
        //                    .font(.title3)
        //                Text(averageTime)
        //                    .font(.title3)
        //                    .foregroundColor(.green)
        //            }
        //        }
        //        .frame(maxWidth: .infinity) // Center the inner VStack
        
        Spacer()
        
        // Navigation Buttons
        VStack{
            HStack {
                Spacer()
                Button(action: {
                    // Action for next
                }) {
                    Text("Review Question 1")
                        .padding()
                        .background(.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                }
            }
            HStack {
                Spacer()
                Button(action: {
                    // Action for next
                }) {
                    Text("Skip to Incorrect Questions >")
                        .padding(5)
                        .foregroundColor(.blue)
                        .cornerRadius(8)
                }
            }
        }
        .padding(.trailing)
    }
}

#Preview {
    QuizSummaryView()
}
