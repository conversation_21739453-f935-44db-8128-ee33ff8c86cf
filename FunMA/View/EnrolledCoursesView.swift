import SwiftUI

struct EnrolledCoursesView: View {
    @StateObject private var coursesViewModel = CoursesViewModel()
    @State private var isLoading = false
    
    var body: some View {
        ZStack {
            Color(UIColor.systemBackground)
                .ignoresSafeArea()
            
            VStack {
                if isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                } else if coursesViewModel.courses.isEmpty {
                    VStack(spacing: 20) {
                        Image(systemName: "books.vertical")
                            .font(.system(size: 70))
                            .foregroundColor(.gray)
                        
                        Text("No Enrolled Courses")
                            .font(.title2)
                            .fontWeight(.medium)
                        
                        Text("Visit the Marketplace to browse and enroll in courses")
                            .foregroundColor(.gray)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                        
                        NavigationLink(value: "marketplace") {
                            Text("Browse Courses")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(width: 200)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                    }
                    .padding()
                } else {
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            ForEach(coursesViewModel.courses) { course in
                                NavigationLink(value: course) {
                                    EnrolledCourseCard(course: course)
                                        .environmentObject(coursesViewModel)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding()
                    }
                }
            }
            .navigationTitle("My Enrolled Courses")
            .navigationDestination(for: String.self) { destination in
                if destination == "marketplace" {
                    MarketplaceView()
                }
            }
            .navigationDestination(for: Course.self) { course in
                CourseView(course: course)
                    .environmentObject(coursesViewModel)
            }
            .onAppear {
                // Fetch courses from the API when the view appears
                isLoading = true
                Task {
                    await coursesViewModel.loadCourses()
                    await MainActor.run {
                        isLoading = false
                    }
                }
            }
        }
    }
}

struct EnrolledCourseCard: View {
    let course: Course
    @EnvironmentObject var coursesViewModel: CoursesViewModel
    
    // Get appropriate SF Symbol based on course difficulty
    private var difficultySymbol: String {
        switch course.difficulty {
        case 1:
            return "1.circle"
        case 2:
            return "2.circle"
        case 3:
            return "3.circle"
        default:
            return "book.fill"
        }
    }
    
    // Determine category based on course name
    private var category: String {
        let name = course.name.lowercased()
        
        if name.contains("math") {
            return "Mathematics"
        } else if name.contains("english") {
            return "English"
        } else if name.contains("science") {
            return "Science"
        } else if name.contains("chinese") {
            return "Chinese"
        } else {
            return "General"
        }
    }
    
    // Get appropriate SF Symbol based on determined category
    private var categorySymbol: String {
        switch category.lowercased() {
        case "mathematics":
            return "function"
        case "english":
            return "text.book.closed"
        case "science":
            return "atom"
        case "chinese":
            return "character"
        default:
            return "book.fill"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            // Course title
            Text(course.name)
                .font(.headline)
                .padding(.bottom, 10)
                
            HStack {
                // Progress info with progress value from the viewModel
                Text("\(coursesViewModel.getProgressForCourse(courseID: course.courseID))/\(course.total)")
                Spacer()
                Image(systemName: "chevron.right")
                    .foregroundColor(.blue)
            }
            
            // Progress bar
            ProgressView(value: Double(coursesViewModel.getProgressForCourse(courseID: course.courseID)), total: Double(course.total))
                .progressViewStyle(LinearProgressViewStyle())
                .frame(width: nil)
                .padding(.top, 5)
            
            // Course details
            HStack(spacing: 20) {
                // Course category
                HStack {
                    Image(systemName: categorySymbol)
                        .foregroundColor(.blue)
                    Text(category)
                        .font(.caption)
                }
                
                // Course difficulty
                HStack {
                    Image(systemName: difficultySymbol)
                        .foregroundColor(.blue)
                    Text(course.level.rawValue)
                        .font(.caption)
                }
                
                // Lessons count
                HStack {
                    Image(systemName: "book")
                        .foregroundColor(.blue)
                    Text("\(course.totalLessons) lessons")
                        .font(.caption)
                }
            }
            .padding(.top, 10)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(10)
    }
}

// Extension to apply different corner radius to different corners
extension View {
    func cornerRadius(_ radius: CGFloat, corners: UIRectCorner) -> some View {
        clipShape(RoundedCorner(radius: radius, corners: corners))
    }
}

// Custom shape for rounded corners
struct RoundedCorner: Shape {
    var radius: CGFloat = .infinity
    var corners: UIRectCorner = .allCorners
    
    func path(in rect: CGRect) -> Path {
        let path = UIBezierPath(
            roundedRect: rect,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        return Path(path.cgPath)
    }
}

struct EnrolledCoursesView_Previews: PreviewProvider {
    static var previews: some View {
        EnrolledCoursesView()
    }
} 