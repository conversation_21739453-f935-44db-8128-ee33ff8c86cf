//
//  QuizView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/8/2024.
//

import SwiftUI

struct QuizView: View {
    // Properties
    var lessonId: String
    var quizType: String  // "pre-quiz" or "post-quiz"
    
    // Quiz state
    @State private var currentQuestionIndex = 0
    @State private var score = 0
    @State private var showResults = false
    @State private var answers: [Int?] = []
    
    // Mock questions - in real app, would fetch based on lessonId and quizType
    private var questions: [QuizQuestion] {
        if quizType == "pre-quiz" {
            return [
                QuizQuestion(
                    question: "What do you already know about this topic?",
                    options: ["Very little", "Some basics", "Intermediate knowledge", "Expert level"],
                    correctAnswerIndex: nil,  // No correct answer for pre-quiz assessment
                    imageName: nil
                ),
                QuizQuestion(
                    question: "What are you hoping to learn from this lesson?",
                    options: ["Core concepts", "Practical applications", "Advanced techniques", "Historical context"],
                    correctAnswerIndex: nil,  // No correct answer for pre-quiz assessment
                    imageName: nil
                )
            ]
        } else {
            // Post-quiz questions
            return [
                QuizQuestion(
                    question: "The figure shows a parallelogram. Find y.",
                    options: ["11/4", "3", "4", "5"],
                    correctAnswerIndex: 3,
                    imageName: "parallelogram_quiz"  // Example image name
                ),
                QuizQuestion(
                    question: "Which of the following best describes the concept explained at the 2-minute mark?",
                    options: ["Process 1", "Process 2", "Process 3", "Process 4"],
                    correctAnswerIndex: 1,
                    imageName: nil
                ),
                QuizQuestion(
                    question: "Based on the video, what would happen if you applied this concept in a different context?",
                    options: ["Result A", "Result B", "Result C", "Result D"],
                    correctAnswerIndex: 3,
                    imageName: nil
                )
            ]
        }
    }
    
    init(lessonId: String, quizType: String = "post-quiz") {
        self.lessonId = lessonId
        self.quizType = quizType
        // Initialize answers array with nil values for each question
        self._answers = State(initialValue: Array(repeating: nil, count: quizType == "pre-quiz" ? 2 : 3))
    }
    
    var body: some View {
        ZStack {
            // Background
            LinearGradient(
                gradient: Gradient(colors: [Color.blue.opacity(0.7), Color.purple.opacity(0.7)]),
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .edgesIgnoringSafeArea(.all)
            
            if showResults {
                // Results view
                resultsView
            } else {
                // Quiz questions view
                questionView
            }
        }
        .navigationBarTitle(quizType == "pre-quiz" ? "Pre-Quiz" : "Exercise", displayMode: .inline)
    }
    
    // Question view
    private var questionView: some View {
        VStack(spacing: 30) {
            // Progress indicator
            ProgressView(value: Double(currentQuestionIndex + 1), total: Double(questions.count))
                .padding(.horizontal)
            
            Text("Question \(currentQuestionIndex + 1) of \(questions.count)")
                .font(.headline)
                .foregroundColor(.white)
            
            // Question card
            VStack(alignment: .leading, spacing: 20) {
                // Question text
                Text(questions[currentQuestionIndex].question)
                    .font(.title3)
                    .fontWeight(.bold)
                    .padding(.top)
                
                // Question image (if available)
                if let imageName = questions[currentQuestionIndex].imageName {
                    HStack {
                        Spacer()
                        Image(imageName)
                            .resizable()
                            .scaledToFit()
                            .frame(maxHeight: 200)
                            .cornerRadius(10)
                        Spacer()
                    }
                    .padding(.vertical)
                }
                
                // Answer options
                ForEach(0..<questions[currentQuestionIndex].options.count, id: \.self) { index in
                    Button(action: {
                        selectAnswer(index)
                    }) {
                        HStack {
                            Text("\(["A", "B", "C", "D"][index])")
                                .font(.headline)
                                .foregroundColor(.white)
                                .frame(width: 30, height: 30)
                                .background(answerButtonBackgroundColor(for: index))
                                .cornerRadius(15)
                            
                            Text(questions[currentQuestionIndex].options[index])
                                .foregroundColor(.black)
                                .padding(.leading, 5)
                            
                            Spacer()
                        }
                        .padding()
                        .background(Color.white)
                        .cornerRadius(10)
                    }
                }
                
                Spacer()
                
                // Navigation buttons
                HStack {
                    // Back button (if not on first question)
                    if currentQuestionIndex > 0 {
                        Button(action: {
                            currentQuestionIndex -= 1
                        }) {
                            HStack {
                                Image(systemName: "arrow.left")
                                Text("Previous")
                            }
                            .padding()
                            .foregroundColor(.white)
                            .background(Color.gray.opacity(0.3))
                            .cornerRadius(8)
                        }
                    }
                    
                    Spacer()
                    
                    // Next/Submit button
                    Button(action: {
                        if currentQuestionIndex < questions.count - 1 {
                            // Move to next question
                            currentQuestionIndex += 1
                        } else {
                            // Show results
                            calculateScore()
                            showResults = true
                        }
                    }) {
                        Text(currentQuestionIndex < questions.count - 1 ? "Next" : "Submit")
                            .padding()
                            .foregroundColor(.white)
                            .background(answers[currentQuestionIndex] != nil ? Color.blue : Color.gray)
                            .cornerRadius(8)
                    }
                    .disabled(answers[currentQuestionIndex] == nil)
                }
            }
            .padding()
            .background(Color.white.opacity(0.9))
            .cornerRadius(15)
            .padding()
        }
    }
    
    // Results view
    private var resultsView: some View {
        VStack(spacing: 25) {
            // Results header
            if quizType == "post-quiz" {
                Image(systemName: score >= questions.count * 70 / 100 ? "trophy.fill" : "star.fill")
                    .font(.system(size: 70))
                    .foregroundColor(score >= questions.count * 70 / 100 ? .yellow : .orange)
                    .padding()
                
                Text("Quiz Completed!")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("You scored \(score) out of \(questions.count)")
                    .font(.title2)
                    .foregroundColor(.white)
                
                // Result message
                Text(resultMessage)
                    .font(.headline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .padding()
            } else {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 70))
                    .foregroundColor(.green)
                    .padding()
                
                Text("Assessment Complete")
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
                
                Text("Thank you for completing the pre-quiz assessment.")
                    .font(.headline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            
            // Actions
            VStack(spacing: 15) {
                // Review Answers button (new)
                NavigationLink(destination: QuizReviewView(questions: questions, userAnswers: answers, lessonId: lessonId)) {
                    Text("Review Answers")
                        .font(.headline)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(10)
                        .padding(.horizontal)
                }
                
                // NavigationLink(destination: lessonView) {
                //     Text(quizType == "pre-quiz" ? "Start Lesson" : "Return to Course")
                //         .font(.headline)
                //         .foregroundColor(.white)
                //         .frame(maxWidth: .infinity)
                //         .padding()
                //         .background(Color.blue)
                //         .cornerRadius(10)
                //         .padding(.horizontal)
                // }
                
                if quizType == "post-quiz" {
                    Button(action: {
                        // Reset quiz
                        currentQuestionIndex = 0
                        score = 0
                        showResults = false
                        answers = Array(repeating: nil, count: questions.count)
                    }) {
                        Text("Try Again")
                            .font(.headline)
                            .foregroundColor(.blue)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.white)
                            .cornerRadius(10)
                            .padding(.horizontal)
                    }
                }
            }
        }
        .padding()
        .background(Color.black.opacity(0.3))
        .cornerRadius(20)
        .padding()
    }
    
    // Return to lesson view placeholder
    private var lessonView: some View {
        Text("Course Home")
            .navigationBarTitle("Course Home")
    }
    
    // Helper methods
    private func selectAnswer(_ index: Int) {
        answers[currentQuestionIndex] = index
    }
    
    private func answerButtonBackgroundColor(for index: Int) -> Color {
        if answers[currentQuestionIndex] == index {
            return Color.blue
        }
        return Color.gray.opacity(0.5)
    }
    
    private func calculateScore() {
        guard quizType == "post-quiz" else { return }
        
        score = 0
        for (index, question) in questions.enumerated() {
            if let selectedAnswer = answers[index],
               let correctAnswer = question.correctAnswerIndex,
               selectedAnswer == correctAnswer {
                score += 1
            }
        }
    }
    
    private var resultMessage: String {
        let percentage = (Double(score) / Double(questions.count)) * 100
        
        if percentage >= 90 {
            return "Excellent! You've mastered this content."
        } else if percentage >= 70 {
            return "Good job! You understand most of the material."
        } else if percentage >= 50 {
            return "You're on the right track. Consider reviewing the lesson again."
        } else {
            return "It seems like you might need to review this lesson again."
        }
    }
}

// Quiz question model
struct QuizQuestion {
    let question: String
    let options: [String]
    let correctAnswerIndex: Int?  // Optional for pre-quiz where there's no right answer
    let imageName: String?  // Optional image name to display with the question
}

#Preview {
    NavigationView {
        QuizView(lessonId: "Sample Lesson", quizType: "post-quiz")
    }
}
