import SwiftUI

struct LoginView: View {
    @StateObject private var userManager = UserManager.shared
    @State private var username: String = ""
    @State private var password: String = ""
    @State private var showingRegisterView: Bool = false
    @State private var showingGuestLoginAlert: Bool = false
    @FocusState private var focusedField: Field?
    
    // Focus fields
    enum Field: Hashable {
        case username, password
    }
    
    var body: some View {
        ZStack {
            Color(UIColor.systemBackground)
                .ignoresSafeArea()
            
                VStack(spacing: 20) {
                    Spacer(minLength: 20)
                    
                    // Logo and title
                    Image("funma-logo")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 250, height: 160)
                        .foregroundColor(.blue)
                        .padding(.bottom, 30)

                    // Login form
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Username")
                            .fontWeight(.medium)
                        
                        Button(action: {
                            focusedField = .username
                        }) {
                            ZStack(alignment: .leading) {
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    .background(RoundedRectangle(cornerRadius: 10).fill(Color(.systemBackground)))
                                    
                                TextField("Enter your username", text: $username)
                                    .padding(.horizontal, 15)
                                    .autocapitalization(.none)
                                    .disableAutocorrection(true)
                                    .focused($focusedField, equals: .username)
                                    .submitLabel(.next)
                                    .onSubmit {
                                        focusedField = .password
                                    }
                            }
                            .frame(height: 56)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle()) // Removes button styling but keeps tap functionality
                        .padding(.bottom, 15)
                        
                        Text("Password")
                            .fontWeight(.medium)
                        
                        Button(action: {
                            focusedField = .password
                        }) {
                            ZStack(alignment: .leading) {
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                                    .background(RoundedRectangle(cornerRadius: 10).fill(Color(.systemBackground)))
                                    
                                SecureField("Enter your password", text: $password)
                                    .padding(.horizontal, 15)
                                    .focused($focusedField, equals: .password)
                                    .submitLabel(.done)
                                    .onSubmit {
                                        focusedField = nil
                                        attemptLogin()
                                    }
                            }
                            .frame(height: 56)
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle()) // Removes button styling but keeps tap functionality
                        .padding(.bottom, 25)
                    }
                    .padding(.horizontal, 30)
                    
                    // Login button
                    Button(action: {
                        focusedField = nil
                        attemptLogin()
                    }) {
                        if userManager.isLoading {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        } else {
                            Text("Login")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                    }
                    .disabled(userManager.isLoading)
                .padding(.horizontal, 30)
                

                // Register button
                // Button(action: {
                //     focusedField = nil
                //     showingRegisterView = true
                // }) {
                //     Text("Create Account")
                //         .fontWeight(.semibold)
                //         .foregroundColor(.blue)
                //         .frame(maxWidth: .infinity)
                //         .padding()
                //         .background(Color.clear)
                //         .overlay(
                //             RoundedRectangle(cornerRadius: 10)
                //                 .stroke(Color.blue, lineWidth: 1)
                //         )
                // }
                // .padding(.horizontal, 30)

                // Guest Login button (plain text)
                Button(action: {
                    focusedField = nil
                    showingGuestLoginAlert = true
                }) {
                    Text("Continue as Guest")
                        .foregroundColor(.blue)
                        .padding(.top, 10)
                }
                .buttonStyle(PlainButtonStyle())

                    
                    // Error message
                    if let errorMessage = userManager.errorMessage {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .padding(.top, 10)
                    }
                    
                    Spacer(minLength: 20)
                    
                    // // Demo credentials hint
                    // VStack {
                    //     Text("Demo Credentials")
                    //         .font(.footnote)
                    //         .foregroundColor(.gray)
                        
                    // Text("Student: test, Password: test")
                    //     .font(.footnote)
                    //     .foregroundColor(.gray)
                    // Text("Teacher: teacher, Password: teacher")
                    //         .font(.footnote)
                    //         .foregroundColor(.gray)
                    // }
                    // .padding(.bottom, 20)
                }
                .padding()
            .alert("Continue as Guest?", isPresented: $showingGuestLoginAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Continue") {
                    loginAsGuest()
                }
            } message: {
                Text("You will have limited access to the app as a guest student.")
            }
        }
        .sheet(isPresented: $showingRegisterView) {
            RegisterView()
        }
        .fullScreenCover(isPresented: $userManager.isLoggedIn) {
            ContentView()
                .transition(.opacity.animation(.easeInOut(duration: 0.3)))
        }
    }
    
    private func attemptLogin() {
        guard !username.isEmpty && !password.isEmpty else {
            userManager.errorMessage = "Username and password are required"
            return
        }
        
        Task {
            _ = await userManager.login(username: username, password: password)
        }
    }
    
    private func loginAsGuest() {
        Task {
            _ = await userManager.loginAsGuest()
        }
    }
}

struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView()
    }
}
