import SwiftUI

struct RegisterView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject private var userManager = UserManager.shared
    
    // Form fields
    @State private var username: String = ""
    @State private var password: String = ""
    @State private var confirmPassword: String = ""
    @State private var firstName: String = ""
    @State private var lastName: String = ""
    @State private var email: String = ""
    // Removed phone and gender fields
    @State private var grade: String = "S1"
    
    // Field validation errors
    @State private var usernameError: String? = nil
    @State private var passwordError: String? = nil
    @State private var confirmPasswordError: String? = nil
    @State private var firstNameError: String? = nil
    @State private var lastNameError: String? = nil
    @State private var emailError: String? = nil
    // Removed phoneError
    
    // UI state
    @State private var isRegistering: Bool = false
    @State private var errorMessage: String? = ""
    @State private var showingSuccessAlert: Bool = false
    @State private var showingCountryCodePicker: Bool = false
    @FocusState private var focusedField: Field?
    
    // Country codes
    let countryCodes = [
        (code: "+852", country: "Hong Kong")
    ]
    
    // Focus fields
    enum Field: Hashable {
        case username, password, confirmPassword, firstName, lastName, email // Removed phone
    }
    
    // Removed Gender enum
    
    // Grade options
    let gradeOptions = ["S1", "S2", "S3", "S4", "S5", "S6"]
    
    var body: some View {
        ZStack {
            Color(uiColor: .systemBackground)
                .ignoresSafeArea()
            
            ScrollView {
                VStack(spacing: 20) {
                    // Logo and title
                    Image(systemName: "person.badge.plus")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 80, height: 80)
                        .foregroundColor(.blue)
                        .padding(.top, 20)
                    
                    Text("Create Account")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .padding(.bottom, 20)
                    
                    // Registration form
                    VStack(alignment: .leading, spacing: 8) {
                        // Username
                        Text("Username")
                            .fontWeight(.medium)
                        
                        TextField("Pick a username", text: $username)
                            .textFieldStyle(CustomTextFieldStyle(isError: usernameError != nil))
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                            .padding(.bottom, usernameError == nil ? 15 : 0)
                            .focused($focusedField, equals: .username)
                            .submitLabel(.next)
                            .onSubmit {
                                focusedField = .password
                            }
                        
                        if let error = usernameError {
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.bottom, 5)
                        }
                        
                        // Password
                        Text("Password")
                            .fontWeight(.medium)
                        
                        SecureField("Enter a password", text: $password)
                            .textFieldStyle(CustomTextFieldStyle(isError: passwordError != nil))
                            .padding(.bottom, passwordError == nil ? 15 : 0)
                            .focused($focusedField, equals: .password)
                            .submitLabel(.next)
                            .onSubmit {
                                focusedField = .confirmPassword
                            }
                        
                        if let error = passwordError {
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.bottom, 5)
                        }
                        
                        // Confirm Password
                        Text("Confirm Password")
                            .fontWeight(.medium)
                        
                        SecureField("Confirm your password", text: $confirmPassword)
                            .textFieldStyle(CustomTextFieldStyle(isError: confirmPasswordError != nil))
                            .padding(.bottom, confirmPasswordError == nil ? 15 : 0)
                            .focused($focusedField, equals: .confirmPassword)
                            .submitLabel(.next)
                            .onSubmit {
                                focusedField = .firstName
                            }
                        
                        if let error = confirmPasswordError {
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.bottom, 5)
                        }
                        
                        // First Name
                        Text("First Name")
                            .fontWeight(.medium)
                        
                        TextField("e.g. Tai Man", text: $firstName)
                            .textFieldStyle(CustomTextFieldStyle(isError: firstNameError != nil))
                            .autocapitalization(.words)
                            .padding(.bottom, firstNameError == nil ? 15 : 0)
                            .focused($focusedField, equals: .firstName)
                            .submitLabel(.next)
                            .onSubmit {
                                focusedField = .lastName
                            }
                        
                        if let error = firstNameError {
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.bottom, 5)
                        }
                        
                        // Last Name
                        Text("Last Name")
                            .fontWeight(.medium)
                        
                        TextField("e.g. Chan", text: $lastName)
                            .textFieldStyle(CustomTextFieldStyle(isError: lastNameError != nil))
                            .autocapitalization(.words)
                            .padding(.bottom, lastNameError == nil ? 15 : 0)
                            .focused($focusedField, equals: .lastName)
                            .submitLabel(.next)
                            .onSubmit {
                                focusedField = .email
                            }
                        
                        if let error = lastNameError {
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.bottom, 5)
                        }
                        
                        // Email
                        Text("Email")
                            .fontWeight(.medium)
                        
                        TextField("Enter your email", text: $email)
                            .textFieldStyle(CustomTextFieldStyle(isError: emailError != nil))
                            .keyboardType(.emailAddress)
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                            .padding(.bottom, emailError == nil ? 15 : 0)
                            .focused($focusedField, equals: .email)
                            .submitLabel(.next)
                            .onSubmit {
                                focusedField = nil
                            }
                        
                        if let error = emailError {
                            Text(error)
                                .font(.caption)
                                .foregroundColor(.red)
                                .padding(.bottom, 5)
                        }
                        // Removed phone and gender fields
                        // Grade
                        Text("Grade")
                            .fontWeight(.medium)
                        
                        Picker("Select your grade", selection: $grade) {
                            ForEach(gradeOptions, id: \.self) { grade in
                                Text(grade).tag(grade)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())
                        .padding(.bottom, 15)
                    }
                    .padding(.horizontal, 30)
                    
                    // Register button
                    Button(action: {
                        focusedField = nil
                        attemptRegistration()
                    }) {
                        if isRegistering {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        } else {
                            Text("Register")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                    }
                    .disabled(isRegistering)
                    .padding(.horizontal, 30)
                    
                    // General error message
                    if let errorMessage = errorMessage, !errorMessage.isEmpty {
                        Text(errorMessage)
                            .foregroundColor(.red)
                            .padding(.top, 10)
                    }
                    
                    // Add some padding at the bottom
                    Spacer(minLength: 40)
                }
                .padding()
            }
            .simultaneousGesture(
                TapGesture().onEnded { _ in
                    // Only dismiss keyboard if a text field is focused
                    if focusedField != nil {
                        focusedField = nil
                    }
                }
            )
        }
        .alert(isPresented: $showingSuccessAlert) {
            Alert(
                title: Text("Registration Successful"),
                message: Text("Your account has been created successfully. You can now log in."),
                dismissButton: .default(Text("OK")) {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
    
    // Validation functions
    private func validateUsername() {
        if username.isEmpty {
            usernameError = "Username is required"
        } else {
            usernameError = nil
        }
    }
    
    private func validatePassword() {
        if password.isEmpty {
            passwordError = "Password is required"
        } else {
            passwordError = nil
        }
    }
    
    private func validateConfirmPassword() {
        if confirmPassword.isEmpty {
            confirmPasswordError = "Please confirm your password"
        } else if password != confirmPassword {
            confirmPasswordError = "Passwords do not match"
        } else {
            confirmPasswordError = nil
        }
    }
    
    private func validateFirstName() {
        if firstName.isEmpty {
            firstNameError = "First name is required"
        } else {
            firstNameError = nil
        }
    }
    
    private func validateLastName() {
        if lastName.isEmpty {
            lastNameError = "Last name is required"
        } else {
            lastNameError = nil
        }
    }
    
    private func validateEmail() {
        if email.isEmpty {
            emailError = "Email is required"
        } else if !isValidEmail(email) {
            emailError = "Please enter a valid email address"
        } else {
            emailError = nil
        }
    }
    
    // Removed validatePhone()
    
    private func validateAll() -> Bool {
        validateUsername()
        validatePassword()
        validateConfirmPassword()
        validateFirstName()
        validateLastName()
        validateEmail()
        // Removed validatePhone()
        return usernameError == nil && 
               passwordError == nil && 
               confirmPasswordError == nil && 
               firstNameError == nil && 
               lastNameError == nil && 
               emailError == nil
    }
    
    private func attemptRegistration() {
        // Reset general error message
        errorMessage = ""
        
        // Validate all fields
        if !validateAll() {
            return
        }
        
        // Set registering state
        isRegistering = true
        
        // Call register API
        Task {
            let result = await userManager.register(
                username: username,
                password: password,
                firstName: firstName,
                lastName: lastName,
                email: email,
                phone: "",
                gender: "",
                grade: grade
            )
            
            // Update UI on main thread
            await MainActor.run {
                isRegistering = false
                
                switch result {
                case .success:
                    showingSuccessAlert = true
                case .failure(let error):
                    errorMessage = error.localizedDescription
                }
            }
        }
    }
    
    // Email validation
    private func isValidEmail(_ email: String) -> Bool {
        let emailRegEx = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPred = NSPredicate(format:"SELF MATCHES %@", emailRegEx)
        return emailPred.evaluate(with: email)
    }
}

// Custom text field style with error state
struct CustomTextFieldStyle: TextFieldStyle {
    var isError: Bool
    
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(10)
            .background(
                RoundedRectangle(cornerRadius: 5)
                    .stroke(isError ? Color.red : Color(uiColor: .systemGray4), lineWidth: 1)
                    .background(Color(uiColor: .systemBackground))
            )
    }
}

// String extension for trimming whitespace
extension String {
    func trim() -> String {
        return self.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    // Phone number validation - checks if string is exactly 8 digits
    func isValidPhoneNumber() -> Bool {
        let phoneRegex = "^[0-9]{8}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        return phonePredicate.evaluate(with: self)
    }
}

struct RegisterView_Previews: PreviewProvider {
    static var previews: some View {
        RegisterView()
    }
} 