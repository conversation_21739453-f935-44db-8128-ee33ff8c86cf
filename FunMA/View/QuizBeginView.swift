//
//  QuizBeginView.swift
//  Luminous Education
//
//  Created by <PERSON> on 1/8/2024.
//

import SwiftUI

struct QuizBeginView: View {
    var body: some View {
        VStack {
            Spacer()
            // Title
            Text("Pre-Quiz")
                .font(.largeTitle)
                .fontWeight(.bold)
                .padding(.top, 50)
            Spacer()
            // Time Limit
            Text("Time limit:")
                .font(.title)
//                .padding(.top, 20)
                .padding(.bottom, 10)
            Text("5 minutes")
                .font(.title2)
                .padding(.bottom, 30)

            // Details
            Text("Details:")
                .font(.title)
                .padding(.bottom, 10)
            VStack(alignment: .leading) {
                Text("5 MC Questions")
                Text("2 Short Questions")
                Text("1 Long Question")
            }
            .font(.title3)
            Spacer()
            
            // Begin Button
            Button(action: {
                // Action for Begin button
            }) {
                Text("Begin")
                    .font(.headline)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
            .padding(.horizontal, 20)

            Spacer()
        }
        .padding()
    }
}

#Preview {
    QuizBeginView()
}
