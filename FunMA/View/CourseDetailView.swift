import SwiftUI

// Course detail view - Extracted into separate file for better organization
struct CourseDetailView: View {
    let course: Course
    @Binding var isPurchasing: Bool
    let onPurchase: () -> Void
    @Binding var isLoadingCourseDetail: Bool
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject private var marketplaceViewModel: MarketplaceViewModel
    
    // Get appropriate SF Symbol based on course category
    private var categorySymbol: String {
        switch course.category.lowercased() {
        case "mathematics":
            return "function"
        case "english":
            return "text.book.closed"
        case "science":
            return "atom"
        case "chinese":
            return "character"
        default:
            return "book.fill"
        }
    }
    
    var body: some View {
        ZStack(alignment: .bottom) {
            if isLoadingCourseDetail {
                VStack {
                    Spacer()
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle())
                        .scaleEffect(1.5)
                    Text("Loading course details...")
                        .padding()
                    Spacer()
                }
            } else {
                ScrollView {
                    VStack(alignment: .leading, spacing: 20) {
                        // Header image
                        Image(systemName: categorySymbol)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(height: 200)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue.opacity(0.1))
                        
                        // Course info
                        VStack(alignment: .leading, spacing: 16) {
                            // Title and price
                            HStack {
                                Text(course.title)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                
                                Spacer()
                                
                                Text("\(course.credit) Credits")
                                    .font(.title3)
                                    .fontWeight(.bold)
                                    .foregroundColor(.blue)
                            }
                            .padding(.horizontal)
                            
                            
                            // Level and lesson count
                            HStack {
                                HStack {
                                    Image(systemName: "chart.bar.fill")
                                        .foregroundColor(.gray)
                                    
                                    Text(course.level.rawValue)
                                        .font(.subheadline)
                                }
                                
                                Spacer()
                                
                                HStack {
                                    Image(systemName: "book.fill")
                                        .foregroundColor(.gray)
                                    
                                    Text("\(course.totalLessons) lessons")
                                        .font(.subheadline)
                                }
                            }
                            .padding(.horizontal)
                            
                            Divider()
                                .padding(.horizontal)
                            
                            // Description
                            VStack(alignment: .leading, spacing: 8) {
                                Text("About this course")
                                    .font(.headline)
                                
                                Text(course.description)
                                    .font(.body)
                                    .foregroundColor(.secondary)
                            }
                            .padding(.horizontal)
                            
                            Divider()
                                .padding(.horizontal)
                            
                            // What you'll learn
                            VStack(alignment: .leading, spacing: 8) {
                                Text("What you'll learn")
                                    .font(.headline)
                                
                                VStack(alignment: .leading, spacing: 8) {
                                    BulletPoint(text: "Comprehensive understanding of \(course.category) concepts")
                                    BulletPoint(text: "Practical skills for solving complex problems")
                                    BulletPoint(text: "Exam techniques and strategies")
                                    BulletPoint(text: "Real-world applications of theoretical knowledge")
                                }
                            }
                            .padding(.horizontal)
                            
                            // Add padding at the bottom to ensure content can scroll past the purchase button
                            Spacer(minLength: 90)
                        }
                    }
                }
                
                // Purchase button - positioned with ZStack
                VStack {
                    Button(action: onPurchase) {
                        if isPurchasing {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        } else if course.isEnrolled {
                            Text("Already Enrolled")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.green)
                                .cornerRadius(10)
                        } else {
                            Text("Enroll Now - \(course.credit) Credits")
                                .fontWeight(.semibold)
                                .foregroundColor(.white)
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(Color.blue)
                                .cornerRadius(10)
                        }
                    }
                    .disabled(isPurchasing || course.isEnrolled)
                    .padding()
                    .background(
                        Rectangle()
                            .fill(Color(UIColor.systemBackground))
                            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: -2)
                    )
                }
            }
        }
        .edgesIgnoringSafeArea(.bottom) // Let the content extend to the bottom edge
        .navigationBarTitle("", displayMode: .inline)
        .navigationBarItems(leading: Button(action: {
            presentationMode.wrappedValue.dismiss()
        }) {
            Image(systemName: "xmark")
                .foregroundColor(.primary)
        })
    }
}

// Bullet point component
struct BulletPoint: View {
    let text: String
    
    var body: some View {
        HStack(alignment: .top, spacing: 10) {
            Image(systemName: "checkmark.circle.fill")
                .foregroundColor(.green)
                .font(.system(size: 16))
            
            Text(text)
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
    }
}

// Preview provider for CourseDetailView
struct CourseDetailView_Previews: PreviewProvider {
    static var previews: some View {
        // Create a sample course for the preview
        let sampleCourse = Course(
            courseId: "SAMPLE001", 
            name: "Sample Course", 
            description: "This is a sample course description for preview purposes.", 
            difficulty: 2, 
            totalLessons: 12, 
            credit: 30, 
            image: "book.fill", 
            lessons: []
        )
        
        // Preview the detail view with the sample course
        CourseDetailView(
            course: sampleCourse, 
            isPurchasing: .constant(false), 
            onPurchase: {}, 
            isLoadingCourseDetail: .constant(false)
        )
        .environmentObject(MarketplaceViewModel())
    }
} 