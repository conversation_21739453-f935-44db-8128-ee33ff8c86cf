import SwiftUI

struct LaTeXTestView: View {
    @State private var testResults: [String] = []
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                Text("LaTeX Number Processing Test")
                    .font(.title2)
                    .fontWeight(.bold)
                
                VStack {
                    HStack {
                        Button("Run Test") {
                            runTest()
                        }
                        .buttonStyle(.borderedProminent)

                        Button("Test Trapezium") {
                            testTrapeziumQuestion()
                            // Also add results to the display
                            testResults.append("")
                            testResults.append("=== Trapezium Test Results ===")
                            let testCase = "What is the area of a trapezium with parallel sides measuring $8$ cm and $12$ cm, and a perpendicular height of $5$ cm?"
                            let processed = preprocessTextForRendering(testCase)
                            let hasLaTeX = containsLaTeX(processed)
                            testResults.append("Original: \(testCase)")
                            testResults.append("Processed: \(processed)")
                            testResults.append("Contains LaTeX: \(hasLaTeX)")

                            if processed == "What is the area of a trapezium with parallel sides measuring 8 cm and 12 cm, and a perpendicular height of 5 cm?" && !hasLaTeX {
                                testResults.append("✅ SUCCESS: Fix is working!")
                            } else {
                                testResults.append("❌ FAILED: Fix not working")
                            }
                        }
                        .buttonStyle(.bordered)
                    }

                    Button("Debug Issue") {
                        debugTrapeziumIssue()
                    }
                    .buttonStyle(.borderedProminent)
                    .foregroundColor(.red)
                }
                
                ScrollView {
                    VStack(alignment: .leading, spacing: 8) {
                        ForEach(testResults, id: \.self) { result in
                            Text(result)
                                .font(.system(.caption, design: .monospaced))
                                .foregroundColor(result.contains("✅") ? .green : 
                                               result.contains("❌") ? .red : .primary)
                        }
                    }
                    .padding()
                }
                .background(Color(.systemGray6))
                .cornerRadius(8)
                
                Spacer()
                
                // Test the actual rendering
                VStack(alignment: .leading, spacing: 12) {
                    Text("Rendering Test:")
                        .font(.headline)
                    
                    Text("Before fix (should show $8$ and $12$ as literal text):")
                        .font(.caption)
                        .foregroundColor(.gray)
                    
                    Text("What is the area of a trapezium with parallel sides measuring $8$ cm and $12$ cm?")
                        .padding()
                        .background(Color(.systemGray5))
                        .cornerRadius(8)
                    
                    Text("After fix (should show 8 and 12 as plain numbers):")
                        .font(.caption)
                        .foregroundColor(.gray)

                    QuestionTextRenderer(
                        text: "What is the area of a trapezium with parallel sides measuring $8$ cm and $12$ cm?",
                        fontSize: 16
                    )
                    .padding()
                    .background(Color(.systemGray5))
                    .cornerRadius(8)

                    Text("Test with complex LaTeX (should still render as LaTeX):")
                        .font(.caption)
                        .foregroundColor(.gray)

                    QuestionTextRenderer(
                        text: "Solve for $x$: $3x + 2y = 10$ where $y = \\frac{1}{2}$",
                        fontSize: 16
                    )
                    .padding()
                    .background(Color(.systemGray5))
                    .cornerRadius(8)
                }
            }
            .padding()
            .navigationTitle("LaTeX Test")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func runTest() {
        testResults.clear()

        let testCase = "What is the area of a trapezium with parallel sides measuring $8$ cm and $12$ cm, and a perpendicular height of $5$ cm?"

        // Test LaTeX processor validation
        let processorResults = LaTeXProcessor.shared.testSimpleNumberProcessing(testCase)
        testResults.append(contentsOf: processorResults)

        testResults.append("")
        testResults.append("=== Testing Text Preprocessing ===")

        // Test the preprocessing that should convert $8$ and $12$ to plain text
        let originalText = testCase
        testResults.append("Original: \(originalText)")

        // Test the new logic: preprocess FIRST, then check LaTeX
        let processedText = preprocessTextForRendering(originalText)
        testResults.append("Processed: \(processedText)")

        let hasLaTeXAfterProcessing = containsLaTeX(processedText)
        testResults.append("Contains LaTeX after processing: \(hasLaTeXAfterProcessing)")

        // Check if the numbers were converted
        let still8 = processedText.contains("$8$")
        let still12 = processedText.contains("$12$")
        let still5 = processedText.contains("$5$")

        if !still8 && !still12 && !still5 {
            testResults.append("✅ SUCCESS: $8$, $12$, and $5$ converted to plain text")
        } else {
            testResults.append("❌ FAILED: Some numbers still in LaTeX format")
            if still8 { testResults.append("  - $8$ not converted") }
            if still12 { testResults.append("  - $12$ not converted") }
            if still5 { testResults.append("  - $5$ not converted") }
        }

        // Test that the processed text should NOT be detected as containing LaTeX
        if !hasLaTeXAfterProcessing {
            testResults.append("✅ SUCCESS: Processed text correctly identified as plain text")
        } else {
            testResults.append("❌ FAILED: Processed text still detected as containing LaTeX")
        }
    }
}

// Helper functions for testing (simplified versions)
private func containsLaTeX(_ text: String) -> Bool {
    // Check for actual LaTeX commands and complex expressions
    let hasInlineMath = text.range(of: #"\$[^$]*\\[a-zA-Z]+[^$]*\$"#, options: .regularExpression) != nil
    let hasDisplayMath = text.range(of: #"\$\$[^$]+\$\$"#, options: .regularExpression) != nil
    
    // Check for mathematical expressions with variables
    let hasMathExpressions =
        text.range(of: #"\$[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
        text.range(of: #"\$\d+[a-zA-Z]+\$"#, options: .regularExpression) != nil ||
        text.range(of: #"\$[^$]*[a-zA-Z]+[^$]*[+\-*/][^$]*\$"#, options: .regularExpression) != nil
    
    return hasInlineMath || hasDisplayMath || hasMathExpressions
}

private func preprocessTextForRendering(_ text: String) -> String {
    var result = text
    
    // Convert pure numbers like $8$ and $12$ to plain text
    let pureNumberPattern = #"\$(-?\d+(?:\.\d+)?)\$"#
    
    // Always convert pure numbers to plain text (removed mathematical context restriction)
    if !result.contains("(") && !result.contains(",") && !result.contains(")") {
        result = result.replacingOccurrences(
            of: pureNumberPattern,
            with: "$1",
            options: .regularExpression
        )
    }
    
    return result
}

#Preview {
    LaTeXTestView()
}
