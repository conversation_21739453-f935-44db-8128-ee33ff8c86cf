import SwiftUI

struct CreditAdjustmentView: View {
    @StateObject private var userManager = UserManager.shared
    @Environment(\.presentationMode) var presentationMode
    
    // Form fields
    @State private var searchText: String = ""
    @State private var selectedUser: User?
    @State private var creditAmount: Int = 0
    @State private var description: String = ""
    @State private var searchResult: User?
    
    // UI state
    @State private var isSearching = false
    @State private var isAdjusting = false
    @State private var showingSuccessAlert = false
    @State private var showingErrorAlert = false
    @State private var alertMessage = ""
    @State private var successMessage = ""
    
    // Focus management
    @FocusState private var focusedField: Field?
    
    enum Field {
        case search, amount, description
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.clear
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        headerSection
                        
                        // Developer access check
                        if !userManager.currentUser.isDeveloper {
                            accessDeniedSection
                        } else {
                            // Main form
                            formSection
                            
                            // Search result
                            if searchResult != nil {
                                searchResultSection
                            }
                            
                            // Selected user info
                            if selectedUser != nil {
                                selectedUserSection
                            }
                            
                            // Adjustment form
                            if selectedUser != nil {
                                adjustmentFormSection
                            }
                            
                            // Adjust button
                            if selectedUser != nil {
                                adjustButtonSection
                            }
                        }
                        
                        Spacer(minLength: 20)
                    }
                    .padding()
                }
            }
            .navigationTitle("Credit Adjustment")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(isAdjusting)
                }
            }
            .alert("Success", isPresented: $showingSuccessAlert) {
                Button("OK") {
                    // Reset form after successful adjustment
                    resetForm()
                }
            } message: {
                Text(successMessage)
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(alertMessage)
            }
        }
    }
    
    // MARK: - View Components
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "creditcard.and.123")
                .font(.system(size: 50))
                .foregroundColor(.blue)
            
            Text("Credit Adjustment")
                .font(.title)
                .fontWeight(.bold)
                .foregroundColor(.primary)
            
            Text("Adjust user credits as a developer")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding(.vertical)
    }
    
    private var accessDeniedSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "lock.fill")
                .font(.system(size: 40))
                .foregroundColor(.red)
            
            Text("Access Denied")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.red)
            
            Text("Only developers can access credit adjustment functionality.")
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color.red.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var formSection: some View {
        VStack(spacing: 16) {
            Text("Search Users")
                .font(.headline)
                .foregroundColor(.primary)
            
            // Search field
            VStack(alignment: .leading, spacing: 8) {
                Text("Search by username or email")
                    .font(.subheadline)
                    .foregroundColor(.primary)

                HStack {
                    TextField("Enter username or email", text: $searchText)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .focused($focusedField, equals: .search)
                        .onSubmit {
                            searchUser()
                        }
                        .autocorrectionDisabled(true)
                        .textInputAutocapitalization(.never)

                    Button("Search") {
                        searchUser()
                    }
                    .buttonStyle(.bordered)
                    .disabled(searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || isSearching)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var searchResultSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Search Result")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Text("User found")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if let user = searchResult {
                UserSearchCard(
                    user: user,
                    isSelected: selectedUser?.id == user.id,
                    onSelect: {
                        selectedUser = user
                        searchResult = nil // Clear search result after selection
                        focusedField = .amount
                    }
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var selectedUserSection: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Selected User")
                    .font(.headline)
                    .foregroundColor(.primary)
                Spacer()
                Button("Change User") {
                    selectedUser = nil
                    searchResult = nil
                    searchText = ""
                    focusedField = .search
                }
                .font(.caption)
                .buttonStyle(.bordered)
            }
            
            if let user = selectedUser {
                SelectedUserCard(user: user)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var adjustmentFormSection: some View {
        VStack(spacing: 16) {
            Text("Credit Adjustment")
                .font(.headline)
                .foregroundColor(.primary)
            
            // Amount field
            VStack(alignment: .leading, spacing: 8) {
                Text("Credit Amount")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                HStack {
                    TextField("Enter amount (+ or -)", value: $creditAmount, format: .number)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .focused($focusedField, equals: .amount)
                        .keyboardType(.numberPad)
                        .autocorrectionDisabled(true)
                        .textInputAutocapitalization(.never)
                    
                    Stepper("", value: $creditAmount, in: -10000...10000, step: 10)
                        .labelsHidden()
                }
                
                Text("Use positive numbers to add credits, negative to deduct")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            // Description field
            VStack(alignment: .leading, spacing: 8) {
                Text("Description (Optional)")
                    .font(.subheadline)
                    .foregroundColor(.primary)
                
                TextField("Reason for adjustment", text: $description)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($focusedField, equals: .description)
                    .autocorrectionDisabled(true)
                    .textInputAutocapitalization(.never)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var adjustButtonSection: some View {
        Button(action: adjustCredits) {
            HStack {
                if isAdjusting {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                
                Text(isAdjusting ? "Adjusting..." : "Adjust Credits")
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(canAdjustCredits ? Color.blue : Color.gray)
            .cornerRadius(10)
        }
        .disabled(!canAdjustCredits || isAdjusting)
    }
    
    // MARK: - Computed Properties
    
    private var canAdjustCredits: Bool {
        selectedUser != nil && 
        creditAmount != 0 && 
        userManager.currentUser.isDeveloper
    }
    
    // MARK: - Actions
    
    private func searchUser() {
        let trimmedSearch = searchText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedSearch.isEmpty else { return }

        isSearching = true
        focusedField = nil

        Task {
            let result = await userManager.searchUser(by: trimmedSearch)

            await MainActor.run {
                isSearching = false

                switch result {
                case .success(let user):
                    if let user = user {
                        searchResult = user
                    } else {
                        alertMessage = "No user found with username or email: \(trimmedSearch)"
                        showingErrorAlert = true
                        searchResult = nil
                    }
                case .failure(let error):
                    alertMessage = "Failed to search user: \(error.localizedDescription)"
                    showingErrorAlert = true
                    searchResult = nil
                }
            }
        }
    }
    
    private func adjustCredits() {
        guard let user = selectedUser, canAdjustCredits else { return }
        
        isAdjusting = true
        focusedField = nil
        
        Task {
            let result = await userManager.adjustUserCredit(
                userId: user.id,
                amount: creditAmount,
                description: description.isEmpty ? nil : description
            )
            
            await MainActor.run {
                isAdjusting = false
                
                switch result {
                case .success(let response):
                    let newBalance = response.actualBalance ?? (user.credit + creditAmount)
                    successMessage = "Successfully adjusted \(user.username)'s credits by \(creditAmount). New balance: \(newBalance)"
                    showingSuccessAlert = true
                case .failure(let error):
                    alertMessage = "Failed to adjust credits: \(error.localizedDescription)"
                    showingErrorAlert = true
                }
            }
        }
    }
    
    private func resetForm() {
        selectedUser = nil
        searchResult = nil
        searchText = ""
        creditAmount = 0
        description = ""
        focusedField = .search
    }

}

// MARK: - Supporting Views

struct UserSearchCard: View {
    let user: User
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack(spacing: 12) {
                Circle()
                    .fill(roleColor.opacity(0.2))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(roleColor)
                            .font(.caption)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(user.username)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    if !user.name.isEmpty {
                        Text(user.name)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text(user.role)
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(roleColor.opacity(0.2))
                            .cornerRadius(4)
                            .foregroundColor(roleColor)
                        
                        Text("\(user.credit) credits")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                } else {
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            .padding()
            .background(isSelected ? Color.blue.opacity(0.1) : Color(.systemBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.blue : Color(.systemGray4), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var roleColor: Color {
        switch user.role {
        case "Teacher": return .green
        case "Student": return .blue
        case "Developer": return .purple
        default: return .gray
        }
    }
}

struct SelectedUserCard: View {
    let user: User
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Circle()
                    .fill(roleColor.opacity(0.2))
                    .frame(width: 50, height: 50)
                    .overlay(
                        Image(systemName: "person.fill")
                            .foregroundColor(roleColor)
                            .font(.title3)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(user.username)
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                    
                    if !user.name.isEmpty {
                        Text(user.name)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    if let email = user.email {
                        Text(email)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(user.role)
                        .font(.caption)
                        .fontWeight(.medium)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(roleColor.opacity(0.2))
                        .cornerRadius(6)
                        .foregroundColor(roleColor)
                    
                    Text("\(user.credit) credits")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
    
    private var roleColor: Color {
        switch user.role {
        case "Teacher": return .green
        case "Student": return .blue
        case "Developer": return .purple
        default: return .gray
        }
    }
}

#Preview {
    CreditAdjustmentView()
}
