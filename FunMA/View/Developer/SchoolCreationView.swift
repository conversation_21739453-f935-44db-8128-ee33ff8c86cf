import SwiftUI

struct SchoolCreationView: View {
    @StateObject private var schoolViewModel = SchoolViewModel()
    @StateObject private var userManager = UserManager.shared
    @Environment(\.presentationMode) var presentationMode
    
    // Form fields
    @State private var schoolName: String = ""
    @State private var schoolAddress: String = ""
    @State private var selectedRegion: SchoolRegion = .hongKong

    // UI state
    @State private var showingSuccessAlert: Bool = false
    @State private var showingErrorAlert: Bool = false

    // Focus management
    @FocusState private var focusedField: Field?

    enum Field: Hashable {
        case schoolName, schoolAddress
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.clear
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        headerSection
                        
                        // Developer access check
                        if !userManager.currentUser.isDeveloper {
                            accessDeniedSection
                        } else {
                            // Main form
                            formSection
                            
                            // Existing schools list
                            existingSchoolsSection
                            
                            // Create button
                            createButtonSection
                        }
                        
                        Spacer(minLength: 20)
                    }
                    .padding()
                }
            }
            .navigationTitle("School Management")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(schoolViewModel.isLoading)
                }
            }
            .alert("Success", isPresented: $showingSuccessAlert) {
                Button("OK") {
                    schoolName = ""
                    schoolAddress = ""
                    selectedRegion = .hongKong
                    focusedField = nil
                }
            } message: {
                Text("School created successfully!")
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(schoolViewModel.errorMessage ?? "Unknown error occurred")
            }
        }
        .task {
            await schoolViewModel.fetchSchools()
        }
    }
    
    // MARK: - View Sections
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "building.2.fill")
                .font(.system(size: 50))
                .foregroundColor(.blue)
            
            Text("School Management")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Create and manage schools for user account assignment")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
    }
    
    private var accessDeniedSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 40))
                .foregroundColor(.red)
            
            Text("Access Denied")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("This feature is only available to users with Developer role.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var formSection: some View {
        VStack(spacing: 20) {
            // School Name
            VStack(alignment: .leading, spacing: 8) {
                Text("School Name")
                    .font(.headline)
                    .foregroundColor(.primary)

                TextField("Enter school name", text: $schoolName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($focusedField, equals: .schoolName)

                Text("Enter a unique name for the school")
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            // School Address
            VStack(alignment: .leading, spacing: 8) {
                Text("School Address")
                    .font(.headline)
                    .foregroundColor(.primary)

                TextField("Enter school address", text: $schoolAddress)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($focusedField, equals: .schoolAddress)

                Text("Enter the complete address of the school")
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            // Region Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Region")
                    .font(.headline)
                    .foregroundColor(.primary)

                Menu {
                    ForEach(SchoolRegion.allCases) { region in
                        Button(region.rawValue) {
                            selectedRegion = region
                        }
                    }
                } label: {
                    HStack {
                        Text(selectedRegion.rawValue)
                            .foregroundColor(.primary)
                        Spacer()
                        Image(systemName: "chevron.down")
                            .foregroundColor(.gray)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                }

                Text("Select the region where the school is located")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var existingSchoolsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Existing Schools")
                .font(.headline)
                .foregroundColor(.primary)
            
            if schoolViewModel.isLoading {
                ProgressView("Loading schools...")
                    .frame(maxWidth: .infinity)
                    .padding()
            } else if schoolViewModel.schools.isEmpty {
                Text("No schools found")
                    .font(.body)
                    .foregroundColor(.gray)
                    .frame(maxWidth: .infinity)
                    .padding()
            } else {
                LazyVStack(spacing: 8) {
                    ForEach(schoolViewModel.schools) { school in
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "building.2")
                                    .foregroundColor(.blue)

                                Text(school.schoolName)
                                    .font(.body)
                                    .fontWeight(.semibold)

                                Spacer()

                                if let region = school.region {
                                    Text(region)
                                        .font(.caption)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(Color.blue.opacity(0.1))
                                        .cornerRadius(4)
                                        .foregroundColor(.blue)
                                } else {
                                    Text("No Region")
                                        .font(.caption)
                                        .padding(.horizontal, 8)
                                        .padding(.vertical, 4)
                                        .background(Color.gray.opacity(0.1))
                                        .cornerRadius(4)
                                        .foregroundColor(.gray)
                                }
                            }

                            if let address = school.address {
                                Text(address)
                                    .font(.caption)
                                    .foregroundColor(.gray)
                            } else {
                                Text("No address available")
                                    .font(.caption)
                                    .foregroundColor(.gray)
                                    .italic()
                            }

                            Text("ID: \(school.id)")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                        .padding()
                        .background(Color.white)
                        .cornerRadius(8)
                    }
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var createButtonSection: some View {
        Button(action: createSchool) {
            HStack {
                if schoolViewModel.isLoading {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }

                Text(schoolViewModel.isLoading ? "Creating..." : "Create School")
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(canCreateSchool ? Color.blue : Color.gray)
            .cornerRadius(10)
        }
        .disabled(!canCreateSchool || schoolViewModel.isLoading)
    }
    
    // MARK: - Computed Properties
    
    private var canCreateSchool: Bool {
        !schoolName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !schoolAddress.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        userManager.currentUser.isDeveloper
    }
    
    // MARK: - Actions
    
    private func createSchool() {
        guard canCreateSchool else { return }

        focusedField = nil

        Task {
            let success = await schoolViewModel.createSchool(
                name: schoolName,
                address: schoolAddress,
                region: selectedRegion.rawValue
            )

            await MainActor.run {
                if success {
                    showingSuccessAlert = true
                } else {
                    showingErrorAlert = true
                }
            }
        }
    }
}

#Preview {
    SchoolCreationView()
}
