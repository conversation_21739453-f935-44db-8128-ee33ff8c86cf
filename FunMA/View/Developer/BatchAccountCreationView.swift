import SwiftUI

struct BatchAccountCreationView: View {
    @StateObject private var userManager = UserManager.shared
    @StateObject private var schoolViewModel = SchoolViewModel()
    @Environment(\.presentationMode) var presentationMode

    // Form fields
    @State private var organization: String = ""
    @State private var password: String = "123456"
    @State private var teacherCount: Int = 0
    @State private var studentCount: Int = 0
    @State private var teacherInitialCredit: Int = 500
    @State private var studentInitialCredit: Int = 50
    @State private var selectedSchool: School?
    
    // UI state
    @State private var isCreating: Bool = false
    @State private var showingSuccessAlert: Bool = false
    @State private var showingErrorAlert: Bool = false
    @State private var errorMessage: String = ""
    @State private var successMessage: String = ""
    @State private var creationProgress: Double = 0.0
    @State private var currentlyCreating: String = ""
    
    // Focus management
    @FocusState private var focusedField: Field?
    
    enum Field: Hashable {
        case organization, password, teacherCount, studentCount, teacherInitialCredit, studentInitialCredit
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                Color.clear
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        headerSection
                        
                        // Developer access check
                        if !userManager.currentUser.isDeveloper {
                            accessDeniedSection
                        } else {
                            // Main form
                            formSection
                            
                            // Progress section (shown during creation)
                            if isCreating {
                                progressSection
                            }
                            
                            // Create button
                            createButtonSection
                        }
                        
                        Spacer(minLength: 20)
                    }
                    .padding()
                }
            }
            .navigationTitle("Batch Account Creation")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        presentationMode.wrappedValue.dismiss()
                    }
                    .disabled(isCreating)
                }
            }
            .alert("Success", isPresented: $showingSuccessAlert) {
                Button("OK") {
                    presentationMode.wrappedValue.dismiss()
                }
            } message: {
                Text(successMessage)
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK") { }
            } message: {
                Text(errorMessage)
            }
        }
        .task {
            await schoolViewModel.fetchSchools()
        }
    }
    
    // MARK: - View Sections
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "person.3.fill")
                .font(.system(size: 50))
                .foregroundColor(.blue)
            
            Text("Batch Account Creation")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Create multiple teacher and student accounts for an organization with customizable settings")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding(.top, 20)
    }
    
    private var accessDeniedSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 40))
                .foregroundColor(.red)
            
            Text("Access Denied")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("This feature is only available to users with Developer role.")
                .font(.body)
                .foregroundColor(.gray)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var formSection: some View {
        VStack(spacing: 20) {
            // Organization field
            VStack(alignment: .leading, spacing: 8) {
                Text("Organization")
                    .font(.headline)
                    .foregroundColor(.primary)

                TextField("Enter organization name (e.g., 'SchoolABC')", text: $organization)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($focusedField, equals: .organization)

                Text("Used as username prefix and last name. Emails <NAME_EMAIL>")
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            // School selection
            VStack(alignment: .leading, spacing: 8) {
                Text("School")
                    .font(.headline)
                    .foregroundColor(.primary)

                if schoolViewModel.isLoading {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Loading schools...")
                            .font(.body)
                            .foregroundColor(.gray)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                } else if schoolViewModel.schools.isEmpty {
                    VStack(spacing: 8) {
                        Text("No schools available")
                            .font(.body)
                            .foregroundColor(.gray)

                        Button("Create School First") {
                            // This would open school creation view
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                } else {
                    Menu {
                        Button("Select School") {
                            selectedSchool = nil
                        }

                        ForEach(schoolViewModel.schools) { school in
                            Button(school.schoolName) {
                                selectedSchool = school
                            }
                        }
                    } label: {
                        HStack {
                            Text(selectedSchool?.schoolName ?? "Select School")
                                .foregroundColor(selectedSchool != nil ? .primary : .gray)
                            Spacer()
                            Image(systemName: "chevron.down")
                                .foregroundColor(.gray)
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                }

                Text("Select the school to associate with the created accounts")
                    .font(.caption)
                    .foregroundColor(.gray)
            }

            // Password field
            VStack(alignment: .leading, spacing: 8) {
                Text("Password")
                    .font(.headline)
                    .foregroundColor(.primary)
                
                TextField("Enter simple password", text: $password)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .focused($focusedField, equals: .password)
                
                Text("All accounts will use this password. Keep it simple for easy distribution.")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            // Account counts
            VStack(spacing: 16) {
                // Teacher count
                VStack(alignment: .leading, spacing: 8) {
                    Text("Number of Teacher Accounts")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        TextField("0", value: $teacherCount, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .focused($focusedField, equals: .teacherCount)
                        
                        Stepper("", value: $teacherCount, in: 0...50)
                            .labelsHidden()
                    }
                }
                
                // Student count
                VStack(alignment: .leading, spacing: 8) {
                    Text("Number of Student Accounts")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    HStack {
                        TextField("0", value: $studentCount, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .focused($focusedField, equals: .studentCount)
                        
                        Stepper("", value: $studentCount, in: 0...100)
                            .labelsHidden()
                    }
                }
            }

            // Credit settings
            VStack(spacing: 16) {
                Text("Initial Credit Settings")
                    .font(.headline)
                    .foregroundColor(.primary)

                // Teacher credit
                VStack(alignment: .leading, spacing: 8) {
                    Text("Teacher Initial Credits")
                        .font(.subheadline)
                        .foregroundColor(.primary)

                    HStack {
                        TextField("500", value: $teacherInitialCredit, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .focused($focusedField, equals: .teacherInitialCredit)

                        Stepper("", value: $teacherInitialCredit, in: 0...10000, step: 100)
                            .labelsHidden()
                    }
                }

                // Student credit
                VStack(alignment: .leading, spacing: 8) {
                    Text("Student Initial Credits")
                        .font(.subheadline)
                        .foregroundColor(.primary)

                    HStack {
                        TextField("50", value: $studentInitialCredit, format: .number)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .focused($focusedField, equals: .studentInitialCredit)

                        Stepper("", value: $studentInitialCredit, in: 0...1000, step: 50)
                            .labelsHidden()
                    }
                }
            }

            // Summary
            if totalAccountCount > 0 {
                summarySection
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var summarySection: some View {
        VStack(spacing: 8) {
            Text("Summary")
                .font(.headline)
                .foregroundColor(.primary)

            HStack {
                Text("Total accounts to create:")
                Spacer()
                Text("\(totalAccountCount)")
                    .fontWeight(.semibold)
            }

            if let school = selectedSchool {
                HStack {
                    Text("School:")
                    Spacer()
                    Text(school.schoolName)
                        .foregroundColor(.purple)
                }
            }

            if teacherCount > 0 {
                HStack {
                    Text("Teachers:")
                    Spacer()
                    Text("\(teacherCount) (\(teacherInitialCredit) credits each)")
                        .foregroundColor(.blue)
                }
            }

            if studentCount > 0 {
                HStack {
                    Text("Students:")
                    Spacer()
                    Text("\(studentCount) (\(studentInitialCredit) credits each)")
                        .foregroundColor(.green)
                }
            }

            Divider()

            VStack(spacing: 4) {
                Text("Account Examples:")
                    .font(.subheadline)
                    .fontWeight(.semibold)

                if !organization.isEmpty {
                    Text("Username: \(organization.lowercased())-teacher1, \(organization.lowercased())-student1")
                        .font(.caption)
                        .foregroundColor(.gray)

                    Text("Name: Teacher 1 \(organization), Student 1 \(organization)")
                        .font(.caption)
                        .foregroundColor(.gray)

                    Text("Email: \(organization.lowercased())-teacher1@\(organization.lowercased())-example.com")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
        }
        .padding()
        .background(Color.white)
        .cornerRadius(8)
    }
    
    private var progressSection: some View {
        VStack(spacing: 12) {
            Text("Creating Accounts...")
                .font(.headline)
                .foregroundColor(.primary)
            
            ProgressView(value: creationProgress, total: 1.0)
                .progressViewStyle(LinearProgressViewStyle())
            
            if !currentlyCreating.isEmpty {
                Text("Creating: \(currentlyCreating)")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            
            Text("\(Int(creationProgress * Double(totalAccountCount))) of \(totalAccountCount) accounts created")
                .font(.caption)
                .foregroundColor(.gray)
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    private var createButtonSection: some View {
        Button(action: createAccounts) {
            HStack {
                if isCreating {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .scaleEffect(0.8)
                }
                
                Text(isCreating ? "Creating..." : "Create Accounts")
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(canCreateAccounts ? Color.blue : Color.gray)
            .cornerRadius(10)
        }
        .disabled(!canCreateAccounts || isCreating)
    }
    
    // MARK: - Computed Properties
    
    private var totalAccountCount: Int {
        teacherCount + studentCount
    }
    
    private var canCreateAccounts: Bool {
        let orgValid = !organization.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let passwordValid = !password.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        let schoolSelected = selectedSchool != nil
        let accountCountValid = (teacherCount > 0 || studentCount > 0)

        // Debug output
        print("BatchAccountCreationView: Validation check:")
        print("  - Organization valid: \(orgValid) (value: '\(organization)')")
        print("  - Password valid: \(passwordValid) (value: '\(password)')")
        print("  - School selected: \(schoolSelected) (school: \(selectedSchool?.schoolName ?? "none"))")
        print("  - Account count valid: \(accountCountValid) (teachers: \(teacherCount), students: \(studentCount))")

        let result = orgValid && passwordValid && schoolSelected && accountCountValid
        print("  - Final result: \(result)")

        return result
    }
    
    // MARK: - Actions

    private func createAccounts() {
        guard canCreateAccounts else { return }

        focusedField = nil
        isCreating = true
        creationProgress = 0.0

        Task {
            await performBatchAccountCreation()
        }
    }

    @MainActor
    private func performBatchAccountCreation() async {
        let trimmedOrganization = organization.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedPassword = password.trimmingCharacters(in: .whitespacesAndNewlines)

        var successCount = 0
        var failedAccounts: [String] = []
        let totalAccounts = totalAccountCount

        // Create teacher accounts
        if teacherCount > 0 {
            for i in 1...teacherCount {
                let username = "\(trimmedOrganization)-teacher\(i)".lowercased()
                currentlyCreating = username

                let result = await userManager.register(
                    username: username,
                    password: trimmedPassword,
                    firstName: "Teacher \(i)",
                    lastName: trimmedOrganization,
                    email: "\(username)@\(trimmedOrganization)-example.com",
                    phone: "+852 0000000\(i)",
                    gender: "Prefer not to say",
                    grade: "S1", // Default grade, not relevant for teachers
                    schoolId: selectedSchool?.id,
                    role: "Teacher"
                )

                switch result {
                case .success:
                    successCount += 1

                    // Set initial credits for the teacher account
                    await setCreditForNewAccount(username: username, creditAmount: teacherInitialCredit, accountType: "teacher")

                case .failure(let error):
                    failedAccounts.append("\(username): \(error.localizedDescription)")
                }

                creationProgress = Double(successCount + failedAccounts.count) / Double(totalAccounts)

                // Small delay to prevent overwhelming the server
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            }
        }

        // Create student accounts
        if studentCount > 0 {
            for i in 1...studentCount {
                let username = "\(trimmedOrganization)-student\(i)".lowercased()
                currentlyCreating = username

                let result = await userManager.register(
                    username: username,
                    password: trimmedPassword,
                    firstName: "Student \(i)",
                    lastName: trimmedOrganization,
                    email: "\(username)@\(trimmedOrganization)-example.com",
                    phone: "+852 1000000\(i)",
                    gender: "Prefer not to say",
                    grade: "S1",
                    schoolId: selectedSchool?.id,
                    role: "Student"
                )

                switch result {
                case .success:
                    successCount += 1

                    // Set initial credits for the student account
                    await setCreditForNewAccount(username: username, creditAmount: studentInitialCredit, accountType: "student")

                case .failure(let error):
                    failedAccounts.append("\(username): \(error.localizedDescription)")
                }

                creationProgress = Double(successCount + failedAccounts.count) / Double(totalAccounts)

                // Small delay to prevent overwhelming the server
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds
            }
        }

        // Update UI with results
        isCreating = false
        currentlyCreating = ""

        if failedAccounts.isEmpty {
            successMessage = "Successfully created \(successCount) accounts for \(trimmedOrganization)!\n\nTeachers: \(teacherCount) (\(teacherInitialCredit) credits each)\nStudents: \(studentCount) (\(studentInitialCredit) credits each)"
            showingSuccessAlert = true
        } else {
            let failureDetails = failedAccounts.joined(separator: "\n")
            errorMessage = "Created \(successCount) of \(totalAccounts) accounts.\n\nFailed accounts:\n\(failureDetails)"
            showingErrorAlert = true
        }
    }

    // Helper function to set credits for newly created accounts
    private func setCreditForNewAccount(username: String, creditAmount: Int, accountType: String) async {
        print("BatchAccountCreationView: Setting \(creditAmount) credits for \(accountType) account: \(username)")

        // Get user ID by temporarily logging in as the new user
        let userId = await getUserIdByLogin(username: username, password: password.trimmingCharacters(in: .whitespacesAndNewlines))

        guard let userId = userId else {
            print("BatchAccountCreationView: ❌ Could not get user ID for \(username)")
            return
        }

        // Set the initial credits using the admin endpoint
        let result = await userManager.adjustUserCredit(
            userId: userId,
            amount: creditAmount,
            description: "Initial credit allocation for \(accountType) account"
        )

        switch result {
        case .success(let response):
            print("BatchAccountCreationView: ✅ Successfully set \(creditAmount) credits for \(username). New balance: \(response.actualBalance ?? 0)")
        case .failure(let error):
            print("BatchAccountCreationView: ❌ Failed to set credits for \(username): \(error.localizedDescription)")
        }
    }

    // Helper function to get user ID by temporarily logging in
    private func getUserIdByLogin(username: String, password: String) async -> String? {
        // Create a temporary login request to get the user ID
        guard let url = URL(string: APIConfig.loginEndpoint) else {
            return nil
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")

        let body: [String: Any] = [
            "username": username,
            "password": password
        ]

        do {
            request.httpBody = try JSONSerialization.data(withJSONObject: body)
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                return nil
            }

            // Parse the login response to get user ID
            if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any],
               let user = jsonObject["user"] as? [String: Any],
               let userId = user["_id"] as? String {
                return userId
            }
        } catch {
            print("BatchAccountCreationView: Error getting user ID for \(username): \(error)")
        }

        return nil
    }
}

#Preview {
    BatchAccountCreationView()
}
