//
//  VideoView.swift
//  Luminous Education
//
//  Created by <PERSON> on 1/8/2024.
//

import SwiftUI
import AVKit
import Combine
import Foundation
import CoreMedia

// Define a preference key to communicate fullscreen state to parent views
struct FullscreenModeKey: PreferenceKey {
    static var defaultValue: Bool = false
    
    static func reduce(value: inout Bool, nextValue: () -> Bool) {
        value = nextValue()
    }
}

// Extension to hide the navigation bar in a cross-platform way
extension View {
    func hideNavigationBarCompat(_ hide: Bool) -> some View {
        #if os(iOS)
        return self
            .navigationBarBackButtonHidden(hide)
        #else
        return self
        #endif
    }
}

struct VideoView: View {
    // Input parameter for video URL
    var videoURL: String
    var lessonName: String
    
    // Store the player as a state object so it persists
    @State private var player: AVPlayer?
    
    // State to track our custom fullscreen mode
    @State private var isFullScreen = false
    
    // For controlling the sidebar - using app storage for global state
    @AppStorage("isSidebarCollapsed") private var isSidebarCollapsed = false
    
    // State to track current video position for the timeline
    @State private var currentVideoTime: Double = 0
    
    // Quiz manager for handling interactive questions
    @StateObject private var quizManager = VideoQuizManager(questions: [
        VideoQuizQuestion(
            timestamp: 10.0,  // Show after 10 seconds - shorter for testing
            question: "What is the main topic of this video?",
            options: ["Option 1", "Option 2", "Option 3", "Option 4"],
            correctAnswerIndex: 0,
            duration: 20.0,
            points: 10,
            explanation: "This is the explanation for the correct answer."
        ),
        VideoQuizQuestion(
            timestamp: 40.0,  // Show after 40 seconds
            question: "What concept was just explained?",
            options: ["Concept A", "Concept B", "Concept C", "All of the above"],
            correctAnswerIndex: 3,
            duration: 20.0,
            points: 15,
            explanation: "All of the concepts were just covered in the video."
        ),
        // Add more questions as needed
    ])
    
    // Tools manager for interactive tools
    @StateObject private var toolsManager = InteractiveToolsManager(toolEvents: [
        InteractiveToolEvent(timestamp: 20.0, toolType: .arExperience, context: "solar_system"),
        InteractiveToolEvent(timestamp: 70.0, toolType: .modelViewer, context: "dna_model"),
        InteractiveToolEvent(timestamp: 120.0, toolType: .aiAssistant, context: "concept_explanation"),
        InteractiveToolEvent(timestamp: 180.0, toolType: .interactiveDiagram, context: "process_flow")
    ])
    
    // Timer for updating managers with current video time
    let timer = Timer.publish(every: 1, on: .main, in: .common).autoconnect()
    
    // Environment values for navigation elements
    @Environment(\.presentationMode) var presentationMode
    
    // State for control visibility
    @State private var isHoveringVideo = false
    @State private var controlsTimer: Timer? = nil
    
    // State for completion overlay
    @State private var showCompletionOverlay = false
    
    // Navigation state
    @State private var navigateToQuizView = false
    
    // Computed property to get the URL
    private var videoURLObject: URL? {
        if !videoURL.isEmpty {
            return URL(string: videoURL)
        }
        return nil
    }
    
    // Initialize with default values
    init(videoURL: String = "", lessonName: String = "Video Lesson") {
        self.videoURL = videoURL
        self.lessonName = lessonName
    }
    
    // Helper method to initialize player outside of view body
    private func initializePlayer(url: URL) {
        if player == nil {
            player = createPlayer(url: url)
            
            // Set the player in both managers for automatic pausing/playing
            if let player = player {
                quizManager.setPlayer(player)
                toolsManager.setPlayer(player)
            }
            
            // Add a small delay to ensure UI is ready
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                // Start playback
                player?.play()
            }
        }
    }
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                VStack(spacing: 0) {
                    // Only show header when not in full screen
                    if !isFullScreen {
                        HStack {
                            Text(lessonName)
                                .font(.largeTitle)
                                .bold()
                                .padding(.top)
                            Spacer()
                            
                            // Custom fullscreen button
                            Button(action: {
                                toggleFullscreen(true)
                            }) {
                                Image(systemName: "arrow.up.left.and.arrow.down.right")
                                    .font(.title2)
                                    .foregroundColor(.blue)
                            }
                            .padding(.trailing)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal)
                    }
                    
                    // Video Player with custom fullscreen support
                    ZStack {
                        // Background color
                        if isFullScreen {
                            Color.black
                                .edgesIgnoringSafeArea(.all)
                        }
                        
                        if let url = videoURLObject {
                            ZStack {
                                // Video Player
                                VideoPlayer(player: player ?? AVPlayer(url: url))
                                    .frame(
                                        width: isFullScreen ? nil : nil,
                                        height: isFullScreen ? geometry.size.height : 600
                                    )
                                    .cornerRadius(isFullScreen ? 0 : 10)
                                    .padding(isFullScreen ? 0 : 16)
                                    .onAppear {
                                        initializePlayer(url: url)
                                    }
                                    .overlay(
                                        // Custom video controls overlay
                                        GeometryReader { controlGeometry in
                                            VStack {
                                                Spacer()
                                                
                                                // Control panel background
                                                VStack(spacing: 8) {
                                                    // Timeline
                                                    VideoTimelineIndicator(
                                                        currentTime: getCurrentTime(),
                                                        duration: getVideoDuration(),
                                                        quizTimes: quizManager.quizTimestamps,
                                                        toolTimes: toolsManager.interactiveToolsTimestamps,
                                                        onSeek: { time in
                                                            seekToTime(time)
                                                        }
                                                    )
                                                    .frame(height: 30)
                                                    
                                                    // Playback controls
                                                    HStack {
                                                        // Play/Pause button
                                                        Button(action: {
                                                            if let player = player {
                                                                if player.timeControlStatus == .playing {
                                                                    player.pause()
                                                                } else {
                                                                    player.play()
                                                                }
                                                            }
                                                        }) {
                                                            Image(systemName: (player?.timeControlStatus == .playing) ? "pause.fill" : "play.fill")
                                                                .font(.title2)
                                                                .foregroundColor(.white)
                                                                .frame(width: 44, height: 44)
                                                                .background(Color.black.opacity(0.6))
                                                                .clipShape(Circle())
                                                        }
                                                        
                                                        Spacer()
                                                        
                                                        // Volume control
                                                        HStack {
                                                            Image(systemName: "speaker.wave.2.fill")
                                                                .foregroundColor(.white)
                                                            Slider(value: Binding(
                                                                get: { player?.volume ?? 1.0 },
                                                                set: { player?.volume = $0 }
                                                            ), in: 0...1)
                                                            .frame(width: 100)
                                                        }
                                                        .padding(.horizontal)
                                                    }
                                                }
                                                .padding()
                                                .background(
                                                    LinearGradient(
                                                        gradient: Gradient(colors: [Color.black.opacity(0), Color.black.opacity(0.7)]),
                                                        startPoint: .top,
                                                        endPoint: .bottom
                                                    )
                                                )
                                            }
                                        }
                                        .opacity((isFullScreen && isHoveringVideo) ? 1 : 0) // Show only in fullscreen and when hovering
                                        .animation(.easeInOut(duration: 0.2), value: isHoveringVideo)
                                    )
                            }
                        } else {
                            // Fallback when no valid video URL is provided
                            Rectangle()
                                .fill(Color.gray.opacity(0.2))
                                .frame(height: 600)
                                .cornerRadius(10)
                                .padding()
                                .overlay(
                                    Text("Video not available")
                                        .font(.title)
                                        .foregroundColor(.gray)
                                )
                        }
                        
                        // Custom fullscreen exit button (only shown in fullscreen mode)
                        if isFullScreen {
                            VStack {
                                HStack {
                                    Spacer()
                                    Button(action: {
                                        toggleFullscreen(false)
                                    }) {
                                        Image(systemName: "arrow.down.right.and.arrow.up.left")
                                            .font(.title)
                                            .foregroundColor(.white)
                                            .padding(10)
                                            .background(Circle().fill(Color.black.opacity(0.6)))
                                    }
                                    .padding()
                                }
                                Spacer()
                            }
                        }
                    }
                    
                    // Hide buttons in fullscreen mode
                    if !isFullScreen {
                        Buttons()
                    }
                }
                
                // Quiz overlay
                if quizManager.isShowingQuestion,
                   let currentQuestion = quizManager.currentQuestion {
                    VideoQuizOverlayView(
                        question: currentQuestion,
                        onAnswer: { answerIndex in
                            quizManager.submitAnswer(answerIndex)
                        }
                    )
                    .transition(.opacity)
                }
                
                // Completion overlay
                if showCompletionOverlay {
                    ZStack {
                        // Semi-transparent background
                        Color.black.opacity(0.7)
                            .edgesIgnoringSafeArea(.all)
                        
                        // Content
                        VStack(spacing: 25) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 60))
                                .foregroundColor(.green)
                            
                            Text("Video Completed!")
                                .font(.title)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                            
                            Text("What would you like to do next?")
                                .font(.title3)
                                .foregroundColor(.white.opacity(0.9))
                            
                            VStack(spacing: 16) {
                                NavigationLink(destination: QuizView(lessonId: lessonName, quizType: "post-quiz")) {
                                    HStack {
                                        Image(systemName: "list.bullet.clipboard.fill")
                                            .font(.title2)
                                        Text("Take Post-Quiz")
                                            .font(.title3)
                                            .fontWeight(.semibold)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding()
                                    .background(Color.blue)
                                    .foregroundColor(.white)
                                    .cornerRadius(12)
                                }
                                
                                Button(action: {
                                    showCompletionOverlay = false
                                }) {
                                    Text("Watch Again")
                                        .font(.title3)
                                        .foregroundColor(.white.opacity(0.7))
                                        .padding()
                                }
                            }
                            .padding(.top)
                        }
                        .padding(40)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color(white: 0.15))
                                .shadow(radius: 20)
                        )
                        .padding()
                    }
                    .transition(.opacity)
                }
                
                // Interactive Tools overlay
                if toolsManager.showInteractiveTools, 
                   let toolEvent = toolsManager.currentToolEvent {
                    ZStack {
                        // Semi-transparent background
                        Color.black.opacity(0.7)
                            .edgesIgnoringSafeArea(.all)
                        
                        // Content
                        VStack(spacing: 25) {
                            HStack {
                                Text(toolsManager.isToolContentActive ? toolEvent.toolType.rawValue : "Interactive Tool Available")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.white)
                                
                                Spacer()
                                
                                Button(action: {
                                    withAnimation {
                                        toolsManager.closeInteractiveTools()
                                    }
                                }) {
                                    Image(systemName: "xmark.circle.fill")
                                        .font(.title2)
                                        .foregroundColor(.white.opacity(0.7))
                                }
                            }
                            .padding(.bottom)
                            
                            if !toolsManager.isToolContentActive {
                                // Tool introduction screen
                                VStack(spacing: 16) {
                                    Image(systemName: toolEvent.toolType.iconName)
                                        .font(.system(size: 60))
                                        .foregroundColor(toolEvent.toolType.color)
                                        .frame(width: 100, height: 100)
                                        .background(
                                            Circle()
                                                .fill(toolEvent.toolType.color.opacity(0.2))
                                        )
                                        .padding(.bottom, 8)
                                    
                                    Text(toolEvent.toolType.rawValue)
                                        .font(.title)
                                        .fontWeight(.bold)
                                        .foregroundColor(.white)
                                    
                                    Text("This interactive tool will help you visualize key concepts from the video.")
                                        .font(.body)
                                        .foregroundColor(.white.opacity(0.9))
                                        .multilineTextAlignment(.center)
                                        .padding(.horizontal)
                                    
                                    Button(action: {
                                        toolsManager.launchCurrentTool()
                                    }) {
                                        Text("Open Tool")
                                            .font(.headline)
                                            .foregroundColor(.white)
                                            .padding(.horizontal, 40)
                                            .padding(.vertical, 16)
                                            .background(toolEvent.toolType.color)
                                            .cornerRadius(12)
                                            .shadow(color: Color.black.opacity(0.2), radius: 4)
                                    }
                                    .padding(.top, 16)
                                    
                                    Button(action: {
                                        toolsManager.closeInteractiveTools()
                                    }) {
                                        Text("Continue Watching")
                                            .font(.subheadline)
                                            .foregroundColor(.white.opacity(0.7))
                                            .padding()
                                    }
                                }
                                .frame(maxWidth: .infinity)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color(white: 0.15))
                                )
                            } else {
                                // Tool content area
                                VStack {
                                    // Tool content based on type
                                    switch toolEvent.toolType {
                                    case .arExperience:
                                        // Use existing ARView with proper context
//                                        ARView(context: toolEvent.context)
                                        ARView()
                                    case .modelViewer:
                                        ARView()
//                                        ModelViewerView(modelName: toolEvent.context)
                                    case .aiAssistant:
                                        ARView()
//                                        AIAssistantView(context: toolEvent.context)
                                    case .interactiveDiagram:
                                        ARView()
//                                        InteractiveDiagramView(context: toolEvent.context)
                                    }
                                    
                                    // Back to video button
//                                    Button(action: {
//                                        toolsManager.closeToolContent()
//                                    }) {
//                                        Text("Return to Video")
//                                            .font(.headline)
//                                            .foregroundColor(.white)
//                                            .padding(.horizontal, 30)
//                                            .padding(.vertical, 12)
//                                            .background(Color.blue)
//                                            .cornerRadius(8)
//                                    }
//                                    .padding(.top, 20)
                                }
                                .frame(maxWidth: .infinity, maxHeight: .infinity)
                                .padding()
                                .background(
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color(white: 0.15))
                                )
                            }
                        }
                        .padding(30)
                        .background(
                            RoundedRectangle(cornerRadius: 20)
                                .fill(Color(white: 0.1))
                        )
                        .padding()
                    }
                    .transition(.opacity)
                }
            }
            .onDisappear {
                // Clean up resources when view disappears
                player?.pause()
                player = nil
                
                // Ensure we're not in fullscreen mode when leaving the view
                if isFullScreen {
                    // Use async to avoid modifying state during view update
                    DispatchQueue.main.async {
                        toggleFullscreen(false)
                    }
                }
            }
            // Set preference value to communicate fullscreen state to parent views
            .preference(key: FullscreenModeKey.self, value: isFullScreen)
            // Hide navigation bar in fullscreen mode
            .hideNavigationBarCompat(isFullScreen)
            .onReceive(timer) { _ in
                if let player = player {
                    let time = player.currentTime().seconds
                    // Add safety check for NaN or infinite values
                    if time.isFinite && !time.isNaN {
                        quizManager.updateTime(time)
                        toolsManager.updateTime(time)
                        currentVideoTime = time
                    } else {
                        // If time is invalid, use 0 or last known good value
                        let fallbackTime = currentVideoTime > 0 ? currentVideoTime : 0
                        quizManager.updateTime(fallbackTime)
                        toolsManager.updateTime(fallbackTime)
                    }
                }
            }
        }
        // For iOS only, hide status bar in fullscreen mode
        #if os(iOS)
        .statusBar(hidden: isFullScreen)
        #endif
        .animation(.easeInOut(duration: 0.3), value: isFullScreen)
    }
    
    // Function to toggle fullscreen mode and update UI accordingly
    private func toggleFullscreen(_ enable: Bool) {
        // Use async to ensure state changes happen outside view update cycle
        DispatchQueue.main.async {
            withAnimation(.easeInOut(duration: 0.3)) {
                // Update fullscreen state
                isFullScreen = enable
                
                // Toggle sidebar collapse state
                isSidebarCollapsed = enable
                
                // Use notification center to communicate with ContentView and SidebarHelper
                NotificationCenter.default.post(
                    name: NSNotification.Name("SidebarCollapseStateChanged"),
                    object: nil,
                    userInfo: ["isCollapsed": enable]
                )
            }
            
            // Apply UI changes for fullscreen - iOS specific code
            #if os(iOS)
            // iOS 16+ uses newer API
            #if compiler(>=5.7)
            // Modern status bar handling is done through the .statusBar modifier
            #else
            // Older iOS versions fallback
            let keyWindow = UIApplication.shared.windows.filter {$0.isKeyWindow}.first
            keyWindow?.windowScene?.statusBarManager?.isStatusBarHidden = enable
            #endif
            #endif
        }
    }
    
    // Function to set up the player
    private func createPlayer(url: URL) -> AVPlayer {
        let newPlayer = AVPlayer(url: url)
        
        // Disable AirPlay
        newPlayer.allowsExternalPlayback = false
        
        // Configure player for better playback experience
        newPlayer.automaticallyWaitsToMinimizeStalling = true
        
        // Add observer for playback completion
        NotificationCenter.default.addObserver(
            forName: .AVPlayerItemDidPlayToEndTime,
            object: newPlayer.currentItem,
            queue: .main
        ) { [self] _ in
            // Show completion overlay when video ends
            withAnimation(.easeInOut(duration: 0.3)) {
                showCompletionOverlay = true
            }
            print("Video playback completed")
        }
        
        print("Playing \(lessonName) from URL: \(url)")
        return newPlayer
    }
    
    // Helper methods to simplify complex expressions
    private func getCurrentTime() -> TimeInterval {
        return currentVideoTime.isFinite ? currentVideoTime : 0
    }
    
    private func getVideoDuration() -> TimeInterval {
        if let player = player, 
           let duration = player.currentItem?.duration.seconds,
           duration.isFinite {
            return duration
        }
        return 100 // Default duration
    }
    
    private func seekToTime(_ time: TimeInterval) {
        let cmTime = CMTime(seconds: time, preferredTimescale: 1000)
        player?.seek(to: cmTime)
        currentVideoTime = time
        quizManager.updateTime(time)
        toolsManager.updateTime(time)
    }
}

struct Buttons: View {
    var body: some View {
        // Buttons
        HStack {
            Button(action: {
                // Action for Bookmark
            }) {
                Label("Bookmark", systemImage: "bookmark")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray, lineWidth: 1)
                    )
            }

            Button(action: {
                // Action for Note
            }) {
                Label("Note", systemImage: "note.text")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray, lineWidth: 1)
                    )
            }

            Button(action: {
                // Action for Mark Done
            }) {
                Label("Activity", systemImage: "gamecontroller.fill")
                     .frame(maxWidth: .infinity)
                     .padding()
                     .overlay(
                         RoundedRectangle(cornerRadius: 8)
                             .stroke(Color.gray, lineWidth: 1)
                     )
            }
        }
        .padding()
    }
}

// Add InteractiveToolButton view
struct InteractiveToolButton: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 30))
                    .foregroundColor(color)
                    .frame(width: 60, height: 60)
                    .background(
                        RoundedRectangle(cornerRadius: 15)
                            .fill(color.opacity(0.2))
                    )
                
                Text(title)
                    .font(.headline)
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 15)
                    .fill(Color(white: 0.2))
            )
        }
    }
}

#Preview {
    VideoView()
}
