import SwiftUI
import WebKit

struct DeveloperInteractiveToolsView: View {
    @StateObject private var gameRoomManager = GameRoomManager.shared
    @StateObject private var userManager = UserManager.shared
    @State private var selectedGame: Game?
    @State private var showingGamePlayer = false
    @State private var gameURL: String = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isLoading = false
    @State private var selectedTab = 0
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Developer Interactive Tools")
                    .font(.title)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding()
            
            // Tab Selection
            Picker("Tool Type", selection: $selectedTab) {
                Text("Games").tag(0)
                Text("In-Class Exercise").tag(1)
                Text("Debug Tools").tag(2)
                Text("Credit Management").tag(3)
            }
            .pickerStyle(.segmented)
            .padding(.horizontal)
            
            // Content based on selected tab
            if selectedTab == 0 {
                GameToolsView(
                    gameRoomManager: gameRoomManager,
                    userManager: userManager,
                    selectedGame: $selectedGame,
                    showingGamePlayer: $showingGamePlayer,
                    gameURL: $gameURL,
                    showingAlert: $showingAlert,
                    alertMessage: $alertMessage,
                    isLoading: $isLoading
                )
            } else if selectedTab == 1 {
                DeveloperExerciseView()
            } else if selectedTab == 2 {
                DeveloperDebugToolsView()
            } else {
                CreditAdjustmentView()
            }
        }
        .fullScreenCover(isPresented: $showingGamePlayer) {
            GamePlayerView(
                gameURL: gameURL, 
                gameName: selectedGame?.name ?? "Developer Game",
                onClose: {
                    // For developer tools, just close the game player
                    showingGamePlayer = false
                }
            )
        }
        .onChange(of: showingGamePlayer) { newValue in
            print("DeveloperInteractiveToolsView: showingGamePlayer changed to: \(newValue)")
            if newValue {
                print("DeveloperInteractiveToolsView: fullScreenCover presented with gameURL: \(gameURL)")
            }
        }
        .alert("Error", isPresented: $showingAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text(alertMessage)
        }
        .task {
            // Initial load of grades
            await gameRoomManager.fetchGrades()
            
            // Set Grade 1 (Form 1) as default selection
            if gameRoomManager.availableGrades.contains(.form1) {
                gameRoomManager.selectedGrade = .form1
            }
        }
    }
}

// MARK: - Developer Exercise View (Read-only)
struct DeveloperExerciseView: View {
    @StateObject private var viewModel = ExerciseViewModel()
    @StateObject private var userManager = UserManager.shared
    @State private var selectedClassroomId: String = "sample-classroom-id"
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("In-Class Exercise")
                    .font(.title)
                    .fontWeight(.bold)
                Spacer()
            }
            .padding()
            
            // Classroom Selection (for developers - show a sample classroom)
            VStack(alignment: .leading, spacing: 8) {
                Text("Classroom ID")
                    .font(.headline)
                
                TextField("Enter Classroom ID", text: $selectedClassroomId)
                    .textFieldStyle(.roundedBorder)
                    .onSubmit {
                        Task {
                            try? await loadExercises()
                        }
                    }
                
                Button("Load Sample Classroom") {
                    selectedClassroomId = "sample-classroom-id"
                    Task {
                        try? await loadExercises()
                    }
                }
                .buttonStyle(.bordered)
                .font(.caption)
            }
            .padding(.horizontal)
            
            // Developer Notice
            VStack(spacing: 12) {
                Image(systemName: "info.circle.fill")
                    .font(.system(size: 40))
                    .foregroundColor(.blue)
                
                Text("Developer Mode")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("As a developer, you can view exercises but cannot create or modify them. Use the Teacher role to create exercises.")
                    .font(.body)
                    .foregroundColor(.gray)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .padding()
            
            // Exercise List (Read-only)
            if viewModel.isLoading {
                ProgressView("Loading exercises...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if viewModel.exercises.isEmpty {
                EmptyStateView(
                    title: "No Exercises",
                    systemImage: "checkmark.circle",
                    description: Text("No exercises are available for this classroom")
                )
            } else {
                ScrollView {
                    LazyVStack(spacing: 16) {
                        ForEach(viewModel.exercises
                            .sorted { $0.title.localizedStandardCompare($1.title) == .orderedAscending }
                        ) { exercise in
                            DeveloperExerciseCard(exercise: exercise)
                        }
                    }
                    .padding()
                }
            }
        }
        .task {
            try? await loadExercises()
        }
    }
    
    private func loadExercises() async throws {
        if !selectedClassroomId.isEmpty {
            try await viewModel.getExercises(for: selectedClassroomId)
        } else {
            // No sample exercise creation here anymore
        }
    }
}

// MARK: - Developer Exercise Card (Read-only)
struct DeveloperExerciseCard: View {
    let exercise: Exercise
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(exercise.title)
                        .font(.headline)
                    Text(exercise.topic)
                        .font(.subheadline)
                        .foregroundColor(.gray)
                    if !exercise.subtopic.isEmpty {
                        Text(exercise.subtopic)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    // Show assigned classroom
                                            Text("Classrooms: \(exercise.classroomIds.joined(separator: ", "))")
                        .font(.caption)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(4)
                }
                
                Spacer()
                
                VStack(alignment: .trailing) {
                    Text("\(exercise.questions.count) Questions")
                        .font(.caption)
                        .foregroundColor(.gray)
                }
            }
            
            HStack {
                Label("Created by Teacher", systemImage: "person.circle")
                    .font(.caption)
                    .foregroundColor(.blue)
                
                Spacer()
                
                // Show due date instead of active status
                HStack {
                    Image(systemName: "clock")
                        .font(.caption)
                        .foregroundColor(.orange)
                    Text("Due: \(exercise.dueDate.formatted(date: .abbreviated, time: .shortened))")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
            
            // Question preview
            if !exercise.questions.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Sample Questions:")
                        .font(.caption)
                        .fontWeight(.semibold)
                        .foregroundColor(.gray)
                    
                    ForEach(Array(exercise.questions.prefix(3))) { question in
                        HStack {
                            Image(systemName: question.questionType == .multipleChoice ? "checkmark.circle" : "text.quote")
                                .font(.caption)
                                .foregroundColor(.blue)
                            
                            QuestionTextRenderer(text: question.questionText, fontSize: 12)
                                .lineLimit(2)
                                .foregroundColor(.gray)
                            
                            Spacer()
                        }
                    }
                    
                    if exercise.questions.count > 3 {
                        Text("+ \(exercise.questions.count - 3) more questions")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                }
                .padding(.top, 8)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(radius: 2)
    }
}

// MARK: - Game Tools View
struct GameToolsView: View {
    @ObservedObject var gameRoomManager: GameRoomManager
    @ObservedObject var userManager: UserManager
    @Binding var selectedGame: Game?
    @Binding var showingGamePlayer: Bool
    @Binding var gameURL: String
    @Binding var showingAlert: Bool
    @Binding var alertMessage: String
    @Binding var isLoading: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            // Grade Selection
            VStack(alignment: .leading, spacing: 8) {
                Text("Select Grade")
                    .font(.headline)
                
                if gameRoomManager.availableGrades.isEmpty {
                    Text("No grades available")
                        .font(.subheadline)
                        .foregroundColor(.gray)
                        .padding()
                } else {
                    Picker("Grade", selection: $gameRoomManager.selectedGrade) {
                        ForEach(gameRoomManager.availableGrades, id: \.self) { grade in
                            Text(grade.displayName).tag(Optional(grade))
                        }
                    }
                    .pickerStyle(.segmented)
                }
            }
            .padding()
            
            if isLoading {
                ProgressView("Loading...")
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if let error = gameRoomManager.error {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 50))
                        .foregroundColor(.red)
                    
                    Text("Error Loading Data")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text(error.localizedDescription)
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                    
                    Button("Retry") {
                        Task {
                            await gameRoomManager.retryFetchData()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else if gameRoomManager.availableTopics.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "gamecontroller")
                        .font(.system(size: 50))
                        .foregroundColor(.gray)
                    
                    Text("No Topics Available")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("There are no topics available for this grade level.")
                        .font(.body)
                        .foregroundColor(.gray)
                        .multilineTextAlignment(.center)
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // Topic Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("Select Topic")
                        .font(.headline)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(gameRoomManager.availableTopics
                                .filter { $0.grade == gameRoomManager.selectedGrade }
                                .sorted { $0.name.localizedStandardCompare($1.name) == .orderedAscending }
                            ) { topic in
                                TopicCard(
                                    topic: topic,
                                    isSelected: gameRoomManager.selectedTopic?.id == topic.id,
                                    action: {
                                        if gameRoomManager.selectedTopic?.id != topic.id {
                                            gameRoomManager.selectTopic(topic)
                                            selectedGame = nil
                                        }
                                    }
                                )
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
                
                // Game Selection
                if let topic = gameRoomManager.selectedTopic {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Select Game")
                            .font(.headline)
                        
                        if gameRoomManager.availableGames.isEmpty {
                            Text("No games available for this topic")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                                .padding()
                        } else {
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 12) {
                                    ForEach(gameRoomManager.availableGames
                                        .sorted { $0.name.localizedStandardCompare($1.name) == .orderedAscending }
                                    ) { game in
                                        GameCard(
                                            game: game,
                                            isSelected: selectedGame?.id == game.id,
                                            action: {
                                                selectedGame = game
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal)
                            }
                        }
                    }
                    .padding()
                }
            }
            
            Spacer()
            
            // Launch Button
            if !isLoading && gameRoomManager.error == nil {
                VStack(spacing: 12) {
                    Button(action: launchGame) {
                        HStack {
                            if isLoading {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                    .scaleEffect(0.8)
                            } else {
                                Text("Launch Game")
                                    .font(.headline)
                            }
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(
                            gameRoomManager.selectedTopic != nil && selectedGame != nil && !isLoading ?
                            Color.green :
                            Color.gray
                        )
                        .cornerRadius(10)
                    }
                    .disabled(gameRoomManager.selectedTopic == nil || selectedGame == nil || isLoading)
                }
                .padding()
            }
        }
    }
    
    private func launchGame() {
        print("GameToolsView: launchGame called")
        
        guard let game = selectedGame else {
            print("GameToolsView: No game selected")
            alertMessage = "Please select a game first"
            showingAlert = true
            return
        }
        
        print("GameToolsView: Selected game - ID: \(game.id), Name: \(game.name)")
        print("GameToolsView: Game URL from DB: \(game.url ?? "nil")")
        
        // Get the game URL
        if let gameURLFromDB = game.url, !gameURLFromDB.isEmpty {
            print("GameToolsView: Using URL from database: \(gameURLFromDB)")
            gameURL = gameURLFromDB
        } else {
            // Fallback: generate a URL based on game if no URL in database
            let baseURL = "https://funma-game.com"
            let gamePath = "\(game.id)"
            gameURL = "\(baseURL)/play/\(gamePath)"
            print("GameToolsView: Generated fallback URL: \(gameURL)")
        }
        
        print("GameToolsView: Final gameURL: \(gameURL)")
        
        // Validate URL format
        if !isValidURL(gameURL) {
            print("GameToolsView: ERROR - Invalid URL format: \(gameURL)")
            alertMessage = "Invalid game URL format. Please check the game configuration."
            showingAlert = true
            return
        }
        
        print("GameToolsView: URL is valid, about to set showingGamePlayer = true")
        
        // Show the game player
        showingGamePlayer = true
        
        print("GameToolsView: showingGamePlayer set to true")
    }
    
    private func isValidURL(_ urlString: String) -> Bool {
        guard let url = URL(string: urlString) else {
            print("GameToolsView: URL validation failed - cannot create URL from: \(urlString)")
            return false
        }
        
        let isValid = url.scheme != nil && url.host != nil
        print("GameToolsView: URL validation result: \(isValid) for URL: \(url)")
        return isValid
    }
}

// MARK: - Developer Debug Tools View
struct DeveloperDebugToolsView: View {
    @StateObject private var classroomViewModel = ClassroomViewModel()
    @StateObject private var userManager = UserManager.shared
    @State private var debugLog: [String] = []
    @State private var isRunningDebug = false
    @State private var testSchoolId = ""
    @State private var diagnosticResults = ""
    @State private var latexTestInput: String = ""
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 20) {
                Group {
                    // Header
                    HStack {
                        Image(systemName: "wrench.and.screwdriver.fill")
                            .font(.title2)
                            .foregroundColor(.orange)
                        Text("Authentication & API Debug Tools")
                            .font(.title2)
                            .fontWeight(.bold)
                        Spacer()
                    }
                    .padding()

                    // Current User Status
                    VStack(alignment: .leading, spacing: 8) {
                    Text("Current User Status")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    InfoRow(label: "Logged In", value: "\(userManager.isLoggedIn)")
                    InfoRow(label: "User ID", value: userManager.currentUser.id.isEmpty ? "Empty" : userManager.currentUser.id)
                    InfoRow(label: "Username", value: userManager.currentUser.username.isEmpty ? "Empty" : userManager.currentUser.username)
                    InfoRow(label: "Role", value: userManager.currentUser.role.isEmpty ? "Empty" : userManager.currentUser.role)
                    InfoRow(label: "School ID", value: userManager.currentUser.school_id?.isEmpty == false ? userManager.currentUser.school_id! : "Missing/Empty")
                    
                    if let authHeader = userManager.getAuthorizationHeader() {
                        InfoRow(label: "Auth Token", value: "Available (\(authHeader["Authorization"]?.count ?? 0) chars)")
                    } else {
                        InfoRow(label: "Auth Token", value: "❌ Missing")
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                }

                Group {
                    // Authentication Diagnostics Section
                    Section("🔐 Authentication Diagnostics") {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Diagnose and fix authentication issues")
                            .font(.headline)
                        
                        HStack {
                            Button("🔍 Run Auth Diagnostics") {
                                Task {
                                    await runAuthenticationDiagnostics()
                                }
                            }
                            .buttonStyle(.bordered)
                            
                            Button("🔧 Fix Auth Issues") {
                                Task {
                                    await fixAuthenticationIssues()
                                }
                            }
                            .buttonStyle(.borderedProminent)
                        }


                        
                        if !diagnosticResults.isEmpty {
                            ScrollView {
                                Text(diagnosticResults)
                                    .font(.system(.caption, design: .monospaced))
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    .padding()
                                    .background(Color(.systemGray6))
                                    .cornerRadius(8)
                            }
                            .frame(maxHeight: 300)
                        }
                    }
                }
                
                // Debug Actions
                VStack(alignment: .leading, spacing: 12) {
                    Text("Debug Actions")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Button(action: runAuthenticationDebug) {
                        HStack {
                            Image(systemName: "checkmark.shield.fill")
                            Text("Run Authentication Debug")
                            if isRunningDebug {
                                Spacer()
                                ProgressView()
                                    .scaleEffect(0.8)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .disabled(isRunningDebug)
                    

                    
                    Button(action: refreshUserProfile) {
                        HStack {
                            Image(systemName: "person.crop.circle.fill")
                            Text("Refresh User Profile")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.green)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                    .disabled(isRunningDebug)
                    
                    Button(action: clearDebugLog) {
                        HStack {
                            Image(systemName: "trash.fill")
                            Text("Clear Debug Log")
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(8)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                }

                Group {
                    // Manual School ID Test
                    VStack(alignment: .leading, spacing: 12) {
                    Text("Manual School ID Test")
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    TextField("Enter School ID (e.g., 64f1a2b3c4d5e6f7890abcde)", text: $testSchoolId)
                        .textFieldStyle(.roundedBorder)
                    

                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                
                // Debug Log
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("Debug Log")
                            .font(.headline)
                            .foregroundColor(.primary)
                        Spacer()
                        Text("\(debugLog.count) entries")
                            .font(.caption)
                            .foregroundColor(.gray)
                    }
                    
                    if debugLog.isEmpty {
                        Text("No debug information yet. Run a debug action to see detailed logs.")
                            .font(.caption)
                            .foregroundColor(.gray)
                            .italic()
                    } else {
                        ScrollView {
                            LazyVStack(alignment: .leading, spacing: 4) {
                                ForEach(debugLog.indices.reversed(), id: \.self) { index in
                                    HStack(alignment: .top) {
                                        Text("\(debugLog.count - index).")
                                            .font(.caption2)
                                            .foregroundColor(.gray)
                                            .frame(width: 20, alignment: .trailing)
                                        
                                        Text(debugLog[index])
                                            .font(.caption)
                                            .foregroundColor(.primary)
                                            .textSelection(.enabled)
                                        
                                        Spacer()
                                    }
                                }
                            }
                            .padding(8)
                        }
                        .frame(maxHeight: 300)
                        .background(Color(.systemBackground))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
                }
            }
            
            // MARK: - LaTeX to Unicode Conversion Testing
            Section("LaTeX to Unicode Conversion") {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Test the comprehensive LaTeX-to-Unicode conversion system")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    HStack {
                        Text("LaTeX Input:")
                            .font(.caption)
                            .foregroundColor(.gray)
                        Spacer()
                    }
                    
                    TextField("Enter LaTeX expression", text: $latexTestInput)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .font(.system(.body, design: .monospaced))
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Unicode Output:")
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        Text(latexTestInput.convertingLatexToUnicode())
                            .font(.title2)
                            .padding()
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(Color(.systemGray6))
                            .cornerRadius(8)
                        
                        Text("Preview (truncated):")
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        Text(latexTestInput.latexToUnicodePreview)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .padding(.horizontal)
                    }
                    
                    HStack {
                        Button("Clear") {
                            latexTestInput = ""
                        }
                        .buttonStyle(.bordered)


                        
                        Spacer()
                        
                        Menu("Sample LaTeX") {
                            Button("Basic: $\\alpha + \\beta = \\gamma$") {
                                latexTestInput = "$\\alpha + \\beta = \\gamma$"
                            }
                            Button("Geometry: $75^{\\circ} + 105^{\\circ} = 180^{\\circ}$") {
                                latexTestInput = "$75^{\\circ} + 105^{\\circ} = 180^{\\circ}$"
                            }
                            Button("Angle Question: Lines RS and TU intersect at V. If ∠ RVT = (4x + 5)° and ∠ SVU = (6x - 25)°") {
                                latexTestInput = "Lines RS and TU intersect at V. If ∠ RVT = (4x + 5)° and ∠ SVU = (6x - 25)°, what is the measure of ∠ RVT?"
                            }
                            Button("Complex: $\\frac{\\pi}{2} \\times \\sqrt{2} \\approx 2.22$") {
                                latexTestInput = "$\\frac{\\pi}{2} \\times \\sqrt{2} \\approx 2.22$"
                            }
                            Button("Calculus: $\\int_{0}^{\\infty} e^{-x} dx = 1$") {
                                latexTestInput = "$\\int_{0}^{\\infty} e^{-x} dx = 1$"
                            }
                            Button("Set Theory: $A \\cup B \\neq \\emptyset$") {
                                latexTestInput = "$A \\cup B \\neq \\emptyset$"
                            }

                            Divider()

                            // Currency pattern test cases
                            Button("Currency Test 1: $\\$800$ smartphone") {
                                latexTestInput = "A smartphone originally priced at $\\$800$ is on sale with a $15\\%$ discount. What is the final price of the smartphone after the discount?"
                            }
                            Button("Currency Test 2: $\\$50$ headphones") {
                                latexTestInput = "A pair of headphones originally cost $\\$50$. During a sale, they are sold for $\\$40$. What is the percentage discount?"
                            }
                            Button("Currency Test 3: Mixed $\\$120$ and $25\\%$") {
                                latexTestInput = "An item costs $\\$120$ with a $25\\%$ discount applied."
                            }
                        }
                        .buttonStyle(.bordered)
                    }
                    
                    // Show if LaTeX is detected
                    if latexTestInput.containsLaTeX {
                        Label("LaTeX expressions detected", systemImage: "checkmark.circle.fill")
                            .font(.caption)
                            .foregroundColor(.green)
                    } else if !latexTestInput.isEmpty {
                        Label("No LaTeX expressions detected", systemImage: "info.circle")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                }
                .padding(.vertical, 8)
            }

            // Test math expression rendering
            Section("Math Expression Testing") {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Testing Geometry Expressions:")
                        .font(.headline)
                    
                    // Test the problematic geometry question
                    let testQuestion = "Lines RS and TU intersect at V. If ∠ RVT = (4x + 5)° and ∠ SVU = (6x - 25)°, what is the measure of ∠ RVT?"
                    let testQuestion2 = "Lines EF and GH intersect at point I. If ∠ EIG = (2y + 15)° and ∠ EIH = (3y - 5)°, find the value of y."
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Original Question:")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                        
                        Text(testQuestion)
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        
                        Text("Second Test Question:")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                        
                        Text(testQuestion2)
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                        
                        Text("LaTeX Processing Test:")
                            .font(.subheadline)
                            .foregroundColor(.gray)
                        
                        // Test MarkdownMathRenderer (the one actually used by QuestionTextRenderer)
                        let renderer = MarkdownMathRenderer.shared
                        let segments1 = renderer.parseContent(testQuestion)
                        let segments2 = renderer.parseContent(testQuestion2)
                        
                        Text("MarkdownMathRenderer segments (Question 1):")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(segments1) { segment in
                                HStack {
                                    Text(segment.type == .markdown ? "MD:" : "MATH:")
                                        .font(.system(.caption, design: .monospaced))
                                        .foregroundColor(segment.type == .markdown ? .gray : .green)
                                    Text(segment.content)
                                        .font(.system(.caption, design: .monospaced))
                                }
                                .padding(4)
                                .background(segment.type == .markdown ? Color.gray.opacity(0.1) : Color.green.opacity(0.1))
                                .cornerRadius(4)
                            }
                        }
                        
                        Text("MarkdownMathRenderer segments (Question 2):")
                            .font(.caption)
                            .foregroundColor(.blue)
                        
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(segments2) { segment in
                                HStack {
                                    Text(segment.type == .markdown ? "MD:" : "MATH:")
                                        .font(.system(.caption, design: .monospaced))
                                        .foregroundColor(segment.type == .markdown ? .gray : .green)
                                    Text(segment.content)
                                        .font(.system(.caption, design: .monospaced))
                                }
                                .padding(4)
                                .background(segment.type == .markdown ? Color.gray.opacity(0.1) : Color.green.opacity(0.1))
                                .cornerRadius(4)
                            }
                        }
                    }
                    
                    // Test individual components
                    let testExpressions = [
                        "∠ RVT = (4x + 5)°",
                        "∠ SVU = (6x - 25)°",
                        "∠ EIG = (2y + 15)°",
                        "∠ EIH = (3y - 5)°",
                        "∠ ABC",
                        "(4x + 5)°",
                        "(2y + 15)°",
                        "90°"
                    ]
                    
                    ForEach(testExpressions, id: \.self) { expression in
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Expression: \(expression)")
                                .font(.caption)
                                .foregroundColor(.gray)
                            
                            let segments = MarkdownMathRenderer.shared.parseContent(expression)
                            HStack {
                                Text("Segments:")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                ForEach(segments) { segment in
                                    Text("\(segment.type == .markdown ? "MD" : "MATH")")
                                        .font(.system(.caption, design: .monospaced))
                                        .padding(2)
                                        .background(segment.type == .markdown ? Color.gray.opacity(0.2) : Color.green.opacity(0.2))
                                        .cornerRadius(2)
                                }
                            }
                        }
                    }
                }
            }

            // MARK: - Currency Pattern Testing
            Section("💰 Currency Pattern Testing") {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Test currency pattern handling and LaTeX detection")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Text("Currency test functionality removed")
                        .font(.caption)
                        .foregroundColor(.gray)
                        .italic()
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
        }
        .padding()
    }
    
    private func addDebugLog(_ message: String) {
        DispatchQueue.main.async {
            let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
            debugLog.append("[\(timestamp)] \(message)")
        }
    }
    
    private func runAuthenticationDebug() {
        guard !isRunningDebug else { return }
        isRunningDebug = true
        addDebugLog("Starting comprehensive authentication debug...")
        
        Task {
            let success = await classroomViewModel.debugAndFixAuthentication()
            addDebugLog("Authentication debug completed. Result: \(success ? "✅ SUCCESS" : "❌ FAILED")")
            
            DispatchQueue.main.async {
                isRunningDebug = false
            }
        }
    }
    

    
    private func refreshUserProfile() {
        guard !isRunningDebug else { return }
        isRunningDebug = true
        addDebugLog("Refreshing user profile...")
        
        Task {
            let success = await userManager.fetchUserProfile()
            addDebugLog("User profile refresh completed. Result: \(success ? "✅ SUCCESS" : "❌ FAILED")")
            
            if success {
                addDebugLog("Updated school_id: \(userManager.currentUser.school_id ?? "still nil")")
            }
            
            DispatchQueue.main.async {
                isRunningDebug = false
            }
        }
    }
    

    
    private func clearDebugLog() {
        debugLog.removeAll()
        addDebugLog("Debug log cleared")
    }
    
    // MARK: - Authentication Diagnostic Functions
    
    private func runAuthenticationDiagnostics() {
        diagnosticResults = "🔍 Running comprehensive authentication diagnostics...\n\n"
        
        Task {
            let userManager = UserManager.shared
            
            // Step 1: Basic state check
            diagnosticResults += "📋 STEP 1: Basic Authentication State\n"
            diagnosticResults += "   - Is logged in: \(userManager.isLoggedIn)\n"
            diagnosticResults += "   - User ID: '\(userManager.currentUser.id)'\n"
            diagnosticResults += "   - Username: '\(userManager.currentUser.username)'\n"
            diagnosticResults += "   - Role: '\(userManager.currentUser.role)'\n"
            diagnosticResults += "   - School ID: '\(userManager.currentUser.school_id ?? "nil")'\n\n"
            
            // Step 2: Token availability
            diagnosticResults += "📋 STEP 2: Token Status\n"
            if let authHeaders = userManager.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    let tokenPreview = value.replacingOccurrences(of: "Bearer ", with: "").prefix(20)
                    diagnosticResults += "   ✅ \(key): Bearer \(tokenPreview)...\n"
                }
            } else {
                diagnosticResults += "   ❌ No authorization header available\n"
            }
            
            // Step 3: UserDefaults check
            diagnosticResults += "\n📋 STEP 3: UserDefaults Token Check\n"
            if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String] {
                diagnosticResults += "   - AccessToken length: \(savedTokens["accessToken"]?.count ?? 0)\n"
                diagnosticResults += "   - RefreshToken length: \(savedTokens["refreshToken"]?.count ?? 0)\n"
                if let accessToken = savedTokens["accessToken"], accessToken.count > 20 {
                    diagnosticResults += "   - AccessToken prefix: \(accessToken.prefix(20))...\n"
                }
            } else {
                diagnosticResults += "   ❌ No tokens found in UserDefaults\n"
            }
            
            // Step 4: Test auth validation
            diagnosticResults += "\n📋 STEP 4: Authentication Validation Test\n"
            let authValid = await userManager.ensureValidAuthentication()
            diagnosticResults += "   - Validation result: \(authValid ? "✅ VALID" : "❌ INVALID")\n"
            
            // Step 5: Test API call
            diagnosticResults += "\n📋 STEP 5: Test API Call\n"
            do {
                let _: User = try await userManager.api.get("user/profile")
                diagnosticResults += "   ✅ Profile API call successful - authentication working!\n"
            } catch {
                diagnosticResults += "   ❌ Profile API call failed: \(error.localizedDescription)\n"
                if let apiError = error as? APIError {
                    diagnosticResults += "   - APIError type: \(apiError)\n"
                }
            }
            
            diagnosticResults += "\n🏁 DIAGNOSTICS COMPLETE\n"
        }
    }
    
    private func fixAuthenticationIssues() {
        diagnosticResults = "🔧 Attempting to fix authentication issues...\n\n"
        
        Task {
            let userManager = UserManager.shared
            
            // Step 1: Ensure valid authentication
            diagnosticResults += "🔄 Running authentication fix...\n"
            let authFixed = await userManager.ensureValidAuthentication()
            
            if authFixed {
                diagnosticResults += "✅ Authentication successfully fixed!\n\n"
                
                // Step 2: Update user profile
                diagnosticResults += "🔄 Fetching updated user profile...\n"
                let profileSuccess = await userManager.fetchUserProfile()
                
                if profileSuccess {
                    diagnosticResults += "✅ User profile updated successfully!\n"
                    diagnosticResults += "   - School ID: \(userManager.currentUser.school_id ?? "still nil")\n\n"
                } else {
                    diagnosticResults += "❌ Failed to update user profile\n\n"
                }
                
                diagnosticResults += "🎉 Authentication issues resolved! You can now try loading school students.\n"
            } else {
                diagnosticResults += "❌ Could not fix authentication. You may need to log out and log back in.\n"
            }
        }
    }
    

}

// Helper view for info rows
struct InfoRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label + ":")
                .font(.caption)
                .foregroundColor(.gray)
                .frame(width: 80, alignment: .leading)
            
            Text(value)
                .font(.caption)
                .foregroundColor(.primary)
                .textSelection(.enabled)
            
            Spacer()
        }
    }
}

struct DeveloperInteractiveToolsView_Previews: PreviewProvider {
    static var previews: some View {
        DeveloperInteractiveToolsView()
    }
} 
