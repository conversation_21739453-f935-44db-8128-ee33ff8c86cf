//
//  CourseView.swift
//  Luminous Education
//
//  Created by <PERSON> on 31/7/2024.
//

import SwiftUI
import AVKit

// Use typealias for Lesson and Course if defined in another file
// This will help with development since we don't have access to the full codebase structure

struct CourseView: View {
    let course: Course
    @EnvironmentObject var coursesViewModel: CoursesViewModel
    @State private var navigationSelection: String?
    @State private var selectedLesson: Lesson?
    
    var body: some View {
        VStack{
            Group{
                HStack{
                    Text(course.name)
                        .font(.largeTitle)
                        .bold()
                        .padding(.top)
                    Spacer()
                    CircularStatusIndicator(
                        progress: CGFloat(coursesViewModel.getProgressForCourse(courseID: course.courseID)) / CGFloat(course.total),
                        statusColor: .blue
                    )
                    .padding(.trailing)
                }
                
                Text(course.description)
                    .font(.subheadline)
                    .foregroundColor(.gray)
                    .padding(.top, 5)
                
                // Continue Watching Section - Find the next lesson to watch based on progress
                if let nextLesson = findNextLesson() {
                    Text("Continue Watching: \(nextLesson.name)")
                        .font(.title2)
                        .bold()
                        .padding(.top)
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.leading)
            
            // Picture Placeholder instead of Video Player
            ZStack {
                if let nextLesson = findNextLesson() {
                    Button(action: {
                        // Set selected lesson to the next recommended lesson and navigate
                        selectedLesson = nextLesson
                        // Navigate to VideoView when tapped
                        navigationSelection = "videoView"
                    }) {
                        if !nextLesson.image.isEmpty {
                            // Using direct URL from the API response
                            AsyncImage(url: URL(string: nextLesson.image)) { phase in
                                switch phase {
                                case .empty:
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.3))
                                        .overlay(
                                            ProgressView()
                                                .progressViewStyle(CircularProgressViewStyle())
                                                .scaleEffect(1.5)
                                        )
                                case .success(let image):
                                    image
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                case .failure:
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.3))
                                        .overlay(
                                            Image(systemName: "photo")
                                                .font(.largeTitle)
                                                .foregroundColor(.white)
                                        )
                                @unknown default:
                                    EmptyView()
                                }
                            }
                        } else {
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .overlay(
                                    Image(systemName: "play.circle.fill")
                                        .font(.system(size: 60))
                                        .foregroundColor(.white)
                                )
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                    .frame(height: 350)
                    .cornerRadius(10)
                    .padding()
                    .overlay(
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 70))
                            .foregroundColor(.white)
                            .shadow(radius: 5)
                    )
                } else {
                    // No lesson available
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .frame(height: 350)
                        .cornerRadius(10)
                        .padding()
                        .overlay(
                            Text("No Lessons Available")
                                .font(.title)
                                .foregroundColor(.gray)
                        )
                }
            }
            .navigationDestination(isPresented: Binding<Bool>(
                get: { navigationSelection == "videoView" },
                set: { 
                    if !$0 { 
                        navigationSelection = nil
                        selectedLesson = nil  // Clear the selected lesson when navigation is reset
                    } 
                }
            )) {
                if let lesson = selectedLesson {
                    // Use the specifically selected lesson if available
                    VideoView(
                        videoURL: lesson.videoLink,
                        lessonName: lesson.name
                    )
                } else if let nextLesson = findNextLesson() {
                    // Fall back to the recommended next lesson if no specific selection
                    VideoView(
                        videoURL: nextLesson.videoLink,
                        lessonName: nextLesson.name
                    )
                } else {
                    VideoView() // Fallback with default values
                }
            }
            
            // Upcoming Lessons Section
            HStack {
                VStack {
                    HStack {
                        Text("Upcoming Lessons")
                            .font(.title2)
                            .bold()
                        Spacer()
                        Button(action: {
                            print("View All tapped")
                        }) {
                            Text("View All")
                                .font(.headline)
                                .foregroundColor(.blue)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            ForEach(course.lessons.prefix(3)) { lesson in
                                LessonCard(lesson: lesson, onTap: {
                                    // Set the selected lesson and navigate to video view
                                    selectedLesson = lesson
                                    navigationSelection = "videoView"
                                })
                            }
                        }
                    }
                }
                .padding(.horizontal)
            }
        }
        .padding()
    }
    
    // Helper method to find the next lesson to watch based on progress
    private func findNextLesson() -> Lesson? {
        return coursesViewModel.findNextLessonToStart(courseID: course.courseID, lessons: course.lessons)
    }
}

struct LessonCard: View {
    let lesson: Lesson
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading) {
                if !lesson.image.isEmpty {
                    // Using direct URL from the API response
                    AsyncImage(url: URL(string: lesson.image)) { phase in
                        switch phase {
                        case .success(let image):
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        case .empty, .failure:
                            Rectangle()
                                .fill(Color.gray.opacity(0.3))
                                .overlay(
                                    Image(systemName: "photo")
                                        .foregroundColor(.gray)
                                )
                        @unknown default:
                            EmptyView()
                        }
                    }
                    .frame(width: 250, height: 120)
                    .clipped()
                    .cornerRadius(10)
                    .onAppear {
                        if !lesson.image.isEmpty {
                            print("Image URL:", lesson.image)
                        }
                    }
                } else {
                    Rectangle()
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 250, height: 120)
                        .cornerRadius(10)
                        .overlay(
                            Image(systemName: "play.circle.fill")
                                .font(.system(size: 30))
                                .foregroundColor(.white)
                                .shadow(radius: 3)
                        )
                }
                
                Text(lesson.name)
                    .font(.headline)
                    .lineLimit(1)
                
                Text("\(lesson.duration) min")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .frame(width: 250)
            .padding(5)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct CircularStatusIndicator: View {
    var progress: CGFloat // Value between 0 and 1
    var statusColor: Color

    var body: some View {
        let lineWidth:CGFloat = 10
        let size:CGFloat = 50
        
        ZStack {
            // Background circle
            Circle()
                .stroke(Color.gray.opacity(0.3), lineWidth: lineWidth)
                .frame(width: size, height: size)

            // Foreground circle representing progress
            Circle()
                .trim(from: 0, to: progress) // Trim the circle based on progress
                .stroke(statusColor, lineWidth: lineWidth)
                .rotationEffect(.degrees(-90)) // Start from the top
                .frame(width: size, height: size)
                .animation(.easeInOut, value: progress)

            // Status label in the center
            Text("\(Int(progress * 100))%")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.black)
        }
    }
}

struct CourseView_Previews: PreviewProvider {
    static var previews: some View {
        let previewCourse = Course(
            courseId: "MATH101",
            name: "Secondary Maths: Basic Algebra",
            description: "An introductory course focusing on variables, equations, and problem-solving techniques for secondary students.",
            difficulty: 1,
            totalLessons: 2,
            credit: 3,
            image: "",
            lessons: [
                Lesson(
                    lessonID: "L1",
                    name: "Introduction to Algebra",
                    description: "Overview of algebraic concepts and basic operations.",
                    duration: 30,
                    videoLink: "https://example.com/algebra_intro",
                    image: ""
                )
            ]
        )
        
        CourseView(course: previewCourse)
            .environmentObject(CoursesViewModel())
    }
}
