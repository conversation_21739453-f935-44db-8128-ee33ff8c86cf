import SwiftUI
import Foundation
// Import the CourseDetailView to use it in this file

struct MarketplaceView: View {
    @StateObject private var marketplaceViewModel = MarketplaceViewModel()
    @State private var searchText = ""
    @State private var selectedCategory: String? = nil
    @State private var selectedLevel: Course.CourseLevel? = nil
    @State private var showingFilters = false
    @State private var selectedCourse: Course? = nil
    @State private var showingCourseDetail = false
    @State private var isPurchasing = false
    @State private var showingPurchaseSuccess = false
    @State private var isLoadingCourseDetail = false
    
    // Categories derived from available courses
    private var categories: [String] {
        let allCategories = marketplaceViewModel.courses.map { $0.category }
        return Array(Set(allCategories)).sorted()
    }
    
    // Filtered courses based on search and filters
    private var filteredCourses: [Course] {
        var result = marketplaceViewModel.courses
        
        // Apply search filter
        if !searchText.isEmpty {
            result = result.filter { 
                $0.title.lowercased().contains(searchText.lowercased()) ||
                $0.description.lowercased().contains(searchText.lowercased())
            }
        }
        
        // Apply category filter
        if let category = selectedCategory {
            result = result.filter { $0.category == category }
        }
        
        // Apply level filter
        if let level = selectedLevel {
            result = result.filter { $0.level == level }
        }
        
        return result
    }
    
    var body: some View {
//        NavigationView {
            ZStack {
                Color(UIColor.systemBackground)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // Search bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.gray)
                        
                        TextField("Search courses", text: $searchText)
                            .autocapitalization(.none)
                            .disableAutocorrection(true)
                        
                        if !searchText.isEmpty {
                            Button(action: {
                                searchText = ""
                            }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.gray)
                            }
                        }
                    }
                    .padding()
                    .background(Color(UIColor.systemGray6))
                    .cornerRadius(10)
                    .padding(.horizontal)
                    .padding(.top)
                    
                    // Filter buttons
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 10) {
                            // All categories button
                            FilterButton(
                                title: "All",
                                isSelected: selectedCategory == nil,
                                action: { selectedCategory = nil }
                            )
                            
                            // Category buttons
                            ForEach(categories, id: \.self) { category in
                                FilterButton(
                                    title: category,
                                    isSelected: selectedCategory == category,
                                    action: { selectedCategory = category }
                                )
                            }
                            
                            // Level filter button
                            Menu {
                                Button("All Levels") {
                                    selectedLevel = nil
                                }
                                
                                ForEach(Course.CourseLevel.allCases) { level in
                                    Button(level.rawValue) {
                                        selectedLevel = level
                                    }
                                }
                            } label: {
                                HStack {
                                    Text(selectedLevel?.rawValue ?? "Level")
                                    Image(systemName: "chevron.down")
                                        .font(.caption)
                                }
                                .padding(.horizontal, 12)
                                .padding(.vertical, 8)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(selectedLevel != nil ? Color.blue.opacity(0.2) : Color(UIColor.systemGray6))
                                )
                                .foregroundColor(selectedLevel != nil ? .blue : .primary)
                            }
                        }
                        .padding(.horizontal)
                        .padding(.vertical, 10)
                    }
                    
                    // Course list
                    if marketplaceViewModel.isLoading {
                        Spacer()
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(1.5)
                        Spacer()
                    } else if filteredCourses.isEmpty {
                        Spacer()
                        VStack(spacing: 20) {
                            Image(systemName: "magnifyingglass")
                                .font(.system(size: 50))
                                .foregroundColor(.gray)
                            
                            Text("No courses found")
                                .font(.title2)
                                .fontWeight(.medium)
                            
                            Text("Try adjusting your search or filters")
                                .foregroundColor(.gray)
                        }
                        Spacer()
                    } else {
                        ScrollView {
                            LazyVStack(spacing: 16) {
                                ForEach(filteredCourses) { course in
                                    MarketplaceCourseCard(course: course)
                                        .environmentObject(marketplaceViewModel)
                                        .onTapGesture {
                                            isLoadingCourseDetail = true
                                            selectedCourse = course
                                            showingCourseDetail = true
                                        }
                                }
                            }
                            .padding()
                        }
                    }
                }
                .navigationTitle("Course Marketplace")
                .sheet(isPresented: $showingCourseDetail, onDismiss: {
                    isLoadingCourseDetail = false
                    isPurchasing = false
                }) {
                    if let course = selectedCourse {
                        CourseDetailView(
                            course: course,
                            isPurchasing: $isPurchasing,
                            onPurchase: { purchaseCourse(course) },
                            isLoadingCourseDetail: $isLoadingCourseDetail
                        )
                        .environmentObject(marketplaceViewModel)
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                isLoadingCourseDetail = false
                            }
                        }
                    } else {
                        VStack {
                            ProgressView()
                            Text("Loading course details...")
                                .padding()
                        }
                    }
                }
                .alert(isPresented: $showingPurchaseSuccess) {
                    Alert(
                        title: Text("Purchase Successful"),
                        message: Text("You have successfully enrolled in this course."),
                        dismissButton: .default(Text("OK"))
                    )
                }
                .onAppear {
                    Task {
                        await marketplaceViewModel.fetchAllCourses()
                    }
                }
            }
//        }
    }
    
    private func purchaseCourse(_ course: Course) {
        isPurchasing = true
        
        // Use a local reference to make it clearer to the compiler
        let viewModel = self.marketplaceViewModel
        
        Task {
            // Call the method on the local reference
            let result = await viewModel.purchaseCourse(course)
            
            // Update UI on the main thread
            await MainActor.run {
                isPurchasing = false
                
                switch result {
                case .success:
                    showingCourseDetail = false
                    showingPurchaseSuccess = true
                case .failure(let error):
                    print("Purchase failed: \(error.localizedDescription)")
                    // Handle error
                }
            }
        }
    }
}

// Filter button component
struct FilterButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.blue.opacity(0.2) : Color(UIColor.systemGray6))
                )
                .foregroundColor(isSelected ? .blue : .primary)
        }
    }
}

// Course card component
struct MarketplaceCourseCard: View {
    let course: Course
    @EnvironmentObject private var marketplaceViewModel: MarketplaceViewModel
    
    // Get appropriate SF Symbol based on course category
    private var categorySymbol: String {
        switch course.category.lowercased() {
        case "mathematics":
            return "function"
        case "english":
            return "text.book.closed"
        case "science":
            return "atom"
        case "chinese":
            return "character"
        default:
            return "book.fill"
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Course image
            ZStack(alignment: .topTrailing) {
                Image(systemName: categorySymbol)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(height: 120)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(12)
                
                if course.isEnrolled {
                    Text("Enrolled")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .padding(.horizontal, 10)
                        .padding(.vertical, 5)
                        .background(Color.green)
                        .cornerRadius(8)
                        .padding(8)
                }
            }
            
            // Course info
            VStack(alignment: .leading, spacing: 8) {
                Text(course.title)
                    .font(.headline)
                    .lineLimit(2)
                
                HStack {
                    // Level
                    Text(course.level.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.blue.opacity(0.1))
                        )
                    
                    // Lessons count
                    Text("\(course.totalLessons) lessons")
                        .font(.caption)
                        .foregroundColor(.gray)
 
                    Spacer()
                    Text("\(course.credit) Credits")
                        .font(.headline)
                        .foregroundColor(.blue)
                }
            }
        }
        .padding()
        .background(Color(UIColor.systemBackground))
        .cornerRadius(12)
        .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct MarketplaceView_Previews: PreviewProvider {
    static var previews: some View {
        MarketplaceView()
    }
} 
