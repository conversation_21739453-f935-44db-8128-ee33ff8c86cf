//
//  UserProfileView.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import SwiftUI

struct UserProfileView: View {
    @ObservedObject private var userManager = UserManager.shared
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    
    @State private var showingLogoutAlert = false
    @State private var showingEditProfile = false
    @State private var isRefreshingUserInfo = false
    @State private var navigationDestination: NavigationDestination?
    
    enum NavigationDestination: Hashable {
        case about
        case help
        case privacy
        case changePassword
    }
    
    @State private var showDownloadDataAlert = false
    @State private var showDeleteAccountAlert = false
    @State private var showDataDownloaded = false
    @State private var showAccountDeleted = false
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 0) {
                    headerSection
                    profileInfoSection
                    accountSettingsSection
                    actionsSection
                }
            }
            .background(backgroundColor)
            .navigationTitle("Profile")
            .navigationBarTitleDisplayMode(.inline)
            .navigationDestination(for: NavigationDestination.self) { destination in
                switch destination {
                case .about:
                    AboutView()
                case .help:
                    HelpSupportView()
                case .privacy:
                    PrivacySettingsView()
                case .changePassword:
                    ChangePasswordView()
                }
            }
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack {
                        Button(action: {
                            refreshUserInfo()
                        }) {
                            Image(systemName: "arrow.clockwise")
                                .rotationEffect(.degrees(isRefreshingUserInfo ? 360 : 0))
                                .animation(isRefreshingUserInfo ? Animation.linear(duration: 1).repeatForever(autoreverses: false) : .default, value: isRefreshingUserInfo)
                        }
                        .disabled(isRefreshingUserInfo || userManager.currentUser.isGuest)
                        
                        Button("Edit") {
                            showingEditProfile = true
                        }
                        .disabled(userManager.currentUser.isGuest)
                    }
                }
            }
        }
        .sheet(isPresented: $showingEditProfile) {
            EditProfileView()
        }
        .alert("Logout", isPresented: $showingLogoutAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Logout", role: .destructive) {
                userManager.logout()
                dismiss()
            }
        } message: {
            Text("Are you sure you want to logout?")
        }
        .onAppear {
            // Refresh user info when view appears
            refreshUserInfo()
        }
    }
    
    @ViewBuilder
    private var headerSection: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(profileImageBackgroundColor)
                    .frame(width: 120, height: 120)
                
                Image(systemName: "person.fill")
                    .font(.system(size: 60))
                    .foregroundColor(.white)
            }
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
            
            VStack(spacing: 4) {
                Text(displayName)
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                RoleBadge(role: userManager.currentUser.role)
            }
        }
        .padding(.vertical, 30)
        .frame(maxWidth: .infinity)
        .background(headerBackgroundColor)
    }
    
    @ViewBuilder
    private var profileInfoSection: some View {
        VStack(spacing: 0) {
            SectionHeader(title: "Profile Information")
                .padding(.top, 16)
            
            VStack(spacing: 0) {
                ProfileInfoRow(
                    icon: "person.fill",
                    title: "Username",
                    value: userManager.currentUser.username,
                    iconColor: .blue
                )
                
                ProfileInfoRow(
                    icon: "textformat.abc",
                    title: "Display Name",
                    value: userManager.currentUser.name.isEmpty ? "Not set" : userManager.currentUser.name,
                    iconColor: .green
                )
                
                if let email = userManager.currentUser.email {
                    ProfileInfoRow(
                        icon: "envelope.fill",
                        title: "Email",
                        value: email,
                        iconColor: .orange
                    )
                }
                
                ProfileInfoRow(
                    icon: "creditcard.fill",
                    title: "Credits",
                    value: "\(userManager.currentUser.credit)",
                    iconColor: .purple,
                    showDivider: false
                )
            }
            .background(cardBackgroundColor)
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 20)
    }
    
    @ViewBuilder
    private var accountSettingsSection: some View {
        VStack(spacing: 0) {
            SectionHeader(title: "Account")
            
            VStack(spacing: 0) {
                if !userManager.currentUser.isGuest {
                    NavigationLink(value: NavigationDestination.changePassword) {
                        AccountSettingsRow(
                            icon: "key.fill",
                            title: "Change Password",
                            iconColor: .orange,
                            action: nil
                        )
                    }
                }
                
                AccountSettingsRow(
                    icon: "bell.fill",
                    title: "Notifications",
                    iconColor: .purple,
                    action: {
                        print("Notifications tapped")
                    }
                )
                
                NavigationLink(value: NavigationDestination.privacy) {
                    AccountSettingsRow(
                        icon: "shield.fill",
                        title: "Privacy Settings",
                        iconColor: .indigo,
                        action: nil,
                        showDivider: false
                    )
                }
            }
            .background(cardBackgroundColor)
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 20)
    }
    
    @ViewBuilder
    private var actionsSection: some View {
        VStack(spacing: 0) {
            SectionHeader(title: "Actions")
            
            VStack(spacing: 0) {
                NavigationLink(value: NavigationDestination.help) {
                    AccountSettingsRow(
                        icon: "questionmark.circle.fill",
                        title: "Help & Support",
                        iconColor: .cyan,
                        action: nil
                    )
                }
                
                NavigationLink(value: NavigationDestination.about) {
                    AccountSettingsRow(
                        icon: "info.circle.fill",
                        title: "About",
                        iconColor: .gray,
                        action: nil
                    )
                }
                
                Button(action: {
                    showingLogoutAlert = true
                }) {
                    HStack(spacing: 12) {
                        ZStack {
                            Circle()
                                .fill(Color.red.opacity(0.1))
                                .frame(width: 32, height: 32)
                            
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.red)
                        }
                        
                        Text("Logout")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.red)
                        
                        Spacer()
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .background(cardBackgroundColor)
            .cornerRadius(12)
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 30)
    }
    
    private var displayName: String {
        if userManager.currentUser.isGuest {
            return "Guest User"
        } else if userManager.currentUser.name.isEmpty {
            return userManager.currentUser.username
        } else {
            return userManager.currentUser.name
        }
    }
    
    private var backgroundColor: Color {
        colorScheme == .dark ? Color.black : Color(.systemGroupedBackground)
    }
    
    private var headerBackgroundColor: Color {
        colorScheme == .dark ? Color(.systemGray6) : Color.white
    }
    
    private var cardBackgroundColor: Color {
        colorScheme == .dark ? Color(.systemGray6) : Color.white
    }
    
    private var profileImageBackgroundColor: Color {
        switch userManager.currentUser.role.lowercased() {
        case "student":
            return .blue
        case "teacher":
            return .green
        case "developer":
            return .purple
        default:
            return .gray
        }
    }
    
    // MARK: - Helper Methods
    
    private func refreshUserInfo() {
        guard !userManager.currentUser.isGuest else { return }
        
        isRefreshingUserInfo = true
        
        Task {
            let success = await userManager.fetchUserProfile()
            
            await MainActor.run {
                self.isRefreshingUserInfo = false
                
                if !success {
                    // Could show an error message here if needed
                    print("Failed to refresh user info")
                }
            }
        }
    }
}

struct RoleBadge: View {
    let role: String
    
    var body: some View {
        Text(role)
            .font(.caption)
            .fontWeight(.semibold)
            .padding(.horizontal, 12)
            .padding(.vertical, 4)
            .background(roleColor.opacity(0.2))
            .foregroundColor(roleColor)
            .cornerRadius(8)
    }
    
    private var roleColor: Color {
        switch role.lowercased() {
        case "student":
            return .blue
        case "teacher":
            return .green
        case "developer":
            return .purple
        default:
            return .gray
        }
    }
}

struct SectionHeader: View {
    let title: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            Spacer()
        }
        .padding(.horizontal, 16)
        .padding(.bottom, 8)
    }
}

struct ProfileInfoRow: View {
    let icon: String
    let title: String
    let value: String
    let iconColor: Color
    var showDivider: Bool = true
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 12) {
                ZStack {
                    Circle()
                        .fill(iconColor.opacity(0.1))
                        .frame(width: 32, height: 32)
                    
                    Image(systemName: icon)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(iconColor)
                }
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.secondary)
                    
                    Text(value)
                        .font(.system(size: 16))
                        .foregroundColor(.primary)
                }
                
                Spacer()
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            
            if showDivider {
                Divider()
                    .padding(.leading, 60)
            }
        }
    }
}

struct AccountSettingsRow: View {
    let icon: String
    let title: String
    let iconColor: Color
    let action: (() -> Void)?
    var showDivider: Bool = true
    
    var body: some View {
        VStack(spacing: 0) {
            if let action = action {
                Button(action: action) {
                    rowContent
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                rowContent
            }
            if showDivider {
                Divider()
                    .padding(.leading, 60)
            }
        }
    }
    
    private var rowContent: some View {
        HStack(spacing: 12) {
            ZStack {
                Circle()
                    .fill(iconColor.opacity(0.1))
                    .frame(width: 32, height: 32)
                Image(systemName: icon)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(iconColor)
            }
            Text(title)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)
            Spacer()
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
    }
}

struct ChangePasswordView: View {
    @ObservedObject private var userManager = UserManager.shared
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.dismiss) private var dismiss

    @State private var oldPassword: String = ""
    @State private var newPassword: String = ""
    @State private var confirmPassword: String = ""
    @State private var isLoading: Bool = false
    @State private var errorMessage: String = ""
    @State private var showingSuccessAlert: Bool = false

    var body: some View {
        ScrollView {
            VStack(spacing: 24) {
                // Header Icon
                Image(systemName: "key.fill")
                    .resizable()
                    .frame(width: 60, height: 80)
                    .foregroundColor(.orange)

                Text("Change Password")
                    .font(.title)
                    .fontWeight(.bold)

                // Password Form
                VStack(spacing: 16) {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Current Password")
                            .font(.headline)
                            .foregroundColor(.primary)
                        SecureField("Enter current password", text: $oldPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .textContentType(.password)
                            .onChange(of: oldPassword) { _ in
                                validatePasswords()
                            }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("New Password")
                            .font(.headline)
                            .foregroundColor(.primary)
                        SecureField("Enter new password", text: $newPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .textContentType(.newPassword)
                            .onChange(of: newPassword) { _ in
                                validatePasswords()
                            }
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("Confirm New Password")
                            .font(.headline)
                            .foregroundColor(.primary)
                        SecureField("Confirm new password", text: $confirmPassword)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .textContentType(.newPassword)
                            .onChange(of: confirmPassword) { _ in
                                validatePasswords()
                            }
                    }
                }
                .padding(.horizontal)

                // Error Message
                if !errorMessage.isEmpty {
                    Text(errorMessage)
                        .foregroundColor(.red)
                        .font(.subheadline)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }

                // Change Password Button
                Button(action: {
                    changePassword()
                }) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        } else {
                            Image(systemName: "key.fill")
                        }
                        Text(isLoading ? "Changing Password..." : "Change Password")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(isFormValid && !isLoading ? Color.orange : Color.gray)
                    .cornerRadius(10)
                }
                .disabled(!isFormValid || isLoading)
                .padding(.horizontal)

                Spacer()
            }
        }
        .padding()
        .navigationTitle("Change Password")
        .navigationBarTitleDisplayMode(.inline)
        .alert("Password Changed", isPresented: $showingSuccessAlert) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("Your password has been changed successfully.")
        }
        .onAppear {
            validatePasswords()
        }
    }

    private var isFormValid: Bool {
        !oldPassword.isEmpty &&
        !newPassword.isEmpty &&
        !confirmPassword.isEmpty &&
        newPassword == confirmPassword &&
        newPassword.count >= 6 &&
        errorMessage.isEmpty
    }

    private func validatePasswords() {
        errorMessage = ""

        if oldPassword.isEmpty {
            errorMessage = "Please enter your current password."
            return
        }

        if !newPassword.isEmpty && newPassword.count < 6 {
            errorMessage = "New password must be at least 6 characters long."
            return
        }

        if !confirmPassword.isEmpty && !newPassword.isEmpty && newPassword != confirmPassword {
            errorMessage = "New password and confirmation do not match."
            return
        }

        // Additional validation: ensure new password is different from old password
        if !oldPassword.isEmpty && !newPassword.isEmpty && oldPassword == newPassword {
            errorMessage = "New password must be different from your current password."
            return
        }
    }

    private func clearForm() {
        oldPassword = ""
        newPassword = ""
        confirmPassword = ""
        errorMessage = ""
    }

    private func changePassword() {
        guard isFormValid else {
            return
        }

        isLoading = true
        errorMessage = ""

        Task {
            do {
                let requestBody = ChangePasswordRequest(
                    old_password: oldPassword,
                    new_password: newPassword,
                    confirm_password: confirmPassword
                )

                let _: ChangePasswordResponse = try await userManager.api.post(
                    "auth/change-password",
                    body: requestBody,
                    responseType: ChangePasswordResponse.self
                )

                await MainActor.run {
                    isLoading = false
                    clearForm()
                    showingSuccessAlert = true
                }

            } catch {
                await MainActor.run {
                    isLoading = false
                    if let apiError = error as? APIError {
                        switch apiError {
                        case .unauthorized:
                            errorMessage = "Old password is incorrect."
                        case .serverError(let message):
                            errorMessage = message
                        case .decodingError(let message):
                            errorMessage = "Response error: \(message)"
                        case .networkError(let message):
                            errorMessage = "Network error: \(message)"
                        default:
                            errorMessage = "Failed to change password. Please try again."
                        }
                    } else {
                        errorMessage = error.localizedDescription
                    }
                }
            }
        }
    }
}

// MARK: - Change Password Models

struct ChangePasswordRequest: Codable {
    let old_password: String
    let new_password: String
    let confirm_password: String
}

struct ChangePasswordResponse: Codable {
    let message: String
}

struct EditProfileView: View {
    @Environment(\.dismiss) private var dismiss
    @ObservedObject private var userManager = UserManager.shared
    @State private var editedName: String = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section("Personal Information") {
                    HStack {
                        Text("Username")
                        Spacer()
                        Text(userManager.currentUser.username)
                            .foregroundColor(.secondary)
                    }
                    
                    TextField("Display Name", text: $editedName)
                }
            }
            .navigationTitle("Edit Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        print("Save profile changes")
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            editedName = userManager.currentUser.name
        }
    }
}

#Preview {
    UserProfileView()
} 

struct AboutView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image("funma-logo")
                .resizable()
                .frame(width: 150, height: 60)
                .clipShape(RoundedRectangle(cornerRadius: 12, style: .continuous))
            Text("FunMA")
                .font(.title)
                .fontWeight(.bold)
            Text("Version \(appVersion)")
                .font(.headline)
                .foregroundColor(.secondary)
            Text("FunMA is an innovative platform for interactive learning and classroom management.")
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            Spacer()
        }
        .padding()
        .navigationTitle("About")
        .navigationBarTitleDisplayMode(.inline)
    }

    private var appVersion: String {
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        return "\(version) (\(build))"
    }
} 

struct HelpSupportView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "questionmark.circle.fill")
                .resizable()
                .frame(width: 60, height: 60)
                .foregroundColor(.cyan)
            Text("Help & Support")
                .font(.title)
                .fontWeight(.bold)
            Text("If you have any questions, feedback, or need assistance, please contact our support team.")
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            VStack(spacing: 8) {
                HStack {
                    Image(systemName: "envelope.fill")
                    Text("<EMAIL>")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
                HStack {
                    Image(systemName: "message.fill")
                    Text("WhatsApp: +852 5797 9439")
                }
                .font(.subheadline)
                .foregroundColor(.secondary)
            }
            Button(action: {
                if let url = URL(string: "mailto:<EMAIL>") {
                    UIApplication.shared.open(url)
                }
            }) {
                HStack {
                    Image(systemName: "envelope.fill")
                    Text("Email Support")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.cyan)
                .cornerRadius(10)
            }
            Button(action: {
                if let url = URL(string: "https://wa.me/85257979439") {
                    UIApplication.shared.open(url)
                }
            }) {
                HStack {
                    Image(systemName: "message.fill")
                    Text("WhatsApp Support")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.green)
                .cornerRadius(10)
            }
            Spacer()
        }
        .padding()
        .navigationTitle("Help & Support")
        .navigationBarTitleDisplayMode(.inline)
    }
} 

struct PrivacySettingsView: View {
    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "shield.fill")
                .resizable()
                .frame(width: 60, height: 60)
                .foregroundColor(.indigo)
            Text("Privacy Settings")
                .font(.title)
                .fontWeight(.bold)
            Text("To download your data or delete your account, please email us at:")
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            HStack {
                Image(systemName: "envelope.fill")
                Text("<EMAIL>")
            }
            .font(.subheadline)
            .foregroundColor(.secondary)
            Button(action: {
                if let url = URL(string: "mailto:<EMAIL>?subject=Data%20Request") {
                    UIApplication.shared.open(url)
                }
            }) {
                HStack {
                    Image(systemName: "envelope.fill")
                    Text("Email Support")
                }
                .font(.headline)
                .foregroundColor(.white)
                .padding()
                .background(Color.indigo)
                .cornerRadius(10)
            }
            Divider().padding(.vertical, 16)
            VStack(spacing: 12) {
                Text("Privacy Policy")
                    .font(.headline)
                    .frame(maxWidth: .infinity, alignment: .leading)
                Text("We are committed to protecting your privacy. Read our full privacy policy to learn how your data is collected, used, and protected.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                Button(action: {
                    if let url = URL(string: "https://www.inspire-edu.hk/privacy-policy") {
                        UIApplication.shared.open(url)
                    }
                }) {
                    HStack {
                        Image(systemName: "doc.text.fill")
                        Text("View Full Privacy Policy")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(Color.indigo)
                    .cornerRadius(10)
                }
            }
            Spacer()
        }
        .padding()
        .navigationTitle("Privacy Settings")
        .navigationBarTitleDisplayMode(.inline)
    }
} 
