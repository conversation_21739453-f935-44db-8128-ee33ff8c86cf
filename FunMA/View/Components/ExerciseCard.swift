import SwiftUI

struct ExerciseCard: View {
    let exercise: Exercise
    @ObservedObject var viewModel: ExerciseViewModel
    let onDelete: () -> Void
    let onViewQuestions: () -> Void
    let onCardTap: () -> Void
    @StateObject private var classroomViewModel = ClassroomViewModel()
    
    // Status computation
    private var status: (text: String, color: Color, icon: String) {
        let now = Date()
        if exercise.dueDate < now {
            return ("Overdue", .red, "exclamationmark.triangle.fill")
        } else if exercise.dueDate.timeIntervalSinceNow < 24 * 60 * 60 { // Due within 24 hours
            return ("Due Soon", .orange, "clock.fill")
        } else if exercise.dueDate.timeIntervalSinceNow < 7 * 24 * 60 * 60 { // Due within 7 days
            return ("Due This Week", .yellow, "calendar")
        } else {
            return ("Active", .green, "checkmark.circle.fill")
        }
    }
    
    // Get classroom names for display
    private var assignedClassroomNames: [String] {
        return classroomViewModel.classrooms
            .filter { exercise.classroomIds.contains($0.id) }
            .map { $0.name }
    }
    
    private var classroomDisplayText: String {
        if assignedClassroomNames.isEmpty {
            return "No classrooms assigned"
        } else if assignedClassroomNames.count == 1 {
            return assignedClassroomNames.first!
        } else {
            return "\(assignedClassroomNames.count) classrooms"
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with title and status
            HStack(alignment: .top, spacing: 12) {
                VStack(alignment: .leading, spacing: 6) {
                    // Title and Status Row
                    HStack {
                        Text(exercise.title)
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                            .lineLimit(2)
                        Spacer()
                        // Status Badge - removed background color
                        HStack(spacing: 4) {
                            Image(systemName: status.icon)
                                .font(.caption)
                            Text(status.text)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(status.color)
                    }
                }
            }
            .padding(.top, 16)
            .padding(.horizontal, 16)
            
            // Divider
            Divider()
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
            
            // Info Section
            VStack(spacing: 8) {
                // Due Date (left)
                HStack {
                    HStack(spacing: 6) {
                        Image(systemName: "clock")
                            .font(.caption)
                            .foregroundColor(.orange)
                        Text("Due: \(exercise.dueDate.formatted(date: .abbreviated, time: .shortened))")
                            .font(.caption)
                            .foregroundColor(.orange)
                    }
                    Spacer()
                }
                // Classroom Assignment (left) - changed to blue color
                HStack(spacing: 6) {
                    Image(systemName: "building.2")
                        .font(.caption)
                        .foregroundColor(.blue)
                    Text(classroomDisplayText)
                        .font(.caption)
                        .foregroundColor(.blue)
                        .lineLimit(1)
                    Spacer()
                }
            }
            .padding(.horizontal, 16)
            
            // Action Buttons
            HStack(spacing: 12) {
                Button(action: onViewQuestions) {
                    HStack(spacing: 4) {
                        Image(systemName: "doc.text.magnifyingglass")
                            .font(.caption)
                        Text("View Questions")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.purple)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.purple.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
                Spacer()
                Button(action: onDelete) {
                    HStack(spacing: 4) {
                        Image(systemName: "trash")
                            .font(.caption)
                        Text("Delete")
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.red)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.horizontal, 16)
            .padding(.bottom, 16)
            .padding(.top, 12)
        }
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.08), radius: 8, x: 0, y: 2)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
        .onTapGesture {
            onCardTap()
        }
        .task {
            // Load classrooms to display names
            if classroomViewModel.classrooms.isEmpty {
                await classroomViewModel.loadClassrooms()
            }
        }
    }
}

#Preview {
    ExerciseCard(
        exercise: Exercise(
            title: "Sample Exercise",
            topic: "Algebra",
            subtopic: "Linear Equations",
            classroomIds: ["classroom1"],
            questions: [],
            createdBy: "teacher1",
            dueDate: Date().addingTimeInterval(86400)
        ),
        viewModel: ExerciseViewModel(),
        onDelete: {},
        onViewQuestions: {},
        onCardTap: {}
    )
    .padding()
}
