import SwiftUI
import PencilKit

// MARK: - Drawing Canvas for Student Calculations and Answers
struct DrawingCanvasView: UIViewRepresentable {
    @Binding var canvasView: PKCanvasView
    @Binding var drawing: PKDrawing
    let isAnswerSubmission: Bool // true for long answers, false for calculations only
    @Binding var selectedTool: PKTool

    // Add a property to store the last non-eraser tool
    class Coordinator: NSObject, PKCanvasViewDelegate, UIPencilInteractionDelegate {
        let parent: DrawingCanvasView
        var lastNonEraserTool: PKTool?
        var isTemporarilyEraser = false

        init(_ parent: DrawingCanvasView) {
            self.parent = parent
            // Default to pen if initial tool is eraser
            if parent.selectedTool is PKEraserTool {
                self.lastNonEraserTool = PKInkingTool(.pen, color: .black, width: 2)
            } else {
                self.lastNonEraserTool = parent.selectedTool
            }
        }

        func canvasViewDrawingDidChange(_ canvasView: PKCanvasView) {
            parent.drawing = canvasView.drawing
        }

        // MARK: - UIPencilInteractionDelegate
        func pencilInteractionDidTap(_ interaction: UIPencilInteraction) {
            let currentTool = parent.selectedTool
            if currentTool is PKEraserTool {
                // If already eraser, switch back to last non-eraser tool
                if let lastTool = lastNonEraserTool {
                    parent.selectedTool = lastTool
                    parent.canvasView.tool = lastTool
                }
            } else {
                // Store current tool and switch to eraser
                lastNonEraserTool = currentTool
                let eraser = PKEraserTool(.bitmap)
                parent.selectedTool = eraser
                parent.canvasView.tool = eraser
            }
        }
    }

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    func makeUIView(context: Context) -> PKCanvasView {
        canvasView.drawing = drawing
        canvasView.delegate = context.coordinator
        canvasView.tool = selectedTool
        canvasView.drawingPolicy = .anyInput
        canvasView.backgroundColor = UIColor.systemBackground

        // Add a subtle border
        canvasView.layer.borderWidth = 1
        canvasView.layer.borderColor = UIColor.systemGray4.cgColor
        canvasView.layer.cornerRadius = 8

        // Add Apple Pencil double-tap support
        let pencilInteraction = UIPencilInteraction()
        pencilInteraction.delegate = context.coordinator
        canvasView.addInteraction(pencilInteraction)

        return canvasView
    }

    func updateUIView(_ uiView: PKCanvasView, context: Context) {
        if uiView.drawing != drawing {
            uiView.drawing = drawing
        }
        // Always set the tool to ensure it's current (PKTool doesn't conform to Equatable)
        uiView.tool = selectedTool
    }
}

// MARK: - Drawing Preview View
struct DrawingPreviewView: UIViewRepresentable {
    let drawing: PKDrawing
    
    func makeUIView(context: Context) -> PKCanvasView {
        let canvasView = PKCanvasView()
        canvasView.drawing = drawing
        canvasView.isUserInteractionEnabled = false // Read-only preview
        canvasView.backgroundColor = UIColor.systemBackground
        canvasView.layer.borderWidth = 1
        canvasView.layer.borderColor = UIColor.systemGray4.cgColor
        canvasView.layer.cornerRadius = 4
        return canvasView
    }
    
    func updateUIView(_ uiView: PKCanvasView, context: Context) {
        if uiView.drawing != drawing {
            uiView.drawing = drawing
        }
    }
}

// MARK: - Camera Capture View for Photo Answers
struct CameraCaptureView: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    @Binding var isPresented: Bool
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = true
        return picker
    }
    
    func updateUIViewController(_ uiView: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UINavigationControllerDelegate, UIImagePickerControllerDelegate {
        let parent: CameraCaptureView
        
        init(_ parent: CameraCaptureView) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let editedImage = info[.editedImage] as? UIImage {
                parent.image = editedImage
            } else if let originalImage = info[.originalImage] as? UIImage {
                parent.image = originalImage
            }
            parent.isPresented = false
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}

// MARK: - Tool Button Component
struct ToolButton: View {
    let icon: String
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(isSelected ? .white : .blue)

                Text(title)
                    .font(.caption2)
                    .foregroundColor(isSelected ? .white : .blue)
            }
            .frame(width: 60, height: 60)
            .background(isSelected ? Color.blue : Color(.systemGray6))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color.blue, lineWidth: isSelected ? 0 : 1)
            )
        }
        .buttonStyle(.plain)
    }
}

// MARK: - Answer Submission Type Enum
enum AnswerSubmissionType: String, CaseIterable {
    case none = "Not Selected"
    case drawing = "Submit Drawing"
    case photo = "Submit Photo"

    var icon: String {
        switch self {
        case .none: return "questionmark.circle"
        case .drawing: return "pencil.and.outline"
        case .photo: return "camera"
        }
    }

    var color: Color {
        switch self {
        case .none: return .gray
        case .drawing: return .blue
        case .photo: return .green
        }
    }
}

// MARK: - Question Type Import
// Note: QuestionType is defined in FunMA/Models/ExerciseModel.swift
// Import that file or ensure it's available in your project

// MARK: - Student Draft Area Component
struct StudentDraftAreaView: View {
    @Binding var canvasView: PKCanvasView
    @Binding var drawing: PKDrawing
    @Binding var answerImage: UIImage?
    @Binding var answerDrawing: PKDrawing?
    @Binding var answerType: AnswerSubmissionType
    let questionType: QuestionType
    let isExpanded: Bool
    let onToggleExpanded: () -> Void

    @State private var showingCamera = false
    @State private var showingAnswerTypeSelection = false
    @State private var selectedTool: PKTool = PKInkingTool(.pen, color: .black, width: 2)

    var body: some View {
        VStack(spacing: 12) {
            // Header with expand/collapse
            HStack {
                Image(systemName: "pencil.tip")
                    .foregroundColor(.blue)

                Text(questionType == .longQuestion ? "Draft Area & Answer Submission" : "Calculation Draft Area")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Button(action: onToggleExpanded) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.blue)
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
            .background(Color(.systemGray6))
            .cornerRadius(8)

            if isExpanded {
                VStack(spacing: 16) {
                    // Drawing Canvas
                    VStack(alignment: .leading, spacing: 8) {
                        VStack(spacing: 8) {
                            HStack {
                                Text("Drawing Canvas")
                                    .font(.subheadline)
                                    .fontWeight(.medium)

                                Spacer()

                                // Clear button
                                Button("Clear") {
                                    drawing = PKDrawing()
                                    canvasView.drawing = PKDrawing()
                                }
                                .font(.caption)
                                .foregroundColor(.red)
                            }

                            // Tool selection
                            HStack(spacing: 12) {
                                ToolButton(
                                    icon: "pencil.tip",
                                    title: "Pen",
                                    isSelected: selectedTool is PKInkingTool && (selectedTool as! PKInkingTool).inkType == .pen,
                                    action: {
                                        selectedTool = PKInkingTool(.pen, color: .black, width: 2)
                                        canvasView.tool = selectedTool
                                    }
                                )

                                ToolButton(
                                    icon: "paintbrush",
                                    title: "Marker",
                                    isSelected: selectedTool is PKInkingTool && (selectedTool as! PKInkingTool).inkType == .marker,
                                    action: {
                                        selectedTool = PKInkingTool(.marker, color: .systemBlue, width: 10)
                                        canvasView.tool = selectedTool
                                    }
                                )

                                ToolButton(
                                    icon: "eraser",
                                    title: "Eraser",
                                    isSelected: selectedTool is PKEraserTool,
                                    action: {
                                        selectedTool = PKEraserTool(.bitmap)
                                        canvasView.tool = selectedTool
                                    }
                                )

                                Spacer()
                            }
                        }

                        DrawingCanvasView(
                            canvasView: $canvasView,
                            drawing: $drawing,
                            isAnswerSubmission: questionType == .longQuestion,
                            selectedTool: $selectedTool
                        )
                        .frame(height: 300)
                        .background(Color(.systemBackground))
                        .cornerRadius(8)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                    }

                    // Answer submission options for long questions
                    if questionType == .longQuestion {
                        VStack(alignment: .leading, spacing: 12) {
                            Text("Answer Submission")
                                .font(.subheadline)
                                .fontWeight(.medium)

                            // Answer type selection
                            HStack(spacing: 12) {
                                ForEach(AnswerSubmissionType.allCases, id: \.self) { type in
                                    Button(action: {
                                        if type == .photo && answerType != .photo {
                                            showingCamera = true
                                        } else if type == .drawing {
                                            answerDrawing = drawing
                                            answerImage = nil
                                        }
                                        answerType = type
                                    }) {
                                        VStack(spacing: 4) {
                                            Image(systemName: type.icon)
                                                .font(.title2)
                                                .foregroundColor(answerType == type ? .white : type.color)

                                            Text(type.rawValue)
                                                .font(.caption)
                                                .foregroundColor(answerType == type ? .white : type.color)
                                                .multilineTextAlignment(.center)
                                        }
                                        .frame(maxWidth: .infinity)
                                        .padding(.vertical, 12)
                                        .background(answerType == type ? type.color : Color(.systemGray6))
                                        .cornerRadius(8)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 8)
                                                .stroke(type.color, lineWidth: answerType == type ? 0 : 1)
                                        )
                                    }
                                    .buttonStyle(.plain)
                                }
                            }

                            // Preview of selected answer
                            if answerType == .drawing && answerDrawing != nil {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Selected Drawing Answer:")
                                        .font(.caption)
                                        .foregroundColor(.gray)

                                    if let drawing = answerDrawing {
                                        DrawingPreviewView(drawing: drawing)
                                            .frame(height: 300)
                                            .cornerRadius(8)
                                    } else {
                                        Rectangle()
                                            .fill(Color(.systemGray6))
                                            .frame(height: 300)
                                            .overlay(
                                                Text("Drawing Preview")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                            )
                                            .cornerRadius(8)
                                    }
                                }
                            } else if answerType == .photo, let image = answerImage {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Selected Photo Answer:")
                                        .font(.caption)
                                        .foregroundColor(.gray)

                                    Image(uiImage: image)
                                        .resizable()
                                        .aspectRatio(contentMode: .fit)
                                        .frame(height: 300)
                                        .cornerRadius(8)
                                }
                            }
                        }
                        .padding()
                        .background(Color(.systemGray6).opacity(0.5))
                        .cornerRadius(8)
                    }

                    // Instructions
                    VStack(alignment: .leading, spacing: 4) {
                        if questionType == .longQuestion {
                            Text("💡 For long questions:")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                            Text("• Use the drawing canvas for calculations and drafts")
                                .font(.caption)
                                .foregroundColor(.gray)
                            Text("• Choose to submit your drawing OR take a photo as your final answer")
                                .font(.caption)
                                .foregroundColor(.gray)
                        } else {
                            Text("💡 Use this space for calculations and working out your answer")
                                .font(.caption)
                                .foregroundColor(.gray)
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(12)
                .shadow(radius: 2)
            }
        }
        .fullScreenCover(isPresented: $showingCamera) {
            CameraCaptureView(image: $answerImage, isPresented: $showingCamera)
                .onDisappear {
                    if answerImage != nil {
                        answerType = .photo
                        answerDrawing = nil
                    }
                }
        }
    }
}
