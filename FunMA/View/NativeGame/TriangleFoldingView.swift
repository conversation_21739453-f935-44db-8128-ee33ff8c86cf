//
//  TriangleFoldingView.swift
//  Luminous Education
//
//  Created by <PERSON> on 27/2/2025.
//

import SwiftUI
import SceneKit

struct TriangleFoldingView: View {
    // Animation states
    @State private var isUnfolded = true
    @State private var foldProgress: Double = 0
    @State private var showButton = true
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack {
            ZStack {
                // SceneKit container for 3D view
                SceneView(scene: createScene(), options: [.allowsCameraControl, .autoenablesDefaultLighting])
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .opacity(isUnfolded ? 0 : 1)
                
                // 2D triangle net
                ZStack {
                    // Base triangle
                    TriangleNet()
                        .stroke(Color.blue, lineWidth: 2)
                        .background(TriangleNet().fill(colorScheme == .dark ? Color.blue.opacity(0.3) : Color.blue.opacity(0.1)))
                    
                    // Triangle labels
                    Text("A")
                        .font(.system(size: 18, weight: .bold))
                        .position(x: 150, y: 240)
                    Text("B")
                        .font(.system(size: 18, weight: .bold))
                        .position(x: 300, y: 240)
                    Text("C")
                        .font(.system(size: 18, weight: .bold))
                        .position(x: 225, y: 100)
                    Text("D")
                        .font(.system(size: 18, weight: .bold))
                        .foregroundColor(.orange)
                        .position(x: 225, y: 190)
                        
                    // Folding lines
                    FoldingLine(from: CGPoint(x: 225, y: 100), to: CGPoint(x: 150, y: 240))
                        .stroke(Color.red, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    FoldingLine(from: CGPoint(x: 225, y: 100), to: CGPoint(x: 300, y: 240))
                        .stroke(Color.red, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    FoldingLine(from: CGPoint(x: 225, y: 190), to: CGPoint(x: 150, y: 240))
                        .stroke(Color.red, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    FoldingLine(from: CGPoint(x: 225, y: 190), to: CGPoint(x: 300, y: 240))
                        .stroke(Color.red, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    
                    // D vertex connection lines
                    FoldingLine(from: CGPoint(x: 225, y: 190), to: CGPoint(x: 225, y: 100))
                        .stroke(Color.orange, style: StrokeStyle(lineWidth: 2, dash: [5]))
                    
                    // Circle for D point
                    Circle()
                        .fill(Color.orange)
                        .frame(width: 10, height: 10)
                        .position(x: 225, y: 190)
                        
                    // Brief explanation of D
                    Text("Point D forms the apex of the pyramid")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .position(x: 225, y: 280)
                }
                .frame(width: 450, height: 340)
                .opacity(isUnfolded ? 1 : 0)
                
                // Instruction text
                VStack {
                    Spacer()
                    Text("A 2D triangle net can be folded to create a 3D triangular pyramid")
                        .multilineTextAlignment(.center)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(colorScheme == .dark ? Color(.systemGray6) : Color.white)
                                .opacity(0.8)
                        )
                        .padding()
                }
            }
            
            // Fold/Unfold button
            Button(action: {
                withAnimation(.easeInOut(duration: 2.0)) {
                    isUnfolded.toggle()
                    showButton = false
                }
                
                // Show button again after animation completes
                DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                    showButton = true
                }
            }) {
                Text(isUnfolded ? "Fold into 3D Pyramid" : "Unfold to 2D Net")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.blue)
                    )
            }
            .disabled(!showButton)
            .opacity(showButton ? 1.0 : 0.5)
            .padding(.bottom)
        }
        .background(colorScheme == .dark ? Color(.systemGray5) : Color.gray.opacity(0.1))
    }
    
    private func createScene() -> SCNScene {
        let scene = SCNScene()
        
        // Create a tetrahedron (triangular pyramid)
        let tetrahedron = SCNNode(geometry: createTetrahedronGeometry())
        tetrahedron.position = SCNVector3(0, 0, 0)
        scene.rootNode.addChildNode(tetrahedron)
        
        // Add vertex labels to the tetrahedron node so they move with it
        addVertexLabel(to: tetrahedron, text: "C", position: SCNVector3(0, 1.2, 0))         // Top vertex
        addVertexLabel(to: tetrahedron, text: "A", position: SCNVector3(-1.2, -1.2, 1.2))   // Front-left base vertex
        addVertexLabel(to: tetrahedron, text: "B", position: SCNVector3(1.2, -1.2, 1.2))    // Front-right base vertex
        addVertexLabel(to: tetrahedron, text: "D", position: SCNVector3(0, -1.2, -1.2))     // Back base vertex
        
        // Add rotation animation
        let rotationAction = SCNAction.rotateBy(x: 0, y: CGFloat(2 * Double.pi), z: 0, duration: 10)
        let repeatedRotation = SCNAction.repeatForever(rotationAction)
        tetrahedron.runAction(repeatedRotation)
        
        return scene
    }
    
    private func createTetrahedronGeometry() -> SCNGeometry {
        // Define the four vertices of a tetrahedron
        let vertices: [SCNVector3] = [
            SCNVector3(0, 1, 0),              // Top vertex
            SCNVector3(-1, -1, 1),            // Front-left base vertex
            SCNVector3(1, -1, 1),             // Front-right base vertex
            SCNVector3(0, -1, -1)             // Back base vertex
        ]
        
        // Define the four triangular faces
        let indices: [Int32] = [
            0, 1, 2,  // Front face
            0, 3, 1,  // Left face
            0, 2, 3,  // Right face
            1, 3, 2   // Bottom face
        ]
        
        // Create a custom geometry source with the vertices
        let vertexSource = SCNGeometrySource(vertices: vertices)
        
        // Create the element with the indices
        let element = SCNGeometryElement(indices: indices, primitiveType: .triangles)
        
        // Create the geometry with the source and element
        let geometry = SCNGeometry(sources: [vertexSource], elements: [element])
        
        // Add colors to the faces
        let colors: [UIColor] = [
            .systemBlue.withAlphaComponent(0.7),
            .systemGreen.withAlphaComponent(0.7),
            .systemPurple.withAlphaComponent(0.7),
            .systemOrange.withAlphaComponent(0.7)
        ]
        
        let colorData = Data(bytes: colors, count: colors.count * MemoryLayout<UIColor>.size)
        let colorSource = SCNGeometrySource(data: colorData,
                                          semantic: .color,
                                          vectorCount: colors.count,
                                          usesFloatComponents: true,
                                          componentsPerVector: 4,
                                          bytesPerComponent: MemoryLayout<CGFloat>.size,
                                          dataOffset: 0,
                                          dataStride: MemoryLayout<UIColor>.size)
        
        // Apply materials to each face
        let materials = colors.map { color -> SCNMaterial in
            let material = SCNMaterial()
            material.diffuse.contents = color
            material.isDoubleSided = true
            return material
        }
        
        geometry.materials = materials
        
        return geometry
    }
    
    // Helper method to add vertex labels
    private func addVertexLabel(to parent: SCNNode, text: String, position: SCNVector3) {
        // Create a small white sphere at the vertex to make the label stand out
        let sphereGeometry = SCNSphere(radius: 0.05)
        let sphereMaterial = SCNMaterial()
        sphereMaterial.diffuse.contents = UIColor.white
        sphereGeometry.materials = [sphereMaterial]
        
        let sphereNode = SCNNode(geometry: sphereGeometry)
        sphereNode.position = position
        parent.addChildNode(sphereNode)
        
        // Create text with better visibility
        let textGeometry = SCNText(string: text, extrusionDepth: 0.05)
        textGeometry.font = UIFont.boldSystemFont(ofSize: 0.6)
        textGeometry.flatness = 0.1
        
        // Black text for better readability
        let material = SCNMaterial()
        material.diffuse.contents = UIColor.black
        
        // Create a white background material for better contrast
        let backgroundMaterial = SCNMaterial()
        backgroundMaterial.diffuse.contents = UIColor.white
        
        textGeometry.materials = [material, backgroundMaterial]
        
        let textNode = SCNNode(geometry: textGeometry)
        
        // Center the text node
        let (min, max) = textGeometry.boundingBox
        let width = max.x - min.x
        textNode.pivot = SCNMatrix4MakeTranslation(width/2, 0, 0)
        
        // Place text exactly at vertex position
        textNode.position = position
        textNode.scale = SCNVector3(0.4, 0.4, 0.4)
        
        // Add a billboard constraint to make the text always face the camera
        let billboardConstraint = SCNBillboardConstraint()
        billboardConstraint.freeAxes = [.X, .Y, .Z]
        textNode.constraints = [billboardConstraint]
        
        parent.addChildNode(textNode)
    }
}

// Custom path for the triangle net
struct TriangleNet: Shape {
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // Base triangle
        path.move(to: CGPoint(x: 150, y: 240))    // Bottom left
        path.addLine(to: CGPoint(x: 300, y: 240)) // Bottom right
        path.addLine(to: CGPoint(x: 225, y: 100)) // Top
        path.closeSubpath()
        
        return path
    }
}

// Folding line representation
struct FoldingLine: Shape {
    var from: CGPoint
    var to: CGPoint
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        path.move(to: from)
        path.addLine(to: to)
        return path
    }
}

struct TriangleFoldingView_Previews: PreviewProvider {
    static var previews: some View {
        TriangleFoldingView()
    }
} 