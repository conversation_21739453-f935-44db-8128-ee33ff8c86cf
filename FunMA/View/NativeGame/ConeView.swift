//
//  ConeView.swift
//  Luminous Education
//
//  Created by <PERSON> on 25/2/2025.
//

import SwiftUI
import SceneKit

struct ConeView: View {
    // "cutPosition" adjusts how much of the cone's height is removed.
    @State private var cutPosition: Float = 0
    @State private var isCutMode: Bool = false  // Toggle for cross section mode

    var body: some View {
        ZStack {
            ConeSceneView(cutPosition: $cutPosition,
                          isCutMode: $isCutMode)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                Spacer()
                Button(action: {
                    isCutMode.toggle()
                    // When exiting cross section, reset the cut amount.
                    if !isCutMode {
                        cutPosition = 0
                    }
                }) {
                    Text(isCutMode ? "Exit Cross Section" : "Cross Section")
                        .font(.headline)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding()
            }
        }
    }
}

struct ConeSceneView: UIViewRepresentable {
    @Binding var cutPosition: Float
    @Binding var isCutMode: Bool
    
    // Create separate materials for the lateral surface and the base.
    static var lateralMaterial: SCNMaterial = {
        let material = SCNMaterial()
        material.diffuse.contents = UIColor.red
        return material
    }()
    
    static var baseMaterial: SCNMaterial = {
        let material = SCNMaterial()
        material.diffuse.contents = UIColor.red
        return material
    }()
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    func makeUIView(context: Context) -> SCNView {
        let scnView = SCNView()
        // Create the scene (which now adds a camera node)
        let scene = createScene(context: context)
        scnView.scene = scene
        
        // Set the scene's camera as the pointOfView.
        scnView.pointOfView = scene.rootNode.childNodes.first(where: { $0.camera != nil })
        scnView.allowsCameraControl = true
        scnView.autoenablesDefaultLighting = true
        scnView.isUserInteractionEnabled = true
        
        // Tap gesture for highlighting a face.
        let tapGesture = UITapGestureRecognizer(target: context.coordinator,
                                                action: #selector(context.coordinator.handleTap(_:)))
        scnView.addGestureRecognizer(tapGesture)
        
        // Pan gesture for adjusting the cut amount.
        let panGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                action: #selector(context.coordinator.handlePan(_:)))
        scnView.addGestureRecognizer(panGesture)
        
        // Rotation gesture (using 2 fingers).
        let rotationGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                     action: #selector(context.coordinator.handleRotation(_:)))
        rotationGesture.minimumNumberOfTouches = 2
        scnView.addGestureRecognizer(rotationGesture)
        
        return scnView
    }
    
    func updateUIView(_ uiView: SCNView, context: Context) {
        // Ensure the coordinator has the latest state.
        context.coordinator.parent = self
        
        // When in cross section mode, update the cone's geometry.
        if isCutMode, let container = context.coordinator.coneNode {
            context.coordinator.updateConeCut(for: container, position: cutPosition)
        }
    }
    
    private func createScene(context: Context) -> SCNScene {
        let scene = SCNScene()
        
        // Create a container node that remains anchored.
        let container = SCNNode()
        container.position = SCNVector3Zero
        
        // --- Static Initial Rotation ---
        // This rotation gives an initial 3D perspective.
        container.eulerAngles = SCNVector3(-0.5, 0.5, 0.5)
        
        // Create the initial cone.
        // A perfect cone has topRadius = 0, bottomRadius = 0.25, and height = 0.5.
        let cone = SCNCone(topRadius: 0, bottomRadius: 0.25, height: 0.5)
        cone.materials = [ConeSceneView.lateralMaterial, ConeSceneView.baseMaterial]
        let coneNode = SCNNode(geometry: cone)
        // Adjust the pivot so that the bottom of the cone is anchored.
        // With this pivot, after transformation the base is at y = 0 and the apex is at y = height.
        coneNode.pivot = SCNMatrix4MakeTranslation(0, -Float(cone.height)/2, 0)
        container.addChildNode(coneNode)
        
        scene.rootNode.addChildNode(container)
        
        // --- Add a camera node ---
        let cameraNode = SCNNode()
        cameraNode.camera = SCNCamera()
        cameraNode.position = SCNVector3(0, 0, 1.5)
        scene.rootNode.addChildNode(cameraNode)
        
        context.coordinator.coneNode = container
        context.coordinator.coneGeometryNode = coneNode
        
        return scene
    }
    
    class Coordinator {
        // "coneNode" is the container node.
        // "coneGeometryNode" holds the SCNCone geometry.
        var coneNode: SCNNode?
        var coneGeometryNode: SCNNode?
        var parent: ConeSceneView
        
        // Original cone dimensions.
        private let originalHeight: Float = 0.5
        private let originalBottomRadius: Float = 0.25
        
        init(_ parent: ConeSceneView) {
            self.parent = parent
        }
        
        @objc func handleTap(_ gesture: UITapGestureRecognizer) {
            guard let scnView = gesture.view as? SCNView,
                  let coneGeometryNode = self.coneGeometryNode else { return }
            let location = gesture.location(in: scnView)
            let hitResults = scnView.hitTest(location, options: nil)
            
            if let hit = hitResults.first, hit.node == coneGeometryNode {
                // Convert the hit point to the cone geometry node's local coordinate space.
                let localHit = coneGeometryNode.convertPosition(hit.worldCoordinates, from: nil)
                // After adjusting the pivot, the base of the cone is at y = 0 and the apex at y = current height.
                // Use a threshold to determine if the base (bottom face) was tapped.
                let threshold: Float = 0.1
                if localHit.y <= threshold {
                    // Base tapped: toggle highlight for the base material.
                    if let currentColor = ConeSceneView.baseMaterial.diffuse.contents as? UIColor,
                       currentColor == UIColor.red {
                        ConeSceneView.baseMaterial.diffuse.contents = UIColor.green
                    } else {
                        ConeSceneView.baseMaterial.diffuse.contents = UIColor.red
                    }
                } else {
                    // Lateral tapped: toggle highlight for the lateral surface material.
                    if let currentColor = ConeSceneView.lateralMaterial.diffuse.contents as? UIColor,
                       currentColor == UIColor.red {
                        ConeSceneView.lateralMaterial.diffuse.contents = UIColor.green
                    } else {
                        ConeSceneView.lateralMaterial.diffuse.contents = UIColor.red
                    }
                }
            }
        }
        
        @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
            if parent.isCutMode {
                let translation = gesture.translation(in: gesture.view)
                // Adjust sensitivity as needed.
                let delta = Float(translation.y) * 0.001
                parent.cutPosition += delta
                // Clamp "cutPosition" so that it never exceeds the original height.
                parent.cutPosition = min(max(parent.cutPosition, 0), originalHeight)
                gesture.setTranslation(.zero, in: gesture.view)
            }
        }
        
        @objc func handleRotation(_ gesture: UIPanGestureRecognizer) {
            let translation = gesture.translation(in: gesture.view)
            let rotationY = Float(translation.x) * 0.01
            let rotationX = Float(translation.y) * 0.01
            
            coneNode?.eulerAngles.y += rotationY
            coneNode?.eulerAngles.x += rotationX
            gesture.setTranslation(.zero, in: gesture.view)
        }
        
        /// Updates the cone’s geometry by reducing its height (i.e. cutting off part of the cone's tip).
        /// A new top radius is computed via similar triangles once the height is shortened.
        func updateConeCut(for containerNode: SCNNode, position: Float) {
            guard let coneGeometryNode = self.coneGeometryNode else { return }
            
            // Calculate the new height.
            let newHeight = max(0.0, originalHeight - position)
            // When cutting the tip of a perfect cone, the new top radius is calculated by similar triangles.
            let newTopRadius = originalBottomRadius * (1 - newHeight / originalHeight)
            
            // Create a new cone geometry representing the truncated cone (frustum).
            let updatedCone = SCNCone(topRadius: CGFloat(newTopRadius),
                                      bottomRadius: CGFloat(originalBottomRadius),
                                      height: CGFloat(newHeight))
            updatedCone.materials = [ConeSceneView.lateralMaterial, ConeSceneView.baseMaterial]
            coneGeometryNode.geometry = updatedCone
            
            // Update the pivot so that the cone's bottom remains anchored.
            coneGeometryNode.pivot = SCNMatrix4MakeTranslation(0, -newHeight / 2, 0)
            
            print("Cut update – newHeight: \(newHeight), newTopRadius: \(newTopRadius)")
        }
    }
}

struct ConeView_Previews: PreviewProvider {
    static var previews: some View {
        ConeView()
    }
}
