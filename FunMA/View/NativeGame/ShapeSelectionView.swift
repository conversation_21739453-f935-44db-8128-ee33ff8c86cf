//
//  ShapeSelectionView.swift
//  Luminous Education
//
//  Created by <PERSON> on 27/2/2025.
//

import SwiftUI

struct ShapeSelectionView: View {
    @State private var selectedShape: String = "Cube" // Default shape
    let shapes = ["Cube", "Cone", "Cy<PERSON>er", "Sphere", "Triangular Pyramid"] // List of shapes
    let shapeIcons = ["cube.fill", "cone.fill", "cylinder.fill", "soccerball", "triangle.fill"] // SF Symbols for icons
    @Environment(\.colorScheme) private var colorScheme

    var body: some View {
        HStack {
            // Left Side: Shape Selection
            VStack(alignment: .leading) {
//                Text("Class Activity: Does it have uniform cross section?")
//                    .font(.title)
//                    .padding()

                ForEach(0..<shapes.count, id: \.self) { index in
                    Button(action: {
                        selectedShape = shapes[index]
                    }) {
                        HStack {
                            Image(systemName: shapeIcons[index])
                                .resizable()
                                .frame(width: 40, height: 40)
                                .foregroundColor(selectedShape == shapes[index] ? .blue : .gray)
                            Text(shapes[index])
                                .font(.headline)
                                .foregroundColor(selectedShape == shapes[index] ? .blue : .primary)
                        }
                        .padding(.vertical, 10)
                    }
                }
                
            }
            .padding()
            .background(colorScheme == .dark ? Color(.systemGray6) : Color.white)
            .cornerRadius(12)
//            Divider()

            // Right Side: Split between visualization and questions
            VStack {
                // Top: Shape Visualization
                destinationView(for: selectedShape)
                    .frame(maxHeight: .infinity)
                    .cornerRadius(12)
                    .padding(.horizontal)
                    
//                    .frame(height: UIScreen.main.bounds.height * 0.45) // 60% of screen height
                
//                Divider()
                
                // Bottom: Question View
                QuestionView(selectedShape: selectedShape)
//                    .frame(height: UIScreen.main.bounds.height * 0.4) // 30% of screen height
            }
            .frame(maxHeight: .infinity)
            .background(colorScheme == .dark ? Color(.systemGray5) : Color.gray.opacity(0.1))
//             .padding()
        }
//        .navigationTitle("Shape Selector")
        .background(colorScheme == .dark ? Color.black : Color(.systemGray6))
        .frame(maxWidth: .infinity, maxHeight:.infinity)
    }

    // Function to return the destination view based on the selected shape
    @ViewBuilder
    private func destinationView(for shapeName: String) -> some View {
        switch shapeName {
        case "Cube":
            CubeView() // Replace with your actual CubeView
        case "Cone":
            ConeView() // Replace with your actual ConeView
        case "Sphere":
            SphereView() // Replace with your actual SphereView
        case "Cylinder":
            SphereView() // Replace with your actual CylinderView
        case "Triangular Pyramid":
            TriangleFoldingView() // Our new folding triangle view
        default:
            Text("Select a valid shape")
        }
    }
}

struct ShapeSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        ShapeSelectionView()
    }
}
