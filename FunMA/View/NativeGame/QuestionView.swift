//
//  QuestionView.swift
//  Luminous Education
//
//  Created by <PERSON> on 27/2/2025.
//

import SwiftUI

// QuestionView to display questions related to selected shapes
struct QuestionView: View {
    var selectedShape: String
    @State private var selectedAnswer: Int? = nil
    @Environment(\.colorScheme) private var colorScheme
    
    // Track which question we're currently showing (0, 1, or 2)
    @State private var currentQuestionIndex: Int = 0
    
    // Questions are now standardized across shapes
    private func getQuestion(index: Int = 0) -> String {
        switch index {
        case 0:
            return "What is the shape of the cross-section when \(selectedShape.lowercased()) is cut parallel to its base?"
        case 1:
            return "Are all cross-sections of a \(selectedShape.lowercased()) the same size?"
        case 2:
            return "Does a \(selectedShape.lowercased()) have uniform cross-sections?"
        default:
            return "Select a shape to see a question"
        }
    }
    
    // Answer options for each shape and question
    private func getAnswers(index: Int = 0) -> [String] {
        switch index {
        case 0: // Shape of cross section
            switch selectedShape {
            case "Cube":
                return ["Triangle", "Square", "Rectangle", "Circle"]
            case "Sphere":
                return ["Always a circle", "Always an ellipse", "Depends on the angle", "Always a point"]
            case "Cone":
                return ["Triangle", "Circle", "Ellipse", "Rectangle"]
            case "Cylinder":
                return ["Circle", "Ellipse", "Rectangle", "Oval"]
            default:
                return []
            }
        case 1: // Same size
            return ["Yes, all the same size", "No, they can be different sizes", "Only if cut at the same angle", "Only at the center"]
        case 2: // Uniform cross section
            return ["Yes, all cross-sections are the same shape", "No, cross-section sizes can vary", "Only along one axis", "Only in special cases"]
        default:
            return []
        }
    }
    
    // Correct answer index for each shape and question
    private func getCorrectAnswerIndex(index: Int = 0) -> Int {
        switch index {
        case 0: // Shape of cross section
            switch selectedShape {
            case "Cube": return 1 // Square
            case "Sphere": return 2 // Depends on the angle
            case "Cone": return 1 // Circle
            case "Cylinder": return 0 // Circle
            default: return 0
            }
        case 1: // Same size
            switch selectedShape {
            case "Cube": return 0 // Yes, all the same size
            case "Sphere": return 1 // No, they can be different sizes
            case "Cone": return 1 // No, they can be different sizes
            case "Cylinder": return 0 // Yes, all the same size (when parallel to base)
            default: return 0
            }
        case 2: // Uniform cross section
            switch selectedShape {
            case "Cube": return 0 // Yes
            case "Sphere": return 1 // No
            case "Cone": return 1 // No
            case "Cylinder": return 0 // Yes
            default: return 0
            }
        default:
            return 0
        }
    }
    
    // Get explanation text for the answer
    private func getExplanationText() -> String {
        switch currentQuestionIndex {
        case 0:
            switch selectedShape {
            case "Cube":
                return "When a cube is cut parallel to its base, the cross-section is always a square."
            case "Sphere":
                return "The cross-section of a sphere depends on where you cut it. It's always a circle, but the size varies."
            case "Cone":
                return "When a cone is cut parallel to its base, the cross-section is a circle."
            case "Cylinder":
                return "When a cylinder is cut parallel to its base, the cross-section is always a circle."
            default:
                return ""
            }
        case 1:
            switch selectedShape {
            case "Cube":
                return "When cut parallel to the base, all cross-sections of a cube are squares of the same size."
            case "Sphere":
                return "Cross-sections of a sphere vary in size depending on how far they are from the center."
            case "Cone":
                return "The circular cross-sections of a cone get smaller as you move from the base to the apex."
            case "Cylinder":
                return "When cut parallel to the base, all cross-sections of a cylinder are circles of the same size."
            default:
                return ""
            }
        case 2:
            switch selectedShape {
            case "Cube":
                return "A cube has uniform cross-sections along each axis - they are always the same shape (square) and size."
            case "Sphere":
                return "A sphere doesn't have uniform cross-sections as their size changes based on distance from center."
            case "Cone":
                return "A cone doesn't have uniform cross-sections as they change in size from base to apex."
            case "Cylinder":
                return "A cylinder has uniform cross-sections when cut parallel to its base - always circles of the same size."
            default:
                return ""
            }
        default:
            return ""
        }
    }
    
    // Maximum number of questions
    private let maxQuestions = 3
    
    var body: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text("Question \(currentQuestionIndex + 1) of \(maxQuestions):")
                    .font(.headline)
                    .padding(.bottom, 5)
                
                Spacer()
                
                if currentQuestionIndex > 0 {
                    Button(action: {
                        // Go back to previous question
                        withAnimation {
                            currentQuestionIndex = currentQuestionIndex - 1
                            selectedAnswer = nil
                        }
                    }) {
                        Label("Previous", systemImage: "arrow.left")
                            .font(.caption)
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
            
            Text(getQuestion(index: currentQuestionIndex))
                .font(.title3)
                .padding(.bottom, 10)
            
            let answers = getAnswers(index: currentQuestionIndex)
            if !answers.isEmpty {
                Text("Your answer:")
                    .font(.headline)
                    .padding(.bottom, 5)
                
                ForEach(0..<answers.count, id: \.self) { index in
                    Button(action: {
                        selectedAnswer = index
                        
                        // If we're not on the last question and answer is selected,
                        // wait a moment and then show the next question
                        if currentQuestionIndex < maxQuestions - 1 {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
                                withAnimation(.easeOut(duration: 0.5)) {
                                    currentQuestionIndex += 1
                                    selectedAnswer = nil
                                }
                            }
                        }
                    }) {
                        HStack {
                            Text(answers[index])
                                .foregroundColor(Color.primary)
                            
                            Spacer()
                            
                            if selectedAnswer == index {
                                Image(systemName: index == getCorrectAnswerIndex(index: currentQuestionIndex) ? "checkmark.circle.fill" : "record.circle.fill")
                                    .foregroundColor(index == getCorrectAnswerIndex(index: currentQuestionIndex)
                                                   ? (colorScheme == .dark
                                                     ? Color(red: 0.0, green: 0.9, blue: 0.3)
                                                     : Color(red: 0.0, green: 0.7, blue: 0.3))
                                                   : Color(red: 1.0, green: 0.2, blue: 0.2))
                                    .imageScale(.large)
                            }
                        }
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 8)
                                .fill(selectedAnswer == index 
                                    ? Color.blue.opacity(0.2) 
                                    : (colorScheme == .dark ? Color.gray.opacity(0.2) : Color.gray.opacity(0.1)))
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(selectedAnswer != nil)
                }
                
                // Show explanation after answering
                if selectedAnswer != nil {
                    VStack(alignment: .leading, spacing: 5) {
                        Text("Explanation:")
                            .font(.headline)
//                            .padding(.top, 8)
                        
                        Text(getExplanationText())
                            .font(.body)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemGray4))
                    )
//                    .padding(.top, 5)
                }
                
                // Show completion message after the final question
                if currentQuestionIndex == maxQuestions - 1 && selectedAnswer != nil {
                    VStack(alignment: .leading, spacing: 10) {
                        Text("Great job!")
                            .font(.headline)
//                            .padding(.top)
                        
                        Text("Try selecting another shape to explore more about different cross-sections.")
                            .font(.subheadline)
                    }
                    .padding()
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(colorScheme == .dark ? Color.green.opacity(0.7) : Color.green.opacity(0.5))
                    )
//                    .padding(.top)
                }
            }
            
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(colorScheme == .dark ? Color(.systemGray6) : Color.white)
        )
        .padding([.horizontal, .bottom])
        // Reset when shape changes
        .onChange(of: selectedShape) { _ in
            selectedAnswer = nil
            currentQuestionIndex = 0
        }
    }
}

struct QuestionView_Previews: PreviewProvider {
    static var previews: some View {
        QuestionView(selectedShape: "Sphere")
            .previewLayout(.fixed(width: 400, height: 300))
    }
} 
