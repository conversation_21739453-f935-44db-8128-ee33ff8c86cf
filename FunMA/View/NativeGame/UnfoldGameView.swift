import SwiftUI
import SceneKit

struct UnfoldGameView: View {
    @State private var isUnfolded = false
    @State private var userAnswer: String = ""
    @State private var isCorrect: Bool? = nil
    @State private var showFeedback = false
    @State private var animationProgress: CGFloat = 0.0
    @State private var isAnimating = false
    @State private var currentQuestionIndex = 0
    
    // Prism dimensions (cm)
    let width: CGFloat = 4
    let height: CGFloat = 8
    let depth: CGFloat = 4
    let correctVolume = 4 * 8 * 4
    let correctSurfaceArea = 4*8*4 + 4*4*2
    
    // Questions array
    let questions = [
        GameQuestion(
            text: "Q1. What is the volume of the prism?",
            correctAnswer: 128,
            unit: "cm³"
        ),
        GameQuestion(
            text: "Q2. What is the total surface area of the prism?",
            correctAnswer: 160,
            unit: "cm²"
        )
    ]
    
    var currentQuestion: GameQuestion {
        questions[currentQuestionIndex]
    }
    
    var body: some View {
        VStack(spacing: 0) {
            ZStack {
                if !isUnfolded {
                    ZStack {
                        Prism3DContainerView(width: width, height: height, depth: depth)
                            .transition(.opacity)
                    }
                } else {
                    VStack {
                        Spacer()
                        UnfoldedNetView(width: width, height: height, depth: depth, progress: animationProgress)
                            .frame(height: 400)
                    }
                    .transition(.opacity)
                }
            }
            .animation(.easeInOut(duration: 1.2), value: isUnfolded)
            .padding(.top, 30)
            
            // Spacer()
            Button(action: {
                if !isUnfolded {
                    isAnimating = true
                    withAnimation(.easeInOut(duration: 1.2)) {
                        animationProgress = 1.0
                        isUnfolded = true
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.2) {
                        isAnimating = false
                    }
                } else {
                    withAnimation(.easeInOut(duration: 1.2)) {
                        animationProgress = 0.0
                        isUnfolded = false
                    }
                }
            }) {
                Text(isUnfolded ? "Fold" : "Unfold")
                    .font(.headline)
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(10)
            }
            .padding(.vertical, 16)
            .disabled(isAnimating)
            
            VStack(alignment: .leading, spacing: 16) {
                Text(currentQuestion.text)
                    .font(.title3)
                    .fontWeight(.semibold)
                HStack {
                    TextField("Enter answer", text: $userAnswer)
                        .keyboardType(.numberPad)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .frame(width: 120)
                        .onChange(of: userAnswer) { newValue in
                            checkAnswer()
                        }
                    Text(currentQuestion.unit)
                        .font(.headline)
                    if let isCorrect = isCorrect {
                        Image(systemName: isCorrect ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(isCorrect ? .green : .red)
                            .transition(.scale)
                    }
                }
                if showFeedback, let isCorrect = isCorrect {
                    Text(isCorrect ? "Correct!" : "Try again.")
                        .foregroundColor(isCorrect ? .green : .red)
                        .fontWeight(.bold)
                        .transition(.opacity)
                }
                
                // Next question button
                if let isCorrect = isCorrect, isCorrect, currentQuestionIndex < questions.count - 1 {
                    Button(action: {
                        nextQuestion()
                    }) {
                        Text("Next Question")
                            .font(.headline)
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 8)
                            .background(Color.green)
                            .cornerRadius(8)
                    }
                    .transition(.opacity)
                }
                
                // Completion message
                if let isCorrect = isCorrect, isCorrect, currentQuestionIndex == questions.count - 1 {
                    Text("🎉 Congratulations! You've completed all questions!")
                        .font(.headline)
                        .foregroundColor(.green)
                        .fontWeight(.bold)
                        .transition(.opacity)
                }
            }
            .padding()
            .background(Color.white.opacity(0.9))
            .cornerRadius(12)
            .padding(.horizontal, 24)
            .padding(.top, 8)
            Spacer()
        }
        .background(Color(.systemGray6))
    }
    
    private func checkAnswer() {
        let trimmed = userAnswer.trimmingCharacters(in: .whitespaces)
        if let value = Int(trimmed), value == currentQuestion.correctAnswer {
            withAnimation { isCorrect = true; showFeedback = true }
        } else if !trimmed.isEmpty {
            withAnimation { isCorrect = false; showFeedback = true }
        } else {
            withAnimation { isCorrect = nil; showFeedback = false }
        }
    }
    
    private func nextQuestion() {
        withAnimation {
            currentQuestionIndex += 1
            userAnswer = ""
            isCorrect = nil
            showFeedback = false
        }
    }
}

// MARK: - Game Question Model
struct GameQuestion {
    let text: String
    let correctAnswer: Int
    let unit: String
}

// MARK: - 3D Prism View
struct Prism3DView: UIViewRepresentable {
    let width: CGFloat
    let height: CGFloat
    let depth: CGFloat
    
    func makeUIView(context: Context) -> SCNView {
        let scnView = SCNView()
        let scene = SCNScene()
        
        // Create the box
        let box = SCNBox(width: width/3, height: height/3, length: depth/3, chamferRadius: 0.02)
        let material = SCNMaterial()
        material.diffuse.contents = UIColor.systemTeal
        box.materials = [material]
        let node = SCNNode(geometry: box)
        node.position = SCNVector3(0, 0, 0)
        scene.rootNode.addChildNode(node)
        
        // Add dimension labels
        let labelScale: CGFloat = 0.2
        let labelOffset: CGFloat = 0.1
        
        // Width label
        let widthText = createTextNode(text: "4 cm", scale: labelScale)
        widthText.position = SCNVector3(0, CGFloat(height/6) + labelOffset, 0.5)
        widthText.eulerAngles.x = Float(CGFloat.pi*1.5)
        scene.rootNode.addChildNode(widthText)
        
        // Height label
        let heightText = createTextNode(text: "4 cm", scale: labelScale)
        heightText.position = SCNVector3(0.3, CGFloat(height/6) + labelOffset, 0)
        heightText.eulerAngles.x = Float(CGFloat.pi*1.5)
        scene.rootNode.addChildNode(heightText)
        
        // Depth label
        let depthText = createTextNode(text: "8 cm", scale: labelScale)
        depthText.position = SCNVector3(0.3, 0, CGFloat(depth/6) + labelOffset)
        scene.rootNode.addChildNode(depthText)
        
        // Camera
        let cameraNode = SCNNode()
        cameraNode.camera = SCNCamera()
        cameraNode.position = SCNVector3(0, 0, 4)
        scene.rootNode.addChildNode(cameraNode)
        
        // Light
        let lightNode = SCNNode()
        lightNode.light = SCNLight()
        lightNode.light?.type = .omni
        lightNode.position = SCNVector3(2, 2, 2)
        scene.rootNode.addChildNode(lightNode)
        
        // Add ambient light to make labels more visible
        let ambientLight = SCNNode()
        ambientLight.light = SCNLight()
        ambientLight.light?.type = .ambient
        ambientLight.light?.intensity = 100
        scene.rootNode.addChildNode(ambientLight)
        
        scnView.scene = scene
        scnView.allowsCameraControl = true
        scnView.backgroundColor = UIColor.clear
        return scnView
    }
    
    private func createTextNode(text: String, scale: CGFloat) -> SCNNode {
        let textGeometry = SCNText(string: text, extrusionDepth: 0.01)
        textGeometry.font = UIFont.systemFont(ofSize: 1)
        textGeometry.flatness = 0.1
        
        let textNode = SCNNode(geometry: textGeometry)
        textNode.scale = SCNVector3(scale, scale, scale)
        
        // Center the text
        let (min, max) = textGeometry.boundingBox
        let dx = min.x + (max.x - min.x) / 2
        let dy = min.y + (max.y - min.y) / 2
        let dz = min.z + (max.z - min.z) / 2
        textNode.pivot = SCNMatrix4MakeTranslation(dx, dy, dz)
        
        // Create material for the text - change to white
        let textMaterial = SCNMaterial()
        textMaterial.diffuse.contents = UIColor.white
        textGeometry.materials = [textMaterial]
        
        return textNode
    }
    
    func updateUIView(_ uiView: SCNView, context: Context) {}
}

// Fix the frame modifier issue by wrapping Prism3DView in a container view
struct Prism3DContainerView: View {
    let width: CGFloat
    let height: CGFloat
    let depth: CGFloat
    
    var body: some View {
        Prism3DView(width: width, height: height, depth: depth)
            .frame(height: 400)
    }
}

// MARK: - 2D Net View
struct UnfoldedNetView: View {
    let width: CGFloat
    let height: CGFloat
    let depth: CGFloat
    let progress: CGFloat // 0.0 (folded) to 1.0 (fully unfolded)
    
    var body: some View {
        GeometryReader { geo in
            let scale: CGFloat = min(geo.size.width/300, geo.size.height/220)
            let w = width * scale * 10
            let h = height * scale * 10
            let d = depth * scale * 10
            let centerX = geo.size.width/1.8
            let centerY = geo.size.height/2.5
            ZStack {
                // Center rectangle (front face)
                Rectangle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: w, height: h)
                    .position(x: centerX, y: centerY)
                // Right face
                Rectangle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: d, height: h)
                    .position(x: centerX + (w+d)/2 * progress, y: centerY)
                    .opacity(progress)
                // Left face
                Rectangle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: d, height: h)
                    .position(x: centerX - (w+d)/2 * progress, y: centerY)
                    .opacity(progress)
                //Left left face
                Rectangle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: w, height: h)
                    .position(x: centerX - (w+d) * progress, y: centerY)
                    .opacity(progress)
                // Top face
                Rectangle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: w, height: d)
                    .position(x: centerX, y: centerY - (h+d)/2 * progress)
                    .opacity(progress)
                // Bottom face
                Rectangle()
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(width: w, height: d)
                    .position(x: centerX, y: centerY + (h+d)/2 * progress)
                    .opacity(progress)


                // --- Unique Dimension Labels ---
                // Horizontal (width) edge: label once below the main rectangle
                Text("4 cm")
                    .font(.callout)
                    .foregroundColor(.blue)
                    .position(x: centerX, y: centerY + h/2 + 18)
                // 4cm label at the bottom right edge
                Text("4 cm")
                    .font(.callout)
                    .foregroundColor(.blue)
                    .position(x: centerX + w/2 + 25, y: centerY + h/2 + d/2)
                // Vertical (height) edge: label once to the left of the main rectangle
                Text("8 cm")
                    .font(.callout)
                    .foregroundColor(.blue)
                    .position(x: centerX - w/2 - 25, y: centerY)
                // Depth (d) edge: label once above the top face
                Text("4 cm")
                    .font(.callout)
                    .foregroundColor(.blue)
                    .position(x: centerX, y: centerY - (h+d)/2 * progress - d/2 - 14)
                    .opacity(progress)
                // Height for side faces: label once to the right of the right face
                Text("8cm")
                    .font(.callout)
                    .foregroundColor(.blue)
                    .position(x: centerX + (w+d)/2 * progress + d/2 + 25, y: centerY)
                    .opacity(progress)
            }
        }
    }
}

#Preview {
    UnfoldGameView()
} 
