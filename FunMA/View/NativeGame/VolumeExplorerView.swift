import SwiftUI

struct VolumeExplorerView: View {
    // Water state: 0 = empty, 1 = cone full, 2 = 1 pour, 3 = 2 pours, 4 = 3 pours (cylinder full)
    @State private var coneFilled = false
    @State private var pours = 0 // 0 to 3
    
    // Dimensions
    let radius: CGFloat = 96
    let height: CGFloat = 256
    
    var body: some View {
        VStack(spacing: 24) {
            Text("Both circular cylinder and circular cone have the same height and radius.")
                .font(.headline)
                .multilineTextAlignment(.center)
                .padding(.top)
            HStack(alignment: .bottom) {
                Spacer()
                // Cylinder
                ZStack(alignment: .bottom) {
                    CylinderShape(radius: radius, height: height)
                        .stroke(Color.blue, lineWidth: 4)
                        .frame(width: radius * 2, height: height)
                    // Water in cylinder
                    if pours > 0 {
                        CylinderWaterShape(radius: radius, waterHeight: (height-radius/2) * CGFloat(pours) / 3, cylinderHeight: height)
                            .fill(Color.blue.opacity(0.2))
                            .frame(width: radius * 2, height: height)
                    }
                    // r label at center of dotted radius line
                    Text("r")
                        .font(.callout)
                        .foregroundColor(.blue)
                        .offset(x: radius/2, y: -height+20)
                    
                    // h label at center of dotted height line
                    Text("h")
                        .font(.callout)
                        .foregroundColor(.blue)
                        .offset(x: 20, y: -height/2)
                }
                Spacer()
                // Cone
                ZStack(alignment: .bottom) {
                    ConeShape(radius: radius, height: height)
                        .stroke(Color.blue, lineWidth: 4)
                        .frame(width: radius * 2, height: height)
                    // Water in cone
                    if coneFilled {
                        ConeWaterShape(radius: radius, height: height)
                            .fill(Color.blue.opacity(0.2))
                            .frame(width: radius * 2, height: height)
                    }
                    // r label at center of dotted radius line
                    Text("r")
                        .font(.callout)
                        .foregroundColor(.blue)
                        .offset(x: radius/2, y: -height+20)
                    
                    // h label at center of dotted height line
                    Text("h")
                        .font(.callout)
                        .foregroundColor(.blue)
                        .offset(x: 20, y: -height/2)
                }
                Spacer()
            }
            .frame(height: height + 20)
            HStack(spacing: 24) {
                Button(action: {
                    coneFilled = true
                }) {
                    Text("Fill")
                        .font(.headline)
                        .frame(width: 80, height: 40)
                        .background(coneFilled ? Color.gray : Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .disabled(coneFilled)
                Button(action: {
                    if coneFilled && pours < 3 {
                        pours += 1
                        coneFilled = false
                    }
                }) {
                    Text("Pour")
                        .font(.headline)
                        .frame(width: 80, height: 40)
                        .background((coneFilled && pours < 3) ? Color.blue : Color.gray)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .disabled(!coneFilled || pours >= 3)
            }
            .padding(.top)
            if pours == 3 {
                Text("The cylinder is full after 3 pours!")
                    .font(.headline)
                    .foregroundColor(.green)
            }
//            Spacer()
            // --- Quiz Section ---
                    QuizSection()
        }
//        .padding()
        
    }
}

// MARK: - Cylinder Shape
struct CylinderShape: Shape {
    let radius: CGFloat
    let height: CGFloat
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let centerX = rect.midX
        let topY = rect.minY
        let bottomY = rect.maxY
        let ellipseHeight = radius / 2

        // Top ellipse
        path.addEllipse(in: CGRect(x: centerX - radius, y: topY, width: radius * 2, height: ellipseHeight))
        // Sides
        path.move(to: CGPoint(x: centerX - radius, y: topY + ellipseHeight / 2))
        path.addLine(to: CGPoint(x: centerX - radius, y: bottomY - ellipseHeight / 2))
        path.move(to: CGPoint(x: centerX + radius, y: topY + ellipseHeight / 2))
        path.addLine(to: CGPoint(x: centerX + radius, y: bottomY - ellipseHeight / 2))
        // Bottom ellipse
        path.addEllipse(in: CGRect(x: centerX - radius, y: bottomY - ellipseHeight, width: radius * 2, height: ellipseHeight))

        // --- Dotted radius line on top ellipse ---
        let dotCount = 15
        let dotSize: CGFloat = 1
        let y = topY + ellipseHeight / 2
        for i in 0..<dotCount {
            let t = CGFloat(i) / CGFloat(dotCount - 1)
            let x = centerX + t * radius
            path.addEllipse(in: CGRect(x: x - dotSize/2, y: y - dotSize/2, width: dotSize, height: dotSize))
        }

        // --- Dotted height line ---
        let heightDotCount = 20
        let heightDotSize: CGFloat = 1
        let heightLineX = centerX // Position at the center of the cylinder
        let heightLineStartY = topY + ellipseHeight / 2
        let heightLineEndY = bottomY - ellipseHeight / 2
        let heightLineLength = heightLineEndY - heightLineStartY
        
        for i in 0..<heightDotCount {
            let t = CGFloat(i) / CGFloat(heightDotCount - 1)
            let y = heightLineStartY + t * heightLineLength
            path.addEllipse(in: CGRect(x: heightLineX - heightDotSize/2, y: y - heightDotSize/2, width: heightDotSize, height: heightDotSize))
        }

        return path
    }
}

struct CylinderWaterShape: Shape {
    let radius: CGFloat
    let waterHeight: CGFloat // current water height (body height * pours/3)
    let cylinderHeight: CGFloat // full cylinder height
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let centerX = rect.midX
        let bottomY = rect.maxY
        let ellipseOffset = radius/4 // Height of bottom ellipse
        let waterTopY = bottomY - waterHeight - ellipseOffset
        // Draw the water rectangle (between ellipses)
        path.addRect(CGRect(x: centerX - radius, y: waterTopY, width: radius * 2, height: waterHeight))
        // Draw the bottom ellipse (matches cylinder)
        path.addEllipse(in: CGRect(x: centerX - radius, y: bottomY - radius/2, width: radius * 2, height: radius/2))
        // Draw the top ellipse (matches water surface, inside the cylinder)
        path.addEllipse(in: CGRect(x: centerX - radius, y: waterTopY - ellipseOffset, width: radius * 2, height: radius/2))
        return path
    }
}

// MARK: - Cone Shape
struct ConeShape: Shape {
    let radius: CGFloat
    let height: CGFloat
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let centerX = rect.midX
        let topY = rect.minY
        let bottomY = rect.maxY
        // Top ellipse
        path.addEllipse(in: CGRect(x: centerX - radius, y: topY, width: radius * 2, height: radius/2))
        // Sides
        path.move(to: CGPoint(x: centerX - radius, y: topY + radius/4))
        path.addLine(to: CGPoint(x: centerX, y: bottomY))
        path.addLine(to: CGPoint(x: centerX + radius, y: topY + radius/4))
        
        // --- Dotted radius line on top ellipse ---
        let radiusDotCount = 15
        let radiusDotSize: CGFloat = 1
        let radiusLineY = topY + radius/4
        for i in 0..<radiusDotCount {
            let t = CGFloat(i) / CGFloat(radiusDotCount - 1)
            let x = centerX + t * radius
            path.addEllipse(in: CGRect(x: x - radiusDotSize/2, y: radiusLineY - radiusDotSize/2, width: radiusDotSize, height: radiusDotSize))
        }
        
        // --- Dotted height line at center of cone ---
        let heightDotCount = 20
        let heightDotSize: CGFloat = 1
        let heightLineX = centerX // Position at the center of the cone
        let heightLineStartY = topY + radius/4
        let heightLineEndY = bottomY
        let heightLineLength = heightLineEndY - heightLineStartY
        
        for i in 0..<heightDotCount {
            let t = CGFloat(i) / CGFloat(heightDotCount - 1)
            let y = heightLineStartY + t * heightLineLength
            path.addEllipse(in: CGRect(x: heightLineX - heightDotSize/2, y: y - heightDotSize/2, width: heightDotSize, height: heightDotSize))
        }
        
        return path
    }
}

struct ConeWaterShape: Shape {
    let radius: CGFloat
    let height: CGFloat
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let centerX = rect.midX
        let topY = rect.minY
        let bottomY = rect.maxY
        let ellipseRect = CGRect(x: centerX - radius, y: topY, width: radius * 2, height: radius/2)
        // Draw the top ellipse (water surface)
        path.addEllipse(in: ellipseRect)
        // Move to rightmost point of ellipse
        path.move(to: CGPoint(x: centerX + radius, y: topY + radius/4))
        // Draw line to tip
        path.addLine(to: CGPoint(x: centerX, y: bottomY))
        // Draw line to leftmost point of ellipse
        path.addLine(to: CGPoint(x: centerX - radius, y: topY + radius/4))
        // Close path
        path.closeSubpath()
        return path
    }
}

// MARK: - Dotted Line Shape
struct DottedLine: Shape {
    let length: CGFloat
    let dotSize: CGFloat
    let spacing: CGFloat
    let isVertical: Bool
    
    init(length: CGFloat, dotSize: CGFloat, spacing: CGFloat, isVertical: Bool = false) {
        self.length = length
        self.dotSize = dotSize
        self.spacing = spacing
        self.isVertical = isVertical
    }
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        let numberOfDots = Int(length / (dotSize + spacing))
        
        if isVertical {
            let startY = (rect.height - length) / 2
            for i in 0..<numberOfDots {
                let y = startY + CGFloat(i) * (dotSize + spacing)
                let dotRect = CGRect(x: rect.midX - dotSize/2, y: y, width: dotSize, height: dotSize)
                path.addEllipse(in: dotRect)
            }
        } else {
            let startX = (rect.width - length) / 2
            for i in 0..<numberOfDots {
                let x = startX + CGFloat(i) * (dotSize + spacing)
                let dotRect = CGRect(x: x, y: rect.midY - dotSize/2, width: dotSize, height: dotSize)
                path.addEllipse(in: dotRect)
            }
        }
        
        return path
    }
}

// MARK: - Quiz Section
struct QuizSection: View {
    struct MCQ {
        let question: String
        let options: [String]
        let correctIndex: Int
    }
    @State private var currentIndex = 0
    @State private var selectedIndex: Int? = nil
    @State private var showFeedback = false
    @State private var isCorrect: Bool? = nil
    
    let questions: [MCQ] = [
        MCQ(
            question: "Q1. What is the formula for the volume of a circular cylinder?",
            options: [
                "πr²h",
                "2πrh",
                "dh",
                "2πr²h"
            ],
            correctIndex: 0
        ),
        MCQ(
            question: "Q2. How many cones filled with water are needed to fill a cylinder?",
            options: [
                "1",
                "2",
                "3",
                "4"
            ],
            correctIndex: 2
        ),
        MCQ(
            question: "Q3. Hence, what is the formula for the volume of a right circular cone?",
            options: [
                "½πr²h",
                "⅓πr²h",
                "3πr²h",
                "2πr²h"
            ],
            correctIndex: 1
        )
    ]
    
    var body: some View {
        VStack(alignment: .leading) {
            Text("Quiz: Volume Relationships")
                .font(.title3)
                .fontWeight(.bold)
                .foregroundColor(.blue)
                .padding(.bottom, 2)
            Text(questions[currentIndex].question)
                .font(.headline)
                .foregroundColor(.primary)
            VStack(spacing: 14) {
                let options = questions[currentIndex].options
                let columns = [GridItem(.flexible()), GridItem(.flexible())]
                LazyVGrid(columns: columns, spacing: 14) {
                    ForEach(0..<options.count, id: \ .self) { idx in
                        let isSelected = selectedIndex == idx
                        let isAnswer = isCorrect == true && idx == questions[currentIndex].correctIndex && showFeedback
                        let isWrong = isCorrect == false && isSelected && showFeedback
                        Button(action: {
                            if isCorrect != true { // Only allow if not already correct
                                selectedIndex = idx
                                let correct = (idx == questions[currentIndex].correctIndex)
                                isCorrect = correct
                                showFeedback = true
                                if !correct {
                                    // Allow another try, but keep the red highlight for this attempt
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                                        showFeedback = false
                                        selectedIndex = nil
                                    }
                                }
                            }
                        }) {
                            HStack {
                                Text("\(Character(UnicodeScalar(65+idx)!)). ")
                                    .fontWeight(.bold)
                                    .foregroundColor(.blue)
                                Text(options[idx])
                                    .foregroundColor(.primary)
                            }
                            .padding(.vertical, 14)
                            .padding(.horizontal, 18)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .background(
                                Group {
                                    if isAnswer {
                                        Color.green.opacity(0.25)
                                    } else if isWrong {
                                        Color.red.opacity(0.18)
                                    } else {
                                        Color.white
                                    }
                                }
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(isSelected ? Color.blue : Color.gray.opacity(0.2), lineWidth: isSelected ? 2 : 1)
                            )
                            .cornerRadius(12)
                            .shadow(color: isSelected ? Color.blue.opacity(0.08) : Color.black.opacity(0.03), radius: 4, x: 0, y: 2)
                        }
                        .disabled(isCorrect == true) // Only disable if correct
                        .scaleEffect(isSelected ? 1.03 : 1.0)
                        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isSelected)
                    }
                }
            }
            if showFeedback {
                if isCorrect == true && currentIndex < questions.count - 1 {
                    HStack {
                        HStack(spacing: 8) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("Correct!")
                                .foregroundColor(.green)
                                .fontWeight(.bold)
                        }
                        .font(.title3)
                        Spacer()
                        Button(action: {
                            currentIndex += 1
                            selectedIndex = nil
                            showFeedback = false
                            isCorrect = nil
                        }) {
                            Text("Next Question")
                                .font(.headline)
                                .foregroundColor(.white)
                                .padding(.horizontal, 28)
                                .padding(.vertical, 10)
                                .background(Color.blue)
                                .cornerRadius(8)
                        }
                    }
                }
                else if isCorrect == false {
                    HStack(spacing: 8) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                        Text("Try again.")
                            .foregroundColor(.red)
                            .fontWeight(.bold)
                    }
                    .font(.title3)
                    .padding(.top, 4)
                }
                if isCorrect == true && currentIndex == questions.count - 1 {
                    Text("🎉 You've completed the quiz!")
                        .font(.headline)
                        .foregroundColor(.blue)
                        .padding(.top, 8)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 18)
                .fill(Color(.systemGray6))
                .shadow(color: Color.black.opacity(0.08), radius: 12, x: 0, y: 6)
        )
        .padding(.horizontal)
        .padding(.bottom, 32)
    }
}

#Preview {
    VolumeExplorerView()
} 
