//
//  SphereView.swift
//  Luminous Education
//
//  Created by <PERSON> on 27/2/2025.
//

import SwiftUI
import SceneKit

struct SphereView: View {
    @State private var cutPosition: Float = 0.0
    @State private var isCutMode: Bool = false
    @State private var selectedAxis: Int? // 0: X, 1: Y, 2: Z
    @State private var scaleText: String = "Scale: 100%"
    
    var body: some View {
        ZStack {
            SphereSceneView(cutPosition: $cutPosition,
                           isCutMode: $isCutMode,
                           selectedAxis: $selectedAxis)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                Spacer()
                HStack {
                    Button(action: {
                        print("Cross Section button pressed")
                        if let coordinator = SphereSceneView.coordinator {
                            print("Found coordinator, selected lines: \(coordinator.selectedLines.count)")
                            if coordinator.selectedLines.count > 0 {
                                coordinator.applyCrossSection()
                            }
                        } else {
                            print("No coordinator found")
                        }
                    }) {
                        Text("Cross Section")
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.blue)
                            .cornerRadius(10)
                    }
                    .disabled(SphereSceneView.coordinator?.selectedLines.count == 0)
                }
                .padding()
            }
        }
    }
}

struct SphereSceneView: UIViewRepresentable {
    @Binding var cutPosition: Float
    @Binding var isCutMode: Bool
    @Binding var selectedAxis: Int?
    
    static let numberOfLines = 9 // 3 lines per axis
    static let lineSegments = 10 // Number of dots per line
    
    static var coordinator: Coordinator?
    
    static func createDottedGridPlane(size: CGFloat) -> SCNNode {
        let containerNode = SCNNode()
        
        // Create a transparent backing plane
        let plane = SCNPlane(width: size, height: size)
        plane.firstMaterial?.diffuse.contents = UIColor.clear
        plane.firstMaterial?.transparency = 0.01
        
        let planeNode = SCNNode(geometry: plane)
        containerNode.addChildNode(planeNode)
        
        // Constants for dotted lines
        let dotRadius: CGFloat = 0.002
        let spacing = size / 3
        let dotsPerLine = 15
        
        // Create lines container
        let linesContainer = SCNNode()
        containerNode.addChildNode(linesContainer)
        
        // Function to create a dotted line
        func createDottedLine(isHorizontal: Bool, position: Float) -> SCNNode {
            let lineNode = SCNNode()
            lineNode.name = isHorizontal ? "horizontalLine" : "verticalLine"
            let halfSize = Float(size / 2)
            
            for i in 0..<dotsPerLine {
                let t = Float(i) / Float(dotsPerLine - 1)
                let coord = -halfSize + (2 * halfSize * t)
                
                let dot = SCNSphere(radius: dotRadius)
                dot.firstMaterial?.diffuse.contents = isHorizontal ? UIColor.purple : UIColor.blue
                dot.firstMaterial?.emission.contents = isHorizontal ? UIColor.purple : UIColor.blue
                dot.firstMaterial?.lightingModel = .constant
                
                let dotNode = SCNNode(geometry: dot)
                if isHorizontal {
                    dotNode.position = SCNVector3(coord, position, 0)
                } else {
                    dotNode.position = SCNVector3(position, coord, 0)
                }
                // Set constraint to maintain look direction
                let constraint = SCNBillboardConstraint()
                constraint.freeAxes = [.X, .Y]
                dotNode.constraints = [constraint]
                
                lineNode.addChildNode(dotNode)
            }
            
            return lineNode
        }
        
        for i in -1...1 {
            // Add vertical dotted lines
            let x = Float(spacing) * Float(i)
            let lineX = createDottedLine(isHorizontal: false, position: x)
            linesContainer.addChildNode(lineX)
        
            // Add horizontal dotted lines
            let y = Float(spacing) * Float(i)
            let lineY = createDottedLine(isHorizontal: true, position: y)
            linesContainer.addChildNode(lineY)
        }
        
        return containerNode
    }
    
    func makeCoordinator() -> Coordinator {
        let coordinator = Coordinator(self)
        SphereSceneView.coordinator = coordinator
        return coordinator
    }
    
    func makeUIView(context: Context) -> SCNView {
        let scnView = SCNView()
        scnView.scene = createScene(context: context)
        scnView.allowsCameraControl = true
        scnView.autoenablesDefaultLighting = true
        scnView.backgroundColor = .black
        scnView.showsStatistics = true
        
        // Set the point of view
        if let cameraNode = scnView.scene?.rootNode.childNode(withName: "camera", recursively: true) {
            scnView.pointOfView = cameraNode
        }
        
        // Add tap gesture recognizer
        let tapGesture = UITapGestureRecognizer(target: context.coordinator,
                                                action: #selector(Coordinator.handleTap(_:)))
        scnView.addGestureRecognizer(tapGesture)
        
        // Add pan gesture recognizer for dragging the cross-section
        let panGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                action: #selector(Coordinator.handlePan(_:)))
        scnView.addGestureRecognizer(panGesture)
        
        // Add rotation gesture (using 2 fingers)
        let rotationGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                    action: #selector(Coordinator.handleRotation(_:)))
        rotationGesture.minimumNumberOfTouches = 2
        scnView.addGestureRecognizer(rotationGesture)
        
        // Add pinch gesture recognizer for scaling
        let pinchGesture = UIPinchGestureRecognizer(target: context.coordinator,
                                                    action: #selector(Coordinator.handlePinch(_:)))
        scnView.addGestureRecognizer(pinchGesture)
        
        return scnView
    }
    
    func updateUIView(_ uiView: SCNView, context: Context) {
        // No automatic cut plane updates needed
    }
    
    private func createScene(context: Context) -> SCNScene {
        let scene = SCNScene()
        
        // Create and add a camera node
        let cameraNode = SCNNode()
        cameraNode.name = "camera"
        cameraNode.camera = SCNCamera()
        cameraNode.position = SCNVector3(x: 0, y: 0, z: 1.5)
        scene.rootNode.addChildNode(cameraNode)
        
        // Create a single container node that will hold everything
        let containerNode = SCNNode()
        containerNode.name = "container"
        containerNode.position = SCNVector3Zero
        containerNode.eulerAngles = SCNVector3(-0.5, 0.5, 0.5)
        
        // Create the sphere
        let sphereRadius: CGFloat = 0.05
        let sphere = SCNSphere(radius: sphereRadius)
        
        let material = SCNMaterial()
        material.diffuse.contents = UIColor.red
        material.specular.contents = UIColor.white
        material.locksAmbientWithDiffuse = true
        material.lightingModel = .physicallyBased
        material.transparency = 1.0
        sphere.firstMaterial = material
        
        let sphereNode = SCNNode(geometry: sphere)
        sphereNode.name = "sphere"
        containerNode.addChildNode(sphereNode)
        
        // Create and add the dotted grid plane
        let gridSize = CGFloat(sphereRadius * 2.2)
        let gridNode = SphereSceneView.createDottedGridPlane(size: gridSize)
        gridNode.name = "grid"
        
        // Position grid relative to the sphere
        gridNode.position = SCNVector3(
            0,                          // x: centered
            Float(sphereRadius) * 0.2,  // y: higher
            Float(sphereRadius) * 1.0   // z: in front
        )
        containerNode.addChildNode(gridNode)
        
        // Add the container to the scene
        scene.rootNode.addChildNode(containerNode)
        
        // Set up the coordinator with references to nodes
        context.coordinator.sphereNode = sphereNode
        context.coordinator.gridNode = gridNode
        context.coordinator.containerNode = containerNode  // Store reference to container
        context.coordinator.lineNodes = gridNode.childNodes.last?.childNodes ?? []
        
        return scene
    }
    
    // MARK: - Coordinator
    
    class Coordinator: NSObject {
        var parent: SphereSceneView
        var sphereNode: SCNNode?
        var gridNode: SCNNode?
        var containerNode: SCNNode?  // Add this property
        var lineNodes: [SCNNode] = []
        var selectedLines: [SCNNode] = []
        
        init(_ parent: SphereSceneView) {
            self.parent = parent
            super.init()
        }
        
        @objc func handleTap(_ gestureRecognize: UIGestureRecognizer) {
            guard let scnView = gestureRecognize.view as? SCNView else { return }
            let touchLocation = gestureRecognize.location(in: scnView)
            
            let hitResults = scnView.hitTest(touchLocation, options: [:])
            if let hit = hitResults.first {
                // Find the parent line node
                var currentNode = hit.node
                while let parentNode = currentNode.parent {
                    if let name = parentNode.name,
                       (name == "horizontalLine" || name == "verticalLine") {
                        handleLineSelection(parentNode)
                        break
                    }
                    currentNode = parentNode
                }
            }
        }
        
        func handleLineSelection(_ selectedLine: SCNNode) {
            let isHorizontal = selectedLine.name == "horizontalLine"
            
            // If the line is already selected, deselect it
            if selectedLines.contains(selectedLine) {
                selectedLines.removeAll { $0 == selectedLine }
                setLineColor(selectedLine, color: isHorizontal ? UIColor.purple : UIColor.blue)
            } else {
                // If more than two lines are selected, remove the oldest
                if selectedLines.count >= 2 {
                    let oldestLine = selectedLines.removeFirst()
                    let oldestIsHorizontal = oldestLine.name == "horizontalLine"
                    setLineColor(oldestLine, color: oldestIsHorizontal ? UIColor.purple : UIColor.blue)
                }
                selectedLines.append(selectedLine)
                setLineColor(selectedLine, color: UIColor.yellow)
            }
            
            // Clear any existing cutting planes when selection changes
            if let sphereNode = sphereNode {
                sphereNode.parent?.childNodes.filter { $0 != sphereNode && $0 != gridNode }.forEach { $0.removeFromParentNode() }
                sphereNode.geometry?.firstMaterial?.shaderModifiers = [:]
            }
            
            // Update the parent view with the selection state
            if let lastSelected = selectedLines.last,
               let index = lineNodes.firstIndex(of: lastSelected) {
                parent.selectedAxis = index
            }
        }
        
        private func setLineColor(_ line: SCNNode, color: UIColor) {
            line.childNodes.forEach { dotNode in
                if let dot = dotNode.geometry as? SCNSphere {
                    dot.firstMaterial?.diffuse.contents = color
                    dot.firstMaterial?.emission.contents = color
                }
            }
        }
        
        @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
            guard let scnView = gesture.view as? SCNView,
                  parent.isCutMode,
                  parent.selectedAxis != nil else { return }
            
            let translation = gesture.translation(in: scnView)
            let delta = Float(translation.y) * 0.005 // Adjust sensitivity as needed
            parent.cutPosition = max(-0.5, min(0.5, parent.cutPosition + delta))
            gesture.setTranslation(.zero, in: scnView)
        }
        
        @objc func handleRotation(_ gesture: UIPanGestureRecognizer) {
            guard let containerNode = self.containerNode else { return }
            
            // Get translation
            let translation = gesture.translation(in: gesture.view)
            
            // Convert translation to rotation
            let rotationY = Float(translation.x) * 0.01
            let rotationX = Float(translation.y) * 0.01
            
            // Apply rotation to the container node
            containerNode.eulerAngles.y += rotationY
            containerNode.eulerAngles.x += rotationX
            
            // Reset translation for next event
            gesture.setTranslation(.zero, in: gesture.view)
            
            // If we're in the middle of a rotation and we have cross-sections, clear them
            if gesture.state == .changed && !selectedLines.isEmpty {
                clearCrossSection()
            }
            
            // If we've finished rotation and we have cross-sections, reapply them
            if gesture.state == .ended && !selectedLines.isEmpty {
                applyCrossSection()
            }
        }
        
        @objc func handlePinch(_ gesture: UIPinchGestureRecognizer) {
            guard let containerNode = sphereNode?.parent else { return }
            
            if gesture.state == .changed {
                let pinchScale = Float(gesture.scale)
                
                // Apply scale to the container node
                let newScale = SCNVector3(
                    containerNode.scale.x * pinchScale,
                    containerNode.scale.y * pinchScale,
                    containerNode.scale.z * pinchScale
                )
                
                // Apply the new scale
                containerNode.scale = newScale
                
                // Reset the gesture scale
                gesture.scale = 1.0
                
                // We don't need to update scaleText since it's not accessible from here
                // and not currently displayed in the UI
            }
        }
        
        // MARK: - Cross Section
        
        func applyCrossSection() {
            print("Applying cross section")
            guard !selectedLines.isEmpty,
                  let sphereNode = sphereNode,
                  let sphere = sphereNode.geometry as? SCNSphere else {
                print("Failed to apply cross section")
                return
            }
            
            // Clear any existing sections
            clearCrossSection()
            
            // Determine if we use a horizontal or vertical cut
            let isHorizontal = selectedLines[0].name == "horizontalLine"
            
            // Get container node which maintains the rotation
            guard let containerNode = sphereNode.parent else { return }
            
            // IMPORTANT: Fix #1 - Use local axes based on the grid orientation
            // Define the axis in the grid's local space
            let gridLocalAxis: SCNVector3 = isHorizontal ? SCNVector3(0, 1, 0) : SCNVector3(1, 0, 0)
            
            // Calculate the axis in the sphere's model (local) space - this is key for the shader
            // We go from grid space through the hierarchy to sphere space
            let gridNode = self.gridNode!
            let axisInSphere = sphereNode.convertVector(gridLocalAxis, from: gridNode)
            
            // Use normalized axis for shader calculations
            let axisLength = sqrt(axisInSphere.x * axisInSphere.x + 
                                 axisInSphere.y * axisInSphere.y + 
                                 axisInSphere.z * axisInSphere.z)
            
            let normalizedAxisInSphere = SCNVector3(
                axisInSphere.x / axisLength,
                axisInSphere.y / axisLength,
                axisInSphere.z / axisLength
            )
            
            // IMPORTANT: Fix #2 - Project line positions in the correct space
            // Get the selected lines' positions in the sphere's coordinate system
            var projections: [Float] = []
            
            for line in selectedLines {
                if let firstDot = line.childNodes.first {
                    // Convert dot position to sphere space
                    let dotPosInSphereSpace = sphereNode.convertPosition(firstDot.worldPosition, from: nil)
                    
                    // Project along the axis in sphere space
                    let proj = dotPosInSphereSpace.x * normalizedAxisInSphere.x + 
                               dotPosInSphereSpace.y * normalizedAxisInSphere.y + 
                               dotPosInSphereSpace.z * normalizedAxisInSphere.z
                               
                    projections.append(proj)
                }
            }
            
            projections.sort()
            guard let minVal = projections.first, let maxVal = projections.last else {
                print("No valid positions found from selected lines")
                return
            }
            
            print("Cut axis in sphere:", normalizedAxisInSphere, "min:", minVal, "max:", maxVal)
            
            // IMPORTANT: Fix #3 - Apply shader using consistent coordinate space
            let shaderCode = """
            #pragma arguments
            float u_min;
            float u_max;
            float3 u_cutAxis;
            #pragma body
            {
                float d = dot(_surface.position, u_cutAxis);
                if (d < u_min || d > u_max) {
                    discard_fragment();
                }
            }
            """
            
            sphereNode.geometry?.firstMaterial?.shaderModifiers = [.surface: shaderCode]
            sphereNode.geometry?.firstMaterial?.setValue(minVal, forKey: "u_min")
            sphereNode.geometry?.firstMaterial?.setValue(maxVal, forKey: "u_max")
            sphereNode.geometry?.firstMaterial?.setValue(NSValue(scnVector3: normalizedAxisInSphere), forKey: "u_cutAxis")
            
            // IMPORTANT: Fix #4 - Create cutting planes that match exactly
            // For visual cutting planes, we'll position them directly in the node hierarchy
            addVisualPlanesForCrossSection(
                sphere: sphere,
                sphereNode: sphereNode,
                gridNode: gridNode,
                cutAxis: gridLocalAxis,
                isHorizontal: isHorizontal,
                minVal: minVal,
                maxVal: maxVal
            )
            
            print("Cross section applied.")
        }
        
        /// Updated method to add visual cutting planes consistent with the shader
        private func addVisualPlanesForCrossSection(
            sphere: SCNSphere,
            sphereNode: SCNNode,
            gridNode: SCNNode,
            cutAxis: SCNVector3,
            isHorizontal: Bool,
            minVal: Float,
            maxVal: Float
        ) {
            let planeSize = CGFloat(sphere.radius * 2.2)
            
            // Create visual planes at min and max cut positions
            for position in [minVal, maxVal] {
                let plane = SCNPlane(width: planeSize, height: planeSize)
                let material = SCNMaterial()
                material.diffuse.contents = isHorizontal 
                    ? UIColor(red: 0.9, green: 0.6, blue: 0.6, alpha: 0.8)
                    : UIColor(red: 0.6, green: 0.6, blue: 0.9, alpha: 0.8)
                material.transparency = 0.7
                material.isDoubleSided = true
                plane.materials = [material]
                
                let planeNode = SCNNode(geometry: plane)
                
                // Position the plane in the sphere's local coordinate system
                // This ensures perfect alignment with the shader cut
                let offset = position
                planeNode.position = sphereNode.convertPosition(
                    SCNVector3(
                        cutAxis.x * offset,
                        cutAxis.y * offset, 
                        cutAxis.z * offset
                    ), 
                    from: gridNode
                )
                
                // Set proper orientation based on the cut axis
                if isHorizontal {
                    planeNode.eulerAngles.x = .pi / 2
                } else {
                    planeNode.eulerAngles.y = .pi / 2
                }
                
                // Ensure the orientation is aligned with the container's rotation
                let constraint = SCNLookAtConstraint(target: sphereNode)
                constraint.localFront = SCNVector3(0, 0, 1)
                planeNode.constraints = [constraint]
                
                material.readsFromDepthBuffer = true
                material.writesToDepthBuffer = false
                
                // Fix: Use sphereNode.parent directly instead of containerNode
                sphereNode.parent?.addChildNode(planeNode)
            }
        }
        
        func clearCrossSection() {
            guard let sphereNode = sphereNode else { return }
            sphereNode.geometry?.firstMaterial?.shaderModifiers = [:]
            sphereNode.parent?.childNodes.filter { $0 != sphereNode && $0 != gridNode }.forEach { $0.removeFromParentNode() }
        }
    }
}


struct SphereView_Previews: PreviewProvider {
    static var previews: some View {
        SphereView()
    }
}
