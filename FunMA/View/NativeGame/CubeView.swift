import SwiftUI
import SceneKit

struct CubeView: View {
    @State private var cutPosition: Float = 0 // Amount to remove from the selected face's dimension
    @State private var isCutMode: Bool = false  // Toggle for cross section mode
    @State private var selectedFaceIndex: Int?  // Holds the selected face index

    var body: some View {
        ZStack {
            CubeSceneView(cutPosition: $cutPosition,
                          isCutMode: $isCutMode,
                          selectedFaceIndex: $selectedFaceIndex)
                .edgesIgnoringSafeArea(.all)
            
            VStack {
                Spacer()
                Button(action: {
                    isCutMode.toggle() // Toggle cross section mode
                    
                    // When exiting cross section, reset geometry, clear selection and baseline.
                    if !isCutMode {
                        if let index = selectedFaceIndex {
                            CubeSceneView.resetFaceColor(index: index)
                        }
                        selectedFaceIndex = nil
                    }
                    // Note: The coordinator's stored baseline will be cleared in updateUIView when isCutMode is false.
                }) {
                    Text(isCutMode ? "Exit Cross Section" : "Cross Section")
                        .font(.headline)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(10)
                }
                .padding()
            }
        }
    }
}

struct CubeSceneView: UIViewRepresentable {
    @Binding var cutPosition: Float
    @Binding var isCutMode: Bool
    @Binding var selectedFaceIndex: Int?
    
    // Create materials for each face in SCNBox order:
    //   0: Front (positive z)
    //   1: Right (positive x)
    //   2: Back (negative z)
    //   3: Left (negative x)
    //   4: Top (positive y)
    //   5: Bottom (negative y)
    static var faceMaterials: [SCNMaterial] = {
        (0..<6).map { _ in
            let material = SCNMaterial()
            material.diffuse.contents = UIColor.red
            return material
        }
    }()
    
    /// Resets the face color of the specified face to red.
    static func resetFaceColor(index: Int) {
        faceMaterials[index].diffuse.contents = UIColor.red
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    func makeUIView(context: Context) -> SCNView {
        let scnView = SCNView()
        // Create the scene (which now adds a camera node)
        let scene = createScene(context: context)
        scnView.scene = scene
        
        // Set the scene's camera as the pointOfView.
        scnView.pointOfView = scene.rootNode.childNodes.first(where: { $0.camera != nil })
        scnView.allowsCameraControl = true
        scnView.autoenablesDefaultLighting = true
        scnView.isUserInteractionEnabled = true
        
        // Setup tap gesture for selecting a face.
        let tapGesture = UITapGestureRecognizer(target: context.coordinator,
                                                action: #selector(context.coordinator.handleTap(_:)))
        scnView.addGestureRecognizer(tapGesture)
        
        // Pan gesture for adjusting the cut amount.
        let panGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                action: #selector(context.coordinator.handlePan(_:)))
        scnView.addGestureRecognizer(panGesture)
        
        // Rotation gesture (using 2 fingers).
        let rotationGesture = UIPanGestureRecognizer(target: context.coordinator,
                                                     action: #selector(context.coordinator.handleRotation(_:)))
        rotationGesture.minimumNumberOfTouches = 2
        scnView.addGestureRecognizer(rotationGesture)
        
        return scnView
    }
    
    func updateUIView(_ uiView: SCNView, context: Context) {
        // Ensure the coordinator has the latest state.
        context.coordinator.parent = self
        
        // Only update the cube's geometry when in cross section mode.
        if isCutMode, let container = context.coordinator.cubeNode {
            context.coordinator.updateCubeCut(for: container, position: cutPosition)
        }
    }
    
    private func createScene(context: Context) -> SCNScene {
        let scene = SCNScene()
        
        // Create a container node that remains anchored.
        let container = SCNNode()
        // Place the container at the origin.
        container.position = SCNVector3Zero
        
        // --- Static Initial Rotation ---
        // Set a static initial rotation for a 3D view.
        container.eulerAngles = SCNVector3(-0.5, 0.5, 0.5)
        
        // --- Optional: Animated Initial Rotation ---
        // Uncomment the following lines if you'd like the cube to start rotating continuously.
        
//        let rotateAction = SCNAction.rotateBy(x: 0, y: CGFloat.pi * 2, z: 0, duration: 10)
//        let continuousRotation = SCNAction.repeatForever(rotateAction)
//        container.runAction(continuousRotation)
        
        
        // Create the initial cube (0.5 x 0.5 x 0.5) using an SCNBox.
        let cube = SCNBox(width: 0.5, height: 0.5, length: 0.5, chamferRadius: 0)
        cube.materials = CubeSceneView.faceMaterials
        let boxNode = SCNNode(geometry: cube)
        // Initially, the box node is centered in the container.
        container.addChildNode(boxNode)
        
        scene.rootNode.addChildNode(container)
        
        context.coordinator.cubeNode = container
        context.coordinator.boxNode = boxNode
        
        return scene
    }
    
    class Coordinator {
        // cubeNode is the container node (anchored), boxNode holds the SCNBox geometry.
        var cubeNode: SCNNode?
        var boxNode: SCNNode?
        var parent: CubeSceneView
        
        // The original full dimension of the cube.
        private let originalDimension: Float = 0.5
        
        init(_ parent: CubeSceneView) {
            self.parent = parent
        }
        
        @objc func handleTap(_ gesture: UITapGestureRecognizer) {
            guard let scnView = gesture.view as? SCNView else { return }
            let location = gesture.location(in: scnView)
            let hitResults = scnView.hitTest(location, options: nil)
            
            if let hit = hitResults.first {
                // Check if the tapped node is either the boxNode or its parent container.
                if let boxNode = self.boxNode,
                   (hit.node == boxNode || hit.node.parent == cubeNode) {
                    
                    if let faceIndex = determineFaceIndex(hit: hit) {
                        print("Face index tapped: \(faceIndex)")
                        // Reset previously selected face if needed.
                        if let previousIndex = parent.selectedFaceIndex, previousIndex != faceIndex {
                            CubeSceneView.resetFaceColor(index: previousIndex)
                        }
                        CubeSceneView.faceMaterials[faceIndex].diffuse.contents = UIColor.green
                        parent.selectedFaceIndex = faceIndex
                    }
                } else {
                    print("Tapped node is not part of the cube.")
                }
            } else {
                print("No valid hit detected")
            }
        }
        
        /// Identifies the tapped face based on the box's local coordinate system,
        /// assuming the SCNBox is centered at (0,0,0).
        private func determineFaceIndex(hit: SCNHitTestResult) -> Int? {
            guard let boxNode = self.boxNode, let box = boxNode.geometry as? SCNBox else {
                return nil
            }
            
            // Convert the hit point into the boxNode's local space.
            let localHit = boxNode.convertPosition(hit.worldCoordinates, from: nil)
            let halfWidth  = Float(box.width)  / 2.0
            let halfHeight = Float(box.height) / 2.0
            let halfLength = Float(box.length) / 2.0
            let tolerance: Float = 0.02
            
            var candidates: [(face: Int, distance: Float)] = []
            
            // Front face (index 0, +z)
            if abs(localHit.x) <= halfWidth + tolerance,
               abs(localHit.y) <= halfHeight + tolerance {
                let dist = abs(localHit.z - halfLength)
                candidates.append((face: 0, distance: dist))
            }
            
            // Right face (index 1, +x)
            if abs(localHit.y) <= halfHeight + tolerance,
               abs(localHit.z) <= halfLength + tolerance {
                let dist = abs(localHit.x - halfWidth)
                candidates.append((face: 1, distance: dist))
            }
            
            // Back face (index 2, -z)
            if abs(localHit.x) <= halfWidth + tolerance,
               abs(localHit.y) <= halfHeight + tolerance {
                let dist = abs(localHit.z + halfLength)
                candidates.append((face: 2, distance: dist))
            }
            
            // Left face (index 3, -x)
            if abs(localHit.y) <= halfHeight + tolerance,
               abs(localHit.z) <= halfLength + tolerance {
                let dist = abs(localHit.x + halfWidth)
                candidates.append((face: 3, distance: dist))
            }
            
            // Top face (index 4, +y)
            if abs(localHit.x) <= halfWidth + tolerance,
               abs(localHit.z) <= halfLength + tolerance {
                let dist = abs(localHit.y - halfHeight)
                candidates.append((face: 4, distance: dist))
            }
            
            // Bottom face (index 5, -y)
            if abs(localHit.x) <= halfWidth + tolerance,
               abs(localHit.z) <= halfLength + tolerance {
                let dist = abs(localHit.y + halfHeight)
                candidates.append((face: 5, distance: dist))
            }
            
            guard !candidates.isEmpty else {
                print("No valid face detected")
                return nil
            }
            
            let selectedFace = candidates.min { $0.distance < $1.distance }!.face
            print("determineFaceIndex: selected face: \(selectedFace) candidates: \(candidates)")
            return selectedFace
        }
        
        @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
            if parent.isCutMode {
                let translation = gesture.translation(in: gesture.view)
                let delta = Float(translation.y) * 0.001 // Adjust sensitivity as needed
                parent.cutPosition += delta
                parent.cutPosition = min(max(parent.cutPosition, 0), originalDimension)
                gesture.setTranslation(.zero, in: gesture.view)
            }
        }
        
        @objc func handleRotation(_ gesture: UIPanGestureRecognizer) {
            let translation = gesture.translation(in: gesture.view)
            let rotationY = Float(translation.x) * 0.01
            let rotationX = Float(translation.y) * 0.01
            
            cubeNode?.eulerAngles.y += rotationY
            cubeNode?.eulerAngles.x += rotationX
            gesture.setTranslation(.zero, in: gesture.view)
        }
        
        /// Updates the cube's cut by replacing the SCNBox geometry on the child node (boxNode)
        /// and setting its local offset so that the container node (cubeNode) remains anchored.
        func updateCubeCut(for containerNode: SCNNode, position: Float) {
            guard let selectedFace = parent.selectedFaceIndex,
                  let boxNode = self.boxNode else { return }
            
            let orig = originalDimension
            let newDimension = max(0.0, orig - position)
            var updatedBox: SCNBox!
            var offset = SCNVector3Zero
            
            switch selectedFace {
            case 0: // Front face (+z)
                updatedBox = SCNBox(width: CGFloat(orig),
                                    height: CGFloat(orig),
                                    length: CGFloat(newDimension),
                                    chamferRadius: 0)
                offset = SCNVector3(0, 0, -position / 2)
            case 1: // Right face (+x)
                updatedBox = SCNBox(width: CGFloat(newDimension),
                                    height: CGFloat(orig),
                                    length: CGFloat(orig),
                                    chamferRadius: 0)
                offset = SCNVector3(-position / 2, 0, 0)
            case 2: // Back face (-z)
                updatedBox = SCNBox(width: CGFloat(orig),
                                    height: CGFloat(orig),
                                    length: CGFloat(newDimension),
                                    chamferRadius: 0)
                offset = SCNVector3(0, 0, position / 2)
            case 3: // Left face (-x)
                updatedBox = SCNBox(width: CGFloat(newDimension),
                                    height: CGFloat(orig),
                                    length: CGFloat(orig),
                                    chamferRadius: 0)
                offset = SCNVector3(position / 2, 0, 0)
            case 4: // Top face (+y)
                updatedBox = SCNBox(width: CGFloat(orig),
                                    height: CGFloat(newDimension),
                                    length: CGFloat(orig),
                                    chamferRadius: 0)
                offset = SCNVector3(0, -position / 2, 0)
            case 5: // Bottom face (-y)
                updatedBox = SCNBox(width: CGFloat(orig),
                                    height: CGFloat(newDimension),
                                    length: CGFloat(orig),
                                    chamferRadius: 0)
                offset = SCNVector3(0, position / 2, 0)
            default:
                return
            }
            
            updatedBox.materials = CubeSceneView.faceMaterials
            boxNode.geometry = updatedBox
            boxNode.position = offset
            
            print("Cut update – selected face: \(selectedFace), newDimension: \(newDimension), offset: \(offset)")
        }
    }
}

struct CubeView_Previews: PreviewProvider {
    static var previews: some View {
        CubeView()
    }
}
