import SwiftUI

struct ContentView: View {
    @StateObject private var userManager = UserManager.shared
    @State private var columnVisibility: NavigationSplitViewVisibility = .all
    @State private var selectedSidebarItem: String = ""
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @State private var showingDetailView = false
    @State private var currentUserId: String = ""

    var body: some View {
        Group {
            if horizontalSizeClass == .compact {
                // iPhone layout - use NavigationStack with sheet presentation
                NavigationStack {
                    SideBarView(selectedItem: $selectedSidebarItem, showingDetailView: $showingDetailView)
                        .navigationTitle("FunMA")
                        .sheet(isPresented: $showingDetailView) {
                            NavigationStack {
                                destinationView(for: selectedSidebarItem)
                                    .navigationBarTitleDisplayMode(.inline)
                                    .navigationTitle(selectedSidebarItem)
                                    .navigationBarItems(trailing:
                                        Button("Done") {
                                            showingDetailView = false
                                        }
                                    )
                            }
                        }
                }
            } else {
                // iPad/Mac layout - use NavigationSplitView
                NavigationSplitView(columnVisibility: $columnVisibility) {
                    SideBarView(selectedItem: $selectedSidebarItem, showingDetailView: .constant(false))
                } detail: {
                    destinationView(for: selectedSidebarItem)
                        .navigationBarTitleDisplayMode(.inline)
                }
                .navigationSplitViewStyle(.automatic)
                .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SidebarCollapseStateChanged"))) { notification in
                    if let isCollapsed = notification.userInfo?["isCollapsed"] as? Bool {
                        columnVisibility = isCollapsed ? .detailOnly : .all
                    }
                }
            }
        }
        .onChange(of: userManager.currentUser.id) { newUserId in
            // Use Task to avoid blocking main thread during navigation state changes
            Task { @MainActor in
                // Reset navigation state when user changes (login/logout/user switch)
                // This ensures every user starts fresh at the appropriate default view
                if currentUserId != newUserId {
                    // Set default view based on user type
                    if userManager.currentUser.isGuest {
                        selectedSidebarItem = "Join Game"
                    } else {
                        selectedSidebarItem = "Dashboard"
                    }
                    // For iPhone, automatically show the detail view after login
                    if horizontalSizeClass == .compact {
                        showingDetailView = true
                    } else {
                        showingDetailView = false
                    }
                    currentUserId = newUserId
                }
            }
        }
        .onAppear {
            // Use Task to avoid blocking main thread during initial setup
            Task { @MainActor in
                // Initialize current user ID tracking
                currentUserId = userManager.currentUser.id

                // Set initial sidebar selection if not already set
                if selectedSidebarItem.isEmpty {
                    if userManager.currentUser.isGuest {
                        selectedSidebarItem = "Join Game"
                    } else {
                        selectedSidebarItem = "Dashboard"
                    }

                    // For iPhone, automatically show the detail view on initial load
                    if horizontalSizeClass == .compact {
                        showingDetailView = true
                    }
                }
            }
        }
    }
    
    // Function to return the destination view based on the selected item
    @ViewBuilder
    private func destinationView(for itemName: String) -> some View {
        switch itemName {
        case "Dashboard":
            if userManager.currentUser.isTeacher {
                TeacherDashboardView()
            } else {
                StudentDashboardView()
            }
        // case "My Courses":
        //     EnrolledCoursesView()
        //         .navigationTitle("My Courses")
        // case "Marketplace":
        //     MarketplaceView()
        //         .navigationTitle("Marketplace")
        // case "Homework":
        //     QuizView(lessonId: "Introduction to Algebraaa", quizType: "post-quiz")
        //         .navigationTitle("Homework")
        case "Interactive Tools":
            if userManager.currentUser.isDeveloper {
                DeveloperInteractiveToolsView()
                    .navigationTitle("Interactive Tools")
            } else {
                InteractiveToolsView()
                    .navigationTitle("Interactive Tools")
            }
        case "Shapes":
            ShapeSelectionView()
        case "AR Experience":
            ARContainerView()
        case "AI Study Assistant":
            AIStudyAssistantView()
        case "Launch Game":
            GameRoomCreationView()
        case "Join Game":
            StudentGameJoinView()
        case "Exercise":
            if userManager.currentUser.isTeacher || userManager.currentUser.isDeveloper {
                InClassExerciseView()
            } else {
                ExerciseTakingView()
            }
        case "My Classrooms":
            if userManager.currentUser.isTeacher || userManager.currentUser.isDeveloper {
                ClassroomManagementView()
            } else {
                StudentClassroomsListView()
            }
        case "My Classroom":
            StudentClassroomView()
        case "Flipped Classroom Resource":
            if userManager.currentUser.isDeveloper {
                FlippedClassroomResourceView()
                    .navigationTitle("Interactive Tools")
            } else {
                ComingSoonView()
                    .navigationTitle("Coming Soon")
            }
        case "School Management":
            if userManager.currentUser.isDeveloper {
                SchoolCreationView()
                    .navigationTitle("School Management")
            } else {
                ComingSoonView()
                    .navigationTitle("Access Denied")
            }
        case "Batch Account Creation":
            if userManager.currentUser.isDeveloper {
                BatchAccountCreationView()
                    .navigationTitle("Batch Account Creation")
            } else {
                ComingSoonView()
                    .navigationTitle("Access Denied")
            }
        case "Credit Adjustment":
            if userManager.currentUser.isDeveloper {
                CreditAdjustmentView()
                    .navigationTitle("Credit Adjustment")
            } else {
                ComingSoonView()
                    .navigationTitle("Access Denied")
            }
        case "AR Cube":
            if userManager.currentUser.isDeveloper {
                ARCubeView()
            }
        default:
            if userManager.currentUser.isGuest {
                StudentGameJoinView()
            } else if userManager.currentUser.isTeacher {
                TeacherDashboardView()
            } else {
                StudentDashboardView()
            }
        }
    }
}

#Preview {
    ContentView()
}

