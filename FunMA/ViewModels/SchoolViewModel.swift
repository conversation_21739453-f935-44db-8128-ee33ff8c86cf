import Foundation
import SwiftUI

class SchoolViewModel: ObservableObject {
    @Published var schools: [School] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    // MARK: - School Management
    
    // Fetch all schools
    @MainActor
    func fetchSchools() async {
        isLoading = true
        errorMessage = nil
        
        do {
            let result: Result<[School], APIError> = await APIService.shared.fetch(
                endpoint: "school",
                responseType: [School].self
            )
            
            switch result {
            case .success(let fetchedSchools):
                schools = fetchedSchools
                print("SchoolViewModel: ✅ Successfully fetched \(fetchedSchools.count) schools")
            case .failure(let error):
                errorMessage = "Failed to fetch schools: \(error.localizedDescription)"
                print("SchoolViewModel: ❌ Failed to fetch schools: \(error)")
            }
        } catch {
            errorMessage = "Unexpected error: \(error.localizedDescription)"
            print("SchoolViewModel: ❌ Unexpected error: \(error)")
        }
        
        isLoading = false
    }
    
    // Create a new school
    @MainActor
    func createSchool(name: String, address: String, region: String) async -> Bool {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedAddress = address.trimmingCharacters(in: .whitespacesAndNewlines)
        let trimmedRegion = region.trimmingCharacters(in: .whitespacesAndNewlines)

        // Validate required fields
        guard !trimmedName.isEmpty else {
            errorMessage = "School name cannot be empty"
            return false
        }

        guard !trimmedAddress.isEmpty else {
            errorMessage = "School address cannot be empty"
            return false
        }

        guard !trimmedRegion.isEmpty else {
            errorMessage = "School region must be selected"
            return false
        }

        isLoading = true
        errorMessage = nil

        let request = SchoolCreationRequest(
            schoolName: trimmedName,
            address: trimmedAddress,
            region: trimmedRegion
        )

        // Use manual URLSession approach to handle the nested response
        guard let url = URL(string: "\(APIConfig.baseURL)/school") else {
            errorMessage = "Invalid URL"
            isLoading = false
            return false
        }

        do {
            var urlRequest = URLRequest(url: url)
            urlRequest.httpMethod = "POST"
            urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

            // Add authorization header if available
            if let authHeader = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeader {
                    urlRequest.setValue(value, forHTTPHeaderField: key)
                }
            }

            urlRequest.httpBody = try JSONEncoder().encode(request)

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                errorMessage = "Invalid response"
                isLoading = false
                return false
            }

            if httpResponse.statusCode == 201 {
                // Parse the JSON response manually
                if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let schoolData = jsonObject["school"] as? [String: Any],
                   let schoolId = schoolData["_id"] as? String,
                   let schoolName = schoolData["schoolName"] as? String,
                   let schoolAddress = schoolData["address"] as? String,
                   let schoolRegion = schoolData["region"] as? String {

                    let newSchool = School(
                        id: schoolId,
                        schoolName: schoolName,
                        address: schoolAddress,
                        region: schoolRegion,
                        createdAt: Date()
                    )

                    schools.append(newSchool)
                    print("SchoolViewModel: ✅ Successfully created school: \(newSchool.schoolName)")
                    isLoading = false
                    return true
                } else {
                    errorMessage = "Invalid response format from server"
                    print("SchoolViewModel: ❌ Invalid response format")
                    isLoading = false
                    return false
                }
            } else {
                let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
                self.errorMessage = "Failed to create school: \(errorMessage)"
                print("SchoolViewModel: ❌ Failed to create school (HTTP \(httpResponse.statusCode)): \(errorMessage)")
                isLoading = false
                return false
            }
        } catch {
            errorMessage = "Unexpected error: \(error.localizedDescription)"
            print("SchoolViewModel: ❌ Unexpected error: \(error)")
            isLoading = false
            return false
        }
    }
}
