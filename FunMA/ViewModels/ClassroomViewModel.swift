import Foundation
import SwiftUI

class ClassroomViewModel: ObservableObject {
    @Published var classrooms: [Classroom] = []
    @Published var selectedClassroom: Classroom?
    @Published var isLoading = false
    @Published var error: String?
    // For creating/editing classrooms
    @Published var showingCreateClassroom = false
    @Published var showingClassroomDetails = false

    // Task management to prevent cancellation issues
    private var loadClassroomsTask: Task<Void, Never>?
    
    
    
    // MARK: - Classroom Management
    
    // Load all classrooms for the current teacher
    @MainActor
    func loadClassrooms() async {
        // Cancel any existing load task
        loadClassroomsTask?.cancel()

        // Prevent multiple concurrent loads
        guard !isLoading else {
            print("🏫 ClassroomViewModel: Already loading, skipping duplicate request")
            return
        }
        
        // Create a new task for this load operation
        loadClassroomsTask = Task {
            await performLoadClassrooms()
        }

        await loadClassroomsTask?.value
    }

    @MainActor
    private func performLoadClassrooms() async {
        let currentUser = UserManager.shared.currentUser
        print("🏫 ClassroomViewModel: Starting loadClassrooms()")
        print("🔍 ClassroomViewModel: Current user ID: '\(currentUser.id)'")
        print("🔍 ClassroomViewModel: Current user name: '\(currentUser.name)'")
        print("🔍 ClassroomViewModel: Current user role: '\(currentUser.role)'")
        print("🔍 ClassroomViewModel: Is logged in: \(UserManager.shared.isLoggedIn)")

        isLoading = true
        error = nil
        
        // Check if user is logged in and is a teacher
        guard UserManager.shared.isLoggedIn else {
            let errorMsg = "Please log in to access classrooms"
            print("❌ ClassroomViewModel: \(errorMsg)")
            self.error = errorMsg
            isLoading = false
            return
        }

        guard currentUser.isTeacher else {
            let errorMsg = "Only teachers can manage classrooms"
            print("❌ ClassroomViewModel: \(errorMsg)")
            self.error = errorMsg
            isLoading = false
            return
        }

        do {
            print("📡 ClassroomViewModel: Fetching classrooms for teacher: \(currentUser.id)")

            // Use teacher-specific endpoint
            let endpoint = "classroom?teacherId=\(currentUser.id)"
            print("📡 ClassroomViewModel: GET request to endpoint: \(endpoint)")

            // Check for task cancellation
            if Task.isCancelled {
                print("🚫 ClassroomViewModel: Task was cancelled before API call")
                isLoading = false
                return
            }

            let classrooms: [Classroom] = try await UserManager.shared.api.get(endpoint)

            // Check again for cancellation after API call
            if Task.isCancelled {
                print("🚫 ClassroomViewModel: Task was cancelled after API call")
                isLoading = false
                return
            }

            self.classrooms = classrooms
            print("✅ ClassroomViewModel: Successfully loaded \(classrooms.count) classrooms")

            // Log details of loaded classrooms
            for (index, classroom) in classrooms.enumerated() {
                print("   📚 Classroom \(index + 1): '\(classroom.name)' (\(classroom.grade.displayName)) with \(classroom.students.count) students")
            }

        } catch {
            // Check if this was a cancellation error
            if error is CancellationError {
                print("🚫 ClassroomViewModel: Task was cancelled during API call")
                isLoading = false
                return
            }

            // Check if this was a URLSession cancellation
            if let urlError = error as? URLError, urlError.code == .cancelled {
                print("🚫 ClassroomViewModel: URLSession task was cancelled")
                isLoading = false
                return
            }

            // Check if this was an APIError with cancellation message
            if let apiError = error as? APIError,
               case .networkError(let message) = apiError,
               message.contains("cancelled") {
                print("🚫 ClassroomViewModel: API request was cancelled")
                isLoading = false
                return
            }

            let errorMsg: String

            // Provide more specific error messages based on the type of error
            if let apiError = error as? APIError {
                switch apiError {
                case .noData:
                    errorMsg = "No classroom data received from server. Please check your internet connection and try again."
                case .unauthorized:
                    errorMsg = "Authentication failed. Please log in again."
                case .networkError(let message):
                    errorMsg = "Network error: \(message). Please check your internet connection."
                case .decodingError(let message):
                    errorMsg = "Data format error: \(message). Please try again later."
                case .serverError(let message):
                    errorMsg = "Server error: \(message). Please try again later."
                case .invalidURL:
                    errorMsg = "Invalid request. Please try again."
                }
            } else {
                errorMsg = "Failed to load classrooms: \(error.localizedDescription)"
            }

            self.error = errorMsg
            print("❌ ClassroomViewModel: \(errorMsg)")
            print("🔍 ClassroomViewModel: Error details: \(error)")
        }

        isLoading = false
        print("🏫 ClassroomViewModel: Finished loadClassrooms() - isLoading: \(isLoading)")
    }
    
    // Create a new classroom
    @MainActor
    func createClassroom(name: String, grade: Grade, subject: String?, schoolYear: String) async -> Bool {
        let currentUser = UserManager.shared.currentUser
        print("🏫 ClassroomViewModel: Starting createClassroom()")
        print("� ClassroomViewModel: Creating classroom with parameters:")
        print("   - Name: '\(name)'")
        print("   - Grade: '\(grade.displayName)'")
        print("   - Subject: '\(subject ?? "nil")'")
        print("   - School Year: '\(schoolYear)'")
        print("   - Teacher ID: '\(currentUser.id)'")
        print("   - Teacher Name: '\(currentUser.name)'")
        
        isLoading = true
        error = nil
        
        // Check if user is logged in and is a teacher
        guard UserManager.shared.isLoggedIn else {
            let errorMsg = "Please log in to create classrooms"
            print("❌ ClassroomViewModel: \(errorMsg)")
            self.error = errorMsg
            isLoading = false
            return false
        }
        
        guard currentUser.isTeacher else {
            let errorMsg = "Only teachers can create classrooms"
            print("❌ ClassroomViewModel: \(errorMsg)")
            self.error = errorMsg
            isLoading = false
            return false
        }
        
        do {
            let newClassroom = EnhancedCreateClassroomRequest(
                name: name,
                grade: grade,
                subject: subject,
                schoolYear: schoolYear,
                teacherId: currentUser.id,
                teacherName: currentUser.name
            )
            
            let response: ClassroomCreateResponse = try await UserManager.shared.api.post("classroom", body: newClassroom)
            
            // Add to local array
            classrooms.append(response.classroom)
            print("✅ ClassroomViewModel: Successfully created classroom: '\(response.classroom.name)'")
            
            isLoading = false
            return true
            
        } catch {
            let errorMsg: String
            
            if let apiError = error as? APIError {
                switch apiError {
                case .unauthorized:
                    errorMsg = "Authentication failed. Please log in again."
                case .serverError(let message) where message.contains("401"):
                    errorMsg = "Authentication failed. Please log in again."
                case .serverError(let message) where message.contains("403"):
                    errorMsg = "Access denied. You don't have permission to create classrooms."
                case .serverError(let message) where message.contains("400"):
                    errorMsg = "Invalid classroom data. Please check your input and try again."
                case .serverError(let message) where message.contains("409"):
                    errorMsg = "A classroom with this name already exists. Please choose a different name."
                default:
                    errorMsg = "Failed to create classroom: \(error.localizedDescription)"
                }
            } else {
                errorMsg = "Failed to create classroom: \(error.localizedDescription)"
            }
            
            self.error = errorMsg
            print("❌ ClassroomViewModel: \(errorMsg)")
            print("🔍 ClassroomViewModel: Error details: \(error)")
        }
        
        isLoading = false
        return false
    }
    
    // Delete a classroom
    @MainActor
    func deleteClassroom(_ classroom: Classroom) async -> Bool {
        print("🏫 ClassroomViewModel: Starting deleteClassroom() for: '\(classroom.name)' (ID: \(classroom.id))")
        
        isLoading = true
        error = nil
        
        do {
            let endpoint = "classroom/\(classroom.id)"
            print("📡 ClassroomViewModel: Sending DELETE request to: \(endpoint)")
            
            try await UserManager.shared.api.delete(endpoint)
            
            self.classrooms.removeAll { $0.id == classroom.id }
            if selectedClassroom?.id == classroom.id {
                selectedClassroom = nil
            }
            print("✅ ClassroomViewModel: Successfully deleted classroom: '\(classroom.name)'")
            print("📝 ClassroomViewModel: Remaining classrooms: \(self.classrooms.count)")
            isLoading = false
            return true
            
        } catch {
            let errorMsg = "Failed to delete classroom: \(error.localizedDescription)"
            self.error = errorMsg
            print("❌ ClassroomViewModel: \(errorMsg)")
            print("🔍 ClassroomViewModel: Error details: \(error)")
            isLoading = false
            return false
        }
    }
    
    
    
    
    // Remove a student from classroom
    @MainActor
    func removeStudent(_ student: ClassroomStudent, from classroom: Classroom) async -> Bool {
        print("🏫 ClassroomViewModel: Starting removeStudent() - Student: '\(student.name)' from classroom: '\(classroom.name)'")
        
        isLoading = true
        error = nil
        
        do {
            let endpoint = "classroom/\(classroom.id)/students/\(student.id)"
            print("📡 ClassroomViewModel: Sending DELETE request to: \(endpoint)")
            
            try await UserManager.shared.api.delete(endpoint)
            
            // Update local classroom data
            if let classroomIndex = classrooms.firstIndex(where: { $0.id == classroom.id }) {
                classrooms[classroomIndex].students.removeAll { $0.id == student.id }
            }
            
            // Update selected classroom if it's the same one
            if selectedClassroom?.id == classroom.id {
                selectedClassroom?.students.removeAll { $0.id == student.id }
            }
            
            print("✅ ClassroomViewModel: Successfully removed student '\(student.name)' from '\(classroom.name)'")
            isLoading = false
            return true
            
        } catch {
            let errorMsg = "Failed to remove student: \(error.localizedDescription)"
            self.error = errorMsg
            print("❌ ClassroomViewModel: \(errorMsg)")
            print("🔍 ClassroomViewModel: Error details: \(error)")
            isLoading = false
            return false
        }
    }
    
    // Update student status
    @MainActor
    func updateStudentStatus(_ student: ClassroomStudent, newStatus: ClassroomStudent.StudentStatus, in classroom: Classroom) async -> Bool {
        print("🏫 ClassroomViewModel: Starting updateStudentStatus() - Student: '\(student.name)' to status: \(newStatus)")
        
        isLoading = true
        error = nil
        
        let request = UpdateStudentStatusRequest(studentId: student.id, status: newStatus)
        
        do {
            let endpoint = "classroom/\(classroom.id)/students/\(student.id)/status"
            print("📡 ClassroomViewModel: Sending PUT request to: \(endpoint)")
            
            let updatedStudent: ClassroomStudent = try await UserManager.shared.api.put(endpoint, body: request)
            
            // Update local classroom data
            if let classroomIndex = classrooms.firstIndex(where: { $0.id == classroom.id }),
               let studentIndex = classrooms[classroomIndex].students.firstIndex(where: { $0.id == student.id }) {
                classrooms[classroomIndex].students[studentIndex] = updatedStudent
            }
            
            // Update selected classroom if it's the same one
            if selectedClassroom?.id == classroom.id,
               let studentIndex = selectedClassroom?.students.firstIndex(where: { $0.id == student.id }) {
                selectedClassroom?.students[studentIndex] = updatedStudent
            }
            
            print("✅ ClassroomViewModel: Successfully updated student '\(student.name)' status to \(newStatus)")
            isLoading = false
            return true
            
        } catch {
            let errorMsg = "Failed to update student status: \(error.localizedDescription)"
            self.error = errorMsg
            print("❌ ClassroomViewModel: \(errorMsg)")
            print("🔍 ClassroomViewModel: Error details: \(error)")
            isLoading = false
            return false
        }
    }
    
    // MARK: - Statistics
    
    // Load classroom statistics
    @MainActor
    func loadClassroomStats(for classroom: Classroom) async -> ClassroomStats? {
        print("📊 ClassroomViewModel: Loading stats for classroom: '\(classroom.name)'")
        
        do {
            let endpoint = "classroom/\(classroom.id)/stats"
            print("📡 ClassroomViewModel: Sending GET request to: \(endpoint)")
            
            let stats: ClassroomStats = try await UserManager.shared.api.get(endpoint)
            
            print("✅ ClassroomViewModel: Successfully loaded stats for '\(classroom.name)'")
            print("📝 ClassroomViewModel: Stats - Total Students: \(stats.totalStudents), Active: \(stats.activeStudents)")
            return stats
            
        } catch {
            let errorMsg = "Failed to load classroom stats: \(error.localizedDescription)"
            self.error = errorMsg
            print("❌ ClassroomViewModel: \(errorMsg)")
            print("🔍 ClassroomViewModel: Error details: \(error)")
            return nil
        }
    }
    
    // MARK: - Helper Methods
    
    // Get available class names for a grade
    func getAvailableClassNames(for grade: Grade) -> [String] {
        switch grade {
        case .form1:
            return ["1A", "1B", "1C", "1D"]
        case .form2:
            return ["2A", "2B", "2C", "2D"]
        case .form3:
            return ["3A", "3B", "3C", "3D"]
        }
    }
    
    // Get current school year
    func getCurrentSchoolYear() -> String {
        let currentYear = Calendar.current.component(.year, from: Date())
        return "\(currentYear)-\(currentYear + 1)"
    }
    
    // Clear error message
    func clearError() {
        error = nil
    }
    
    // Debug method to check authentication status
    @MainActor
    func debugAuthenticationStatus() {
        let userManager = UserManager.shared
        print("🔍 DEBUG: Authentication Status Check")
        print("   - Is logged in: \(userManager.isLoggedIn)")
        print("   - User ID: '\(userManager.currentUser.id)'")
        print("   - Username: '\(userManager.currentUser.username)'")
        print("   - Role: '\(userManager.currentUser.role)'")
        print("   - School ID: '\(userManager.currentUser.school_id ?? "nil")'")
        
        // Check UserDefaults for tokens
        if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String] {
            print("   - 🔑 Tokens in UserDefaults:")
            print("     - AccessToken length: \(savedTokens["accessToken"]?.count ?? 0)")
            print("     - RefreshToken length: \(savedTokens["refreshToken"]?.count ?? 0)")
        } else {
            print("   - ❌ No tokens found in UserDefaults")
        }
        
        if let authHeaders = userManager.getAuthorizationHeader() {
            for (key, value) in authHeaders {
                print("   - ✅ \(key): Bearer \(value.replacingOccurrences(of: "Bearer ", with: "").prefix(20))...")
            }
        } else {
            print("   - ❌ No authorization header available")
        }
    }
    
    // Select a classroom
    func selectClassroom(_ classroom: Classroom) {
        selectedClassroom = classroom
        showingClassroomDetails = true
    }
    
    // MARK: - Debug Methods
    
    // Debug method to test school_id fetching
    @MainActor
    func debugSchoolIdFetching() async {
        let currentUser = UserManager.shared.currentUser
        print("🔍 DEBUG: Current user before profile fetch:")
        print("   - ID: \(currentUser.id)")
        print("   - Username: \(currentUser.username)")
        print("   - Name: \(currentUser.name)")
        print("   - Role: \(currentUser.role)")
        print("   - School ID: \(currentUser.school_id ?? "nil")")
        
        if currentUser.school_id == nil || currentUser.school_id?.isEmpty == true {
            print("🔍 DEBUG: Attempting to fetch user profile to get school_id...")
            let success = await UserManager.shared.fetchUserProfile()
            
            let updatedUser = UserManager.shared.currentUser
            print("🔍 DEBUG: Profile fetch result: \(success)")
            print("🔍 DEBUG: Updated user after profile fetch:")
            print("   - ID: \(updatedUser.id)")
            print("   - Username: \(updatedUser.username)")
            print("   - Name: \(updatedUser.name)")
            print("   - Role: \(updatedUser.role)")
            print("   - School ID: \(updatedUser.school_id ?? "nil")")
        } else {
            print("✅ DEBUG: School ID already available: \(currentUser.school_id!)")
        }
    }
    
    // MARK: - Enhanced Authentication Debugging
    
    // Debug method to thoroughly check authentication status and fix issues
    @MainActor
    func debugAndFixAuthentication() async -> Bool {
        print("🔍 DEBUG: Starting comprehensive authentication check...")
        
        // Step 1: Check basic authentication state
        let userManager = UserManager.shared
        print("📋 Step 1: Basic Authentication State")
        print("   - Is logged in: \(userManager.isLoggedIn)")
        print("   - User ID: '\(userManager.currentUser.id)'")
        print("   - Username: '\(userManager.currentUser.username)'")
        print("   - Role: '\(userManager.currentUser.role)'")
        print("   - School ID: '\(userManager.currentUser.school_id ?? "nil")'")
        
        // Step 2: Check token availability
        print("📋 Step 2: Token Status")
        if let authHeaders = userManager.getAuthorizationHeader() {
            for (key, value) in authHeaders {
                print("   ✅ \(key): Bearer \(value.replacingOccurrences(of: "Bearer ", with: "").prefix(20))...")
            }
        } else {
            print("   ❌ No authorization header available")
        }
        
        // Step 3: Check UserDefaults for saved tokens
        print("📋 Step 3: UserDefaults Token Check")
        if let savedTokens = UserDefaults.standard.dictionary(forKey: "authTokens") as? [String: String] {
            print("   - AccessToken length: \(savedTokens["accessToken"]?.count ?? 0)")
            print("   - RefreshToken length: \(savedTokens["refreshToken"]?.count ?? 0)")
            if let accessToken = savedTokens["accessToken"], accessToken.count > 20 {
                print("   - AccessToken prefix: \(accessToken.prefix(20))...")
            }
        } else {
            print("   ❌ No tokens found in UserDefaults")
        }
        
        // Step 4: Ensure valid authentication
        print("📋 Step 4: Attempting to ensure valid authentication...")
        let authValid = await userManager.ensureValidAuthentication()
        print("   - Authentication validation result: \(authValid)")
        
        if !authValid {
            print("❌ Authentication validation failed - user needs to re-login")
            return false
        }
        
        // Step 5: Test a simple authenticated request
        print("📋 Step 5: Testing authenticated request...")
        let testEndpoint = "user/profile"
        do {
            print("   - Testing endpoint: \(testEndpoint)")
            
            let _: User = try await userManager.api.get(testEndpoint)
            print("   ✅ Test request successful - authentication is working")
            return true
            
        } catch {
            print("   ❌ Test request failed: \(error)")
            
            // If it's an authentication error, try token refresh
            if let apiError = error as? APIError {
                switch apiError {
                case .unauthorized:
                    print("📋 Step 6: Authentication failed, attempting token refresh...")
                    let refreshSuccess = await userManager.refreshToken()
                    print("   - Token refresh result: \(refreshSuccess)")
                    
                    if refreshSuccess {
                        // Try the test request again
                        do {
                            let _: User = try await userManager.api.get(testEndpoint)
                            print("   ✅ Test request successful after token refresh")
                            return true
                        } catch {
                            print("   ❌ Test request still failing after refresh: \(error)")
                            return false
                        }
                    } else {
                        print("   ❌ Token refresh failed - user needs to re-login")
                        return false
                    }
                default:
                    print("   ❌ Non-authentication API error: \(apiError)")
                    return false
                }
            }
            return false
        }
    }
    
    // MARK: - Student Management
    
    @Published var availableStudents: [StudentSearchResult] = []
    @Published var isSearchingStudents = false
    @Published var studentSearchError: String?
    
    /// Search for students from the same school that can be added to a classroom
    @MainActor
    func searchStudentsFromSameSchool(
        schoolId: String,
        searchTerm: String? = nil,
        excludeClassroomId: String? = nil
    ) async {
        isSearchingStudents = true
        studentSearchError = nil
        
        do {
            // Use the actual backend API: GET /api/student/?school_id=X
            var urlComponents = URLComponents(string: "\(APIConfig.baseURL)/student/")!
            urlComponents.queryItems = [
                URLQueryItem(name: "school_id", value: schoolId)
            ]
            
            guard let url = urlComponents.url else {
                throw APIError.invalidURL
            }
            
            var urlRequest = URLRequest(url: url)
            urlRequest.httpMethod = "GET"
            
            // Add authorization header
            if let token = UserManager.shared.token {
                urlRequest.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
            
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("🔍 ClassroomViewModel: Search students response status: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 200 {
                    let searchApiResponse = try JSONDecoder().decode(StudentSearchApiResponse.self, from: data)
                    print("📝 ClassroomViewModel: Received \(searchApiResponse.students.count) students from API")
                    print("📝 ClassroomViewModel: School: \(searchApiResponse.school.schoolName) (ID: \(searchApiResponse.school.id))")
                    
                    // Convert to StudentSearchResult and filter
                    var searchResults: [StudentSearchResult] = []
                    
                    for apiResponse in searchApiResponse.students {
                        if let studentSearchResult = apiResponse.toStudentSearchResult(excludeClassroomId: excludeClassroomId) {
                            // Apply search term filter if provided
                            if let searchTerm = searchTerm, !searchTerm.isEmpty {
                                let lowercaseSearchTerm = searchTerm.lowercased()
                                if studentSearchResult.name.lowercased().contains(lowercaseSearchTerm) ||
                                    studentSearchResult.username.lowercased().contains(lowercaseSearchTerm) {
                                    searchResults.append(studentSearchResult)
                                }
                            } else {
                                searchResults.append(studentSearchResult)
                            }
                        }
                    }
                    
                    // Filter out students already in the classroom
                    if let excludeClassroomId = excludeClassroomId {
                        searchResults = searchResults.filter { !$0.isAlreadyInClassroom }
                    }
                    
                    self.availableStudents = searchResults
                    print("✅ ClassroomViewModel: Found \(searchResults.count) available students after filtering")
                    
                    // Debug: Print some student info
                    for student in searchResults.prefix(3) {
                        print("📝 Student: \(student.name) (@\(student.username)) - Already in classroom: \(student.isAlreadyInClassroom)")
                    }
                } else {
                    let errorResponse = try? JSONDecoder().decode(APIErrorResponse.self, from: data)
                    throw APIError.serverError(errorResponse?.message ?? "Failed to search students")
                }
            }
        } catch {
            print("❌ ClassroomViewModel: Search students error: \(error.localizedDescription)")
            self.studentSearchError = error.localizedDescription
        }
        
        isSearchingStudents = false
    }
    
    /// Add a student to a classroom
    @MainActor
    func addStudentToClassroom(_ student: StudentSearchResult, to classroom: Classroom) async -> Bool {
        do {
            // Use the actual backend API: POST /api/classroom/{id}/students
            guard let url = URL(string: "\(APIConfig.baseURL)/classroom/\(classroom.id)/students") else {
                throw APIError.invalidURL
            }
            
            let request = AddStudentsToClassroomRequest(
                studentIds: [student.id] // Backend expects array of student IDs
            )
            
            var urlRequest = URLRequest(url: url)
            urlRequest.httpMethod = "POST"
            urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Add authorization header
            if let token = UserManager.shared.token {
                urlRequest.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
            
            urlRequest.httpBody = try JSONEncoder().encode(request)
            
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("🎓 ClassroomViewModel: Add student response status: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 200 || httpResponse.statusCode == 201 {
                    // Parse response to check if the student was actually added
                    if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        print("📝 ClassroomViewModel: Add student response: \(jsonData)")
                    }
                    
                    // Update the local classroom with the new student
                    if let classroomIndex = classrooms.firstIndex(where: { $0.id == classroom.id }) {
                        let newStudent = student.toClassroomStudent()
                        classrooms[classroomIndex].students.append(newStudent)
                        
                        // Update selectedClassroom if it's the same one
                        if selectedClassroom?.id == classroom.id {
                            selectedClassroom?.students.append(newStudent)
                        }
                    }
                    
                    // Remove student from available students list
                    availableStudents.removeAll { $0.id == student.id }
                    
                    print("✅ ClassroomViewModel: Successfully added student '\(student.name)' to classroom '\(classroom.name)'")
                    return true
                } else {
                    // Try to parse error message from response
                    var errorMessage = "Failed to add student to classroom"
                    if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let message = jsonData["message"] as? String {
                        errorMessage = message
                    }
                    throw APIError.serverError(errorMessage)
                }
            }
        } catch {
            print("❌ ClassroomViewModel: Add student error: \(error.localizedDescription)")
            self.error = error.localizedDescription
        }
        
        return false
    }
    
    /// Add multiple students to a classroom (bulk operation)
    @MainActor
    func addStudentsToClassroom(_ students: [StudentSearchResult], to classroom: Classroom) async -> Bool {
        guard !students.isEmpty else { return true }
        
        do {
            // Use the actual backend API: POST /api/classroom/{id}/students
            guard let url = URL(string: "\(APIConfig.baseURL)/classroom/\(classroom.id)/students") else {
                throw APIError.invalidURL
            }
            
            let studentIds = students.map { $0.id }
            let request = AddStudentsToClassroomRequest(studentIds: studentIds)
            
            var urlRequest = URLRequest(url: url)
            urlRequest.httpMethod = "POST"
            urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Add authorization header
            if let token = UserManager.shared.token {
                urlRequest.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
            
            urlRequest.httpBody = try JSONEncoder().encode(request)
            
            let (data, response) = try await URLSession.shared.data(for: urlRequest)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("🎓 ClassroomViewModel: Add students response status: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 200 || httpResponse.statusCode == 201 {
                    // Parse response to check results
                    if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        print("📝 ClassroomViewModel: Add students response: \(jsonData)")
                    }
                    
                    // Update the local classroom with the new students
                    if let classroomIndex = classrooms.firstIndex(where: { $0.id == classroom.id }) {
                        let newStudents = students.map { $0.toClassroomStudent() }
                        classrooms[classroomIndex].students.append(contentsOf: newStudents)
                        
                        // Update selectedClassroom if it's the same one
                        if selectedClassroom?.id == classroom.id {
                            selectedClassroom?.students.append(contentsOf: newStudents)
                        }
                    }
                    
                    // Remove students from available students list
                    let addedStudentIds = Set(studentIds)
                    availableStudents.removeAll { addedStudentIds.contains($0.id) }
                    
                    print("✅ ClassroomViewModel: Successfully added \(students.count) students to classroom '\(classroom.name)'")
                    return true
                } else {
                    // Try to parse error message from response
                    var errorMessage = "Failed to add students to classroom"
                    if let jsonData = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let message = jsonData["message"] as? String {
                        errorMessage = message
                    }
                    throw APIError.serverError(errorMessage)
                }
            }
        } catch {
            print("❌ ClassroomViewModel: Add students error: \(error.localizedDescription)")
            self.error = error.localizedDescription
        }
        
        return false
    }
    
    /// Get the teacher's school ID (this would typically come from UserManager)
    func getTeacherSchoolId() -> String? {
        return UserManager.shared.currentUserSchoolId
    }
    // MARK: - Teacher Dashboard Statistics
    
    // Get aggregated dashboard statistics for the current teacher
    @MainActor
    func getTeacherDashboardStats() async -> TeacherDashboardStats {
        let currentUser = UserManager.shared.currentUser
        print("📊 ClassroomViewModel: Getting dashboard stats for teacher: \(currentUser.id)")
        
        var totalStudents = 0
        var activeStudents = 0
        var completedExercises = 0
        var pendingExercises = 0
        var activeCourses = 0
        
        // Load classrooms if not already loaded
        if classrooms.isEmpty {
            await loadClassrooms()
        }
        
        // Aggregate statistics from all classrooms
        for classroom in classrooms {
            totalStudents += classroom.students.count
            activeStudents += classroom.students.filter { $0.status == .active }.count
            
            // Get classroom stats if available
            if let stats = await loadClassroomStats(for: classroom) {
                completedExercises += stats.completedExercises
                pendingExercises += stats.pendingExercises
            }
        }
        
        // Calculate active courses (estimate based on classroom count and subjects)
        let uniqueSubjects = Set(classrooms.compactMap { $0.subject }).count
        activeCourses = max(uniqueSubjects, classrooms.count) // At least one course per classroom
        
        // Get advanced analytics
        let pendingGrading = await getPendingGradingItems()
        let overdueAlerts = await getOverdueAlerts()
        let performanceData = await getPerformanceAnalytics()
        let riskIndicators = await getStudentRiskIndicators()
        
        print("📊 ClassroomViewModel: Dashboard stats - Students: \(totalStudents), Active: \(activeStudents), Courses: \(activeCourses)")
        
        return TeacherDashboardStats(
            totalStudents: totalStudents,
            activeStudents: activeStudents,
            activeCourses: activeCourses,
            completedExercises: completedExercises,
            pendingExercises: pendingExercises,
            activeGames: 0, // Will be populated by GameRoomManager
            liveExercises: 0, // Will be populated by ExerciseViewModel
            pendingGrading: pendingGrading,
            overdueAlerts: overdueAlerts,
            performanceData: performanceData,
            riskIndicators: riskIndicators
        )
    }
    
    // MARK: - Advanced Analytics Methods
    
    func getPendingGradingItems() async -> [PendingGradingItem] {
        var pendingItems: [PendingGradingItem] = []
        let exerciseViewModel = ExerciseViewModel()
        
        for classroom in classrooms {
            do {
                // Get exercises for this classroom
                try await exerciseViewModel.getExercises(for: classroom.id)
                
                // For each exercise, get submissions that need grading
                for exercise in exerciseViewModel.exercises {
                    do {
                        let submissions = try await exerciseViewModel.getStudentSubmissions(for: exercise.id)
                        
                        for submission in submissions {
                            // Check if submission needs manual grading
                            let needsManualGrading = submission.gradingStatus == "pending" ||
                            submission.gradingStatus == nil ||
                            submission.answers.contains { $0.needsManualGrading == true }
                            
                            if needsManualGrading {
                                // Find student name
                                let student = classroom.students.first { $0.id == submission.studentId }
                                let studentName = student?.name ?? "Unknown Student"
                                
                                let item = PendingGradingItem(
                                    submissionId: submission.id,
                                    studentName: studentName,
                                    exerciseTitle: submission.exerciseTitle ?? exercise.title,
                                    submittedAt: submission.endTime ?? submission.startTime,
                                    needsManualGrading: needsManualGrading
                                )
                                pendingItems.append(item)
                            }
                        }
                    } catch {
                        print("Error getting submissions for exercise \(exercise.id): \(error)")
                    }
                }
            } catch {
                print("Error getting exercises for classroom \(classroom.id): \(error)")
            }
        }
        
        // Sort by submission date (oldest first)
        return pendingItems.sorted { $0.submittedAt < $1.submittedAt }
    }
    
    func getOverdueAlerts() async -> [OverdueAlert] {
        var overdueAlerts: [OverdueAlert] = []
        let exerciseViewModel = ExerciseViewModel()
        let now = Date()
        
        for classroom in classrooms {
            do {
                try await exerciseViewModel.getExercises(for: classroom.id)
                
                for exercise in exerciseViewModel.exercises {
                    if exercise.dueDate < now {
                        let overdueBy = now.timeIntervalSince(exercise.dueDate)
                        
                        // Get submissions to see how many students are affected
                        do {
                            let submissions = try await exerciseViewModel.getStudentSubmissions(for: exercise.id)
                            let submittedStudentIds = Set(submissions.map { $0.studentId })
                            let studentsAffected = classroom.students.filter { student in
                                !submittedStudentIds.contains(student.id) && student.status == .active
                            }.count
                            
                            if studentsAffected > 0 {
                                let alert = OverdueAlert(
                                    exerciseTitle: exercise.title,
                                    dueDate: exercise.dueDate,
                                    overdueBy: overdueBy,
                                    studentsAffected: studentsAffected,
                                    classroomName: classroom.name
                                )
                                overdueAlerts.append(alert)
                            }
                        } catch {
                            print("Error getting submissions for overdue exercise \(exercise.id): \(error)")
                        }
                    }
                }
            } catch {
                print("Error getting exercises for overdue alerts: \(error)")
            }
        }
        
        // Sort by how overdue they are (most overdue first)
        return overdueAlerts.sorted { $0.overdueBy > $1.overdueBy }
    }
    
    func getPerformanceAnalytics() async -> PerformanceAnalytics {
        let exerciseViewModel = ExerciseViewModel()
        var allSubmissions: [StudentSubmission] = []
        var topicScores: [String: [StudentSubmission]] = [:]
        
        // Collect all submissions across classrooms
        for classroom in classrooms {
            do {
                try await exerciseViewModel.getExercises(for: classroom.id)
                
                for exercise in exerciseViewModel.exercises {
                    do {
                        let submissions = try await exerciseViewModel.getStudentSubmissions(for: exercise.id)
                        allSubmissions.append(contentsOf: submissions)
                        
                        // Group by topic
                        if topicScores[exercise.topic] == nil {
                            topicScores[exercise.topic] = []
                        }
                        topicScores[exercise.topic]?.append(contentsOf: submissions)
                    } catch {
                        print("Error getting submissions for analytics: \(error)")
                    }
                }
            } catch {
                print("Error getting exercises for analytics: \(error)")
            }
        }
        
        // Calculate metrics
        let scores = allSubmissions.compactMap { submission in
            if let earned = submission.earnedPoints, let total = submission.totalPoints, total > 0 {
                return (earned / total) * 100.0
            } else if let score = submission.score {
                return score
            } else {
                return nil
            }
        }
        let averageScore = scores.isEmpty ? 0.0 : scores.reduce(0, +) / Double(scores.count)
        
        let completedCount = allSubmissions.filter { $0.endTime != nil }.count
        let totalAssigned = allSubmissions.count
        let completionRate = totalAssigned > 0 ? Double(completedCount) / Double(totalAssigned) * 100 : 0.0
        
        // Generate topic performance
        let topicPerformance = topicScores.map { topic, topicSubmissions in
            let topicScores = topicSubmissions.compactMap { submission in
                if let earned = submission.earnedPoints, let total = submission.totalPoints, total > 0 {
                    return (earned / total) * 100.0
                } else if let score = submission.score {
                    return score
                } else {
                    return nil
                }
            }
            let avg = topicScores.isEmpty ? 0.0 : topicScores.reduce(0, +) / Double(topicScores.count)
            return TopicPerformance(
                topic: topic,
                averageScore: avg,
                completionCount: topicScores.count
            )
        }.sorted { $0.averageScore > $1.averageScore }
        
        // Generate trend data (simplified - last 7 days average)
        let last7Days = (0..<7).map { dayOffset in
            let date = Calendar.current.date(byAdding: .day, value: -dayOffset, to: Date()) ?? Date()
            let dayStart = Calendar.current.startOfDay(for: date)
            let dayEnd = Calendar.current.date(byAdding: .day, value: 1, to: dayStart) ?? date
            
            let daySubmissions = allSubmissions.filter { submission in
                if let endTime = submission.endTime {
                    return endTime >= dayStart && endTime < dayEnd
                }
                return false
            }
            
            let dayScores = daySubmissions.compactMap { submission in
                if let earned = submission.earnedPoints, let total = submission.totalPoints, total > 0 {
                    return (earned / total) * 100.0
                } else if let score = submission.score {
                    return score
                } else {
                    return nil
                }
            }
            return dayScores.isEmpty ? 0.0 : dayScores.reduce(0, +) / Double(dayScores.count)
        }.reversed()
        
        return PerformanceAnalytics(
            averageScore: averageScore,
            completionRate: completionRate,
            scoresTrend: Array(last7Days),
            topicPerformance: topicPerformance
        )
    }
    
    func getStudentRiskIndicators() async -> [StudentRiskIndicator] {
        var riskIndicators: [StudentRiskIndicator] = []
        let exerciseViewModel = ExerciseViewModel()
        
        // Dictionary to track students we've already processed
        var processedStudentIds = Set<String>()
        
        // Dictionary to collect all submissions by student ID
        var allStudentSubmissions: [String: [StudentSubmission]] = [:]
        
        // First pass: collect all submissions for all students
        for classroom in classrooms {
            do {
                try await exerciseViewModel.getExercises(for: classroom.id)
                
                for exercise in exerciseViewModel.exercises {
                    do {
                        let submissions = try await exerciseViewModel.getStudentSubmissions(for: exercise.id)
                        
                        // Group submissions by student ID
                        for submission in submissions {
                            let studentId = submission.studentId
                            if allStudentSubmissions[studentId] == nil {
                                allStudentSubmissions[studentId] = []
                            }
                            allStudentSubmissions[studentId]?.append(submission)
                        }
                    } catch {
                        print("Error getting submissions for risk analysis: \(error)")
                    }
                }
            } catch {
                print("Error getting exercises for risk analysis: \(error)")
            }
        }
        
        // Second pass: analyze risk for each student (only once per student)
        for classroom in classrooms {
            for student in classroom.students {
                // Skip inactive students
                guard student.status == .active else { continue }
                
                // Skip students we've already processed
                guard !processedStudentIds.contains(student.id) else { continue }
                processedStudentIds.insert(student.id)
                
                // Get all submissions for this student
                let studentSubmissions = allStudentSubmissions[student.id] ?? []
                var reasons: [String] = []
                
                // Calculate risk factors
                let scores = studentSubmissions.compactMap { submission in
                    if let earned = submission.earnedPoints, let total = submission.totalPoints, total > 0 {
                        return (earned / total) * 100.0
                    } else if let score = submission.score {
                        return score
                    } else {
                        return nil
                    }
                }
                let averageScore = scores.isEmpty ? 0.0 : scores.reduce(0, +) / Double(scores.count)
                
                // Low performance
                if averageScore < 60 && !scores.isEmpty {
                    reasons.append("Low average score (\(Int(averageScore))%)")
                }
                
                // Late submissions
                let lateSubmissions = studentSubmissions.filter { $0.isLateSubmission == true }
                if lateSubmissions.count > 0 {
                    reasons.append("\(lateSubmissions.count) late submissions")
                }
                
                // Missing submissions (simplified check)
                let totalExercises = exerciseViewModel.exercises.count
                let completedExercises = studentSubmissions.filter { $0.endTime != nil }.count
                let missingCount = totalExercises - completedExercises
                if missingCount > 2 {
                    reasons.append("\(missingCount) missing submissions")
                }
                
                // Inactivity
                let lastActivity = studentSubmissions.compactMap { $0.endTime }.max() ?? student.joinedAt
                let daysSinceActivity = Calendar.current.dateComponents([.day], from: lastActivity, to: Date()).day ?? 0
                if daysSinceActivity > 7 {
                    reasons.append("No activity for \(daysSinceActivity) days")
                }
                
                // Determine risk level
                let riskLevel: RiskLevel
                if reasons.count >= 3 || averageScore < 40 {
                    riskLevel = .high
                } else if reasons.count >= 2 || averageScore < 60 {
                    riskLevel = .medium
                } else if reasons.count >= 1 || averageScore < 75 {
                    riskLevel = .low
                } else {
                    continue // No risk
                }
                
                let indicator = StudentRiskIndicator(
                    studentId: student.id, // Add student ID to the risk indicator
                    studentName: student.name,
                    riskLevel: riskLevel,
                    reasons: reasons,
                    lastActivity: lastActivity,
                    averageScore: averageScore
                )
                riskIndicators.append(indicator)
            }
        }
        
        // Sort by risk level (high first)
        return riskIndicators.sorted { first, second in
            let riskOrder: [RiskLevel] = [.high, .medium, .low]
            guard let firstIndex = riskOrder.firstIndex(of: first.riskLevel),
                  let secondIndex = riskOrder.firstIndex(of: second.riskLevel) else {
                return false
            }
            return firstIndex < secondIndex
        }
    }
}
