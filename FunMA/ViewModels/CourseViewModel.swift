//
//  TestVM.swift
//  Luminous Education
//
//  Created by <PERSON> on 10/9/2024.
//

import Foundation
import SwiftUI


@MainActor
class CoursesViewModel: ObservableObject {
    @Published var courses: [LessonCourse] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @Published var selectedCourse: String? = nil
    @ObservedObject private var userProgressViewModel = UserProgressViewModel()
    @ObservedObject private var userManager = UserManager.shared
    
    // This will store all courses loaded from API
    private var allCourses: [LessonCourse] = []
    
    init() {
        // Load courses on initialization
        Task {
            await loadCourses()
        }
    }

    func loadCourses() async {
        // Prevent reentrant calls
        if isLoading {
            print("DEBUG: ⚠️ Skipping loadCourses because isLoading is true")
            return
        }
        
        print("DEBUG: 🔄 Starting loadCourses for user: \(userManager.currentUser.username)")
        
        DispatchQueue.main.async {
            self.isLoading = true
            self.errorMessage = nil
        }
        
        // Get the current user's username
        let username = userManager.currentUser.username
        
        // Clear the progress records cache to ensure fresh data
        await MainActor.run {
            print("DEBUG: 🧹 Clearing userProgressRecords cache")
            userProgressViewModel.userProgressRecords = []
        }
        
        // Use the new API endpoint to get user's enrolled courses with progress in a single request
        do {
            // Build the URL with the username parameter
            var urlComponents = URLComponents(string: APIConfig.userCourseEndpoint)!
            urlComponents.queryItems = [URLQueryItem(name: "username", value: username)]
            
            guard let url = urlComponents.url else {
                errorMessage = "Invalid URL for user courses"
                isLoading = false
                print("DEBUG: ❌ Invalid URL for user courses")
                return
            }
            
            print("DEBUG: 📊 Fetching user courses from URL: \(url.absoluteString)")
            
            let (data, response) = try await URLSession.shared.data(from: url)
            
            // Print raw JSON response for debugging
            if let jsonString = String(data: data, encoding: .utf8) {
                print("DEBUG: 📄 Raw user courses API response: \(jsonString)")
            }
            
            // Check for HTTP errors
            guard let httpResponse = response as? HTTPURLResponse else {
                errorMessage = "Invalid response"
                isLoading = false
                print("DEBUG: ❌ Invalid response from user courses endpoint")
                return
            }
            
            print("DEBUG: 🔄 User courses API response code: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode != 200 {
                // Try to decode an error message if available
                if let errorJson = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorJson["error"] as? String {
                    self.errorMessage = errorMessage
                    print("DEBUG: ❌ Error from user courses API: \(errorMessage)")
                } else {
                    self.errorMessage = "Server error: \(httpResponse.statusCode)"
                    print("DEBUG: ❌ Server error from user courses API: \(httpResponse.statusCode)")
                }
                isLoading = false
                return
            }
            
            // Define a struct to match the API response format
            struct UserCourseProgress: Decodable {
                let courseId: String
                let courseName: String
                let progress: Int
                let score: Int
            }
            
            // Decode the response
            let userCourseProgresses: [UserCourseProgress]
            do {
                userCourseProgresses = try JSONDecoder().decode([UserCourseProgress].self, from: data)
                print("DEBUG: 🎯 Successfully decoded \(userCourseProgresses.count) user course progresses")
                
                // Print details of each user course progress
                for (index, courseProgress) in userCourseProgresses.enumerated() {
                    print("DEBUG: 📝 User Course Progress #\(index + 1):")
                    print("  - CourseID: \(courseProgress.courseId)")
                    print("  - Course Name: \(courseProgress.courseName)")
                    print("  - Progress: \(courseProgress.progress)%")
                    print("  - Score: \(courseProgress.score)")
                }
            } catch {
                print("DEBUG: ❌ Decoding error: \(error.localizedDescription)")
                // Try to print the raw response for debugging
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("DEBUG: ❌ Raw response that failed to decode: \(jsonString)")
                }
                errorMessage = "Failed to decode course data: \(error.localizedDescription)"
                isLoading = false
                return
            }
            
            // After getting the user course progress, fetch detailed progress information
            print("DEBUG: 🔄 Fetching detailed progress information for all courses")
            let progressRecords = await userProgressViewModel.fetchUserProgress(username: username)
            print("DEBUG: ✅ Fetched \(progressRecords.count) detailed progress records")
            
            // Convert the API response to Course objects
            var enrolledCourses: [LessonCourse] = []
            
            // Fetch detailed course information for each enrolled course
            for courseProgress in userCourseProgresses {
                print("DEBUG: 🔍 Processing course \(courseProgress.courseId)")
                
                // Fetch detailed course information
                if let course = await fetchCourseDetails(courseId: courseProgress.courseId) {
                    print("DEBUG: ✅ Successfully fetched details for course \(courseProgress.courseId)")
                    enrolledCourses.append(course)
                    
                    // Store the progress data directly without making a PUT request
                    userProgressViewModel.storeProgressData(
                        username: username,
                        courseId: courseProgress.courseId,
                        progress: courseProgress.progress,
                        score: courseProgress.score
                    )
                    print("DEBUG: 💾 Stored progress data for course \(courseProgress.courseId): Progress \(courseProgress.progress)%, Score: \(courseProgress.score)")
                } else {
                    // If we couldn't fetch detailed course info, create a basic course object
                    print("DEBUG: ⚠️ Failed to fetch details for course \(courseProgress.courseId), creating basic course")
                    let basicCourse = LessonCourse(
                        courseId: courseProgress.courseId,
                        name: courseProgress.courseName != "Unknown" ? courseProgress.courseName : "Course \(courseProgress.courseId)",
                        description: "Course description for \(courseProgress.courseName)",
                        difficulty: 1,
                        totalLessons: 10,
                        credit: 3,
                        image: "placeholder_image",
                        lessons: []
                    )
                    
                    enrolledCourses.append(basicCourse)
                    
                    // Store the progress data directly without making a PUT request
                    userProgressViewModel.storeProgressData(
                        username: username,
                        courseId: courseProgress.courseId,
                        progress: courseProgress.progress,
                        score: courseProgress.score
                    )
                    print("DEBUG: 💾 Stored progress data for basic course \(courseProgress.courseId): Progress \(courseProgress.progress)%, Score: \(courseProgress.score)")
                }
            }
            
            // Update the courses and allCourses properties
            DispatchQueue.main.async {
                print("DEBUG: 🔄 Updating UI with \(enrolledCourses.count) courses")
                self.courses = enrolledCourses
                self.allCourses = enrolledCourses
            }
            
        } catch {
            errorMessage = "Failed to load enrolled courses from server"
            print("DEBUG: ❌ Failed to load enrolled courses: \(error.localizedDescription)")
            
            // If API fetch fails, use empty arrays
            DispatchQueue.main.async {
                self.allCourses = []
                self.courses = []
                self.errorMessage = "Failed to load enrolled courses from server"
            }
        }
        
        DispatchQueue.main.async {
            self.isLoading = false
            print("DEBUG: ✅ loadCourses completed")
        }
    }
    
    // Method to fetch detailed course information
    private func fetchCourseDetails(courseId: String) async -> LessonCourse? {
        do {
            // Build the URL with the courseId parameter
            var urlComponents = URLComponents(string: APIConfig.courseEndpoint)!
            urlComponents.queryItems = [URLQueryItem(name: "courseId", value: courseId)]
            
            guard let url = urlComponents.url else {
                return nil
            }
            
            let (data, response) = try await URLSession.shared.data(from: url)
            
            // Check for HTTP errors
            guard let httpResponse = response as? HTTPURLResponse else {
                print("Invalid response for course \(courseId)")
                return nil
            }
            
            if httpResponse.statusCode != 200 {
                // Try to decode an error message if available
                if let errorJson = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorJson["error"] as? String {
                    print("Error fetching course \(courseId): \(errorMessage)")
                } else {
                    print("Server error fetching course \(courseId): \(httpResponse.statusCode)")
                }
                return nil
            }
            
            // Try to decode the response
            do {
                // The API returns an array with a single course
                let courses = try JSONDecoder().decode([LessonCourse].self, from: data)
                
                // Return the first (and only) course in the array
                if let course = courses.first {
                    return course
                }
                
                return nil
            } catch {
                print("Decoding error for course \(courseId): \(error.localizedDescription)")
                // Try to print the raw response for debugging
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("Raw response: \(jsonString)")
                }
                return nil
            }
        } catch {
            print("Error fetching course details for \(courseId): \(error.localizedDescription)")
            return nil
        }
    }
    
    // Method to refresh courses data
    func refreshCourses() {
        Task {
            await loadCourses()
        }
    }
    
    // Method to update course progress
    func updateCourseProgress(courseId: String, newProgress: Int) async -> Bool {
        // Get the user's username
        let username = userManager.currentUser.username
        
        // Create a progress update payload
        let success = await userProgressViewModel.updateUserProgress(
            username: username,
            courseId: courseId,
            newProgress: newProgress
        )
        
        // If the update was successful, refresh our course list
        if success {
            await loadCourses()
        }
        
        return success
    }
    
    // Compatibility method for existing code
    func updateCourseProgress(courseId: String, newProgress: Int, completion: @escaping (Bool) -> Void) {
        Task {
            let success = await updateCourseProgress(courseId: courseId, newProgress: newProgress)
            completion(success)
        }
    }
    
    // Method to get progress for a specific course
    func getProgressForCourse(courseID: String) -> Int {
        // Get the user's username
        let username = userManager.currentUser.username
        
        print("DEBUG: 🔍 getProgressForCourse called for course \(courseID) and user \(username)")
        
        // Find the progress record for this course and user
        if let progressRecord = userProgressViewModel.getProgressRecord(username: username, courseId: courseID) {
            print("DEBUG: ✅ Found local progress record for course \(courseID): \(progressRecord.progress)%")
            return progressRecord.progress
        }
        
        print("DEBUG: ⚠️ No local progress record found for course \(courseID), will trigger async fetch")
        
        // If we don't have a local record, trigger an async fetch to update it for next time
        // This ensures we'll have the data next time this method is called
        Task {
            print("DEBUG: 🔄 Async fetching progress for course \(courseID) from server...")
            let records = await userProgressViewModel.fetchUserProgress(username: username, courseId: courseID)
            if let record = records.first {
                print("DEBUG: ✅ Retrieved progress from server for course \(courseID): \(record.progress)%")
            } else {
                print("DEBUG: ❌ No progress record found on server for course \(courseID)")
            }
        }
        
        // Return 0 if no progress record is found
        print("DEBUG: ⚠️ Returning default progress (0) for course \(courseID)")
        return 0
    }
    
    // Method to find the next lesson to start based on lesson progress status
    func findNextLessonToStart(courseID: String, lessons: [Lesson]) -> Lesson? {
        // If there are no lessons, return nil
        if lessons.isEmpty {
            return nil
        }
        
        // Get the user's username
        let username = userManager.currentUser.username
        
        // Try to get the progress record for this course
        if let progressRecord = userProgressViewModel.getProgressRecord(username: username, courseId: courseID) {
            // Check if there are any lessons with "Not Started" status
            if let notStartedLesson = progressRecord.lessonsProgress.first(where: { $0.status == "Not Started" }) {
                // Find the corresponding lesson in the lessons array
                return lessons.first(where: { $0.lessonID == notStartedLesson.lessonId })
            }
            
            // If no "Not Started" lessons found, return the first lesson that isn't marked as "Completed"
            if let incompleteLesson = progressRecord.lessonsProgress.first(where: { $0.status != "Completed" }) {
                return lessons.first(where: { $0.lessonID == incompleteLesson.lessonId })
            }
        }
        
        // If no progress record or all lessons are completed, fallback to using overall progress
        let progressCount = getProgressForCourse(courseID: courseID)
        
        // If no progress, start with the first lesson
        if progressCount == 0 {
            return lessons.first
        }
        
        // If some progress but less than total lessons, show the next lesson based on the count
        if progressCount < lessons.count {
            return lessons[Int(progressCount)]
        }
        
        // If all lessons completed, return the last lesson
        return lessons.last
    }
    
    // Method to enroll in a course (delegates to MarketplaceViewModel)
    func purchaseCourse(_ course: Course) async -> Result<Bool, Error> {
        // Create a marketplace view model to handle the purchase
        let marketplaceViewModel = MarketplaceViewModel()
        
        // Delegate to the marketplace view model
        return await marketplaceViewModel.purchaseCourse(course)
    }
    
    // Helper function to format price for display
    func formatPrice(_ price: Double) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "HKD"
        formatter.currencySymbol = "HK$"
        
        return formatter.string(from: NSNumber(value: price)) ?? "HK$\(price)"
    }
    
    // Ensure we load fresh data when we need it
    func ensureFreshData() {
        Task {
            // Get the current user's username
            let username = userManager.currentUser.username
            
            // First, clear any cached progress data
            await MainActor.run {
                userProgressViewModel.userProgressRecords = []
            }
            
            // Fetch the latest progress data using GET request
            _ = await userProgressViewModel.fetchUserProgress(username: username)
            
            // Then load courses
            await loadCourses()
        }
    }
}

// Clean container struct for Codable
struct CoursesContainer: Decodable {
    let courses: [LessonCourse]
}

@MainActor
class UserProgressViewModel: ObservableObject {
    @Published var userProgressRecords: [UserProgress] = []
    
    // Method to fetch progress data from API using GET request
    func fetchUserProgress(username: String, courseId: String? = nil) async -> [UserProgress] {
        do {
            // Build the URL with query parameters
            var urlComponents = URLComponents(string: APIConfig.userProgressEndpoint)!
            var queryItems = [URLQueryItem(name: "username", value: username)]
            
            // If courseId is provided, add it to the query
            if let courseId = courseId {
                queryItems.append(URLQueryItem(name: "courseId", value: courseId))
            }
            
            urlComponents.queryItems = queryItems
            
            guard let url = urlComponents.url else {
                print("DEBUG: Invalid URL for fetching user progress")
                return []
            }
            
            print("DEBUG: 📊 Fetching progress from URL: \(url.absoluteString)")
            
            let (data, response) = try await URLSession.shared.data(from: url)
            
            // Print raw JSON response for debugging
            if let jsonString = String(data: data, encoding: .utf8) {
                print("DEBUG: 📄 Raw progress API response: \(jsonString)")
            }
            
            // Check for HTTP errors
            guard let httpResponse = response as? HTTPURLResponse else {
                print("DEBUG: ❌ Invalid response when fetching progress")
                return []
            }
            
            print("DEBUG: 🔄 Progress API response code: \(httpResponse.statusCode)")
            
            if httpResponse.statusCode != 200 {
                // Try to decode an error message if available
                if let errorJson = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorJson["error"] as? String {
                    print("DEBUG: ❌ Error fetching progress: \(errorMessage)")
                } else {
                    print("DEBUG: ❌ Server error fetching progress: \(httpResponse.statusCode)")
                }
                return []
            }
            
            // Decode the response
            let progressRecords: [UserProgress]
            do {
                progressRecords = try JSONDecoder().decode([UserProgress].self, from: data)
                print("DEBUG: 🎯 Successfully decoded \(progressRecords.count) progress records")
                
                // Print details of each progress record
                for (index, record) in progressRecords.enumerated() {
                    print("DEBUG: 📝 Progress Record #\(index + 1):")
                    print("  - ID: \(record.id)")
                    print("  - Username: \(record.username)")
                    print("  - CourseID: \(record.courseId)")
                    print("  - Progress: \(record.progress)%")
                    print("  - Score: \(record.score)")
                    print("  - Lessons Progress: \(record.lessonsProgress.count) lessons")
                    
                    // Print individual lesson progress if available
                    for (lessonIndex, lessonProgress) in record.lessonsProgress.enumerated() {
                        print("    📚 Lesson #\(lessonIndex + 1): ID: \(lessonProgress.lessonId), Number: \(lessonProgress.lessonNumber), Score: \(lessonProgress.score), Status: \(lessonProgress.status)")
                    }
                }
            } catch {
                print("DEBUG: ❌ Error decoding progress data: \(error.localizedDescription)")
                if let jsonString = String(data: data, encoding: .utf8) {
                    print("DEBUG: ❌ JSON that failed to decode: \(jsonString)")
                }
                return []
            }
            
            // Update our local cache
            await MainActor.run {
                // If we fetched for a specific course, only update that record
                if let courseId = courseId, let record = progressRecords.first {
                    if let index = userProgressRecords.firstIndex(where: { 
                        $0.username == username && $0.courseId == courseId 
                    }) {
                        print("DEBUG: 🔄 Updating existing progress record for course \(courseId)")
                        userProgressRecords[index] = record
                    } else {
                        print("DEBUG: ➕ Adding new progress record for course \(courseId)")
                        userProgressRecords.append(record)
                    }
                } else {
                    // If we fetched all progress records, replace our entire cache
                    print("DEBUG: 🔄 Replacing entire progress cache with \(progressRecords.count) records")
                    userProgressRecords = progressRecords
                }
                
                // Print the current state of the cache
                print("DEBUG: 📋 Current progress cache contains \(userProgressRecords.count) records:")
                for (index, record) in userProgressRecords.enumerated() {
                    print("DEBUG:   #\(index + 1): Course \(record.courseId) - Progress: \(record.progress)%, Score: \(record.score)")
                }
            }
            
            return progressRecords
        } catch {
            print("DEBUG: ❌ Error fetching user progress: \(error.localizedDescription)")
            return []
        }
    }
    
    // Store progress data locally without making API requests
    func storeProgressData(username: String, courseId: String, progress: Int, score: Int) {
        let progressRecord = UserProgress(
            id: UUID().uuidString, // Convert UUID to String
            username: username,
            courseId: courseId,
            progress: progress,
            score: score,
            lessonsProgress: [] // Initialize with empty lessons progress
        )
        
        // Add or update the progress record
        addProgressRecord(progressRecord)
        print("Stored progress data for course \(courseId): Progress \(progress), Score: \(score)")
    }
    
    // Method to add a progress record without triggering notifications
    func addProgressRecord(_ progress: UserProgress) {
        // Check if the record already exists
        if let index = userProgressRecords.firstIndex(where: { 
            $0.username == progress.username && $0.courseId == progress.courseId 
        }) {
            // Update existing record
            userProgressRecords[index] = progress
        } else {
            // Add new record
            userProgressRecords.append(progress)
        }
    }
    
    // Method to get a progress record
    func getProgressRecord(username: String, courseId: String) -> UserProgress? {
        return userProgressRecords.first(where: { 
            $0.username == username && $0.courseId == courseId 
        })
    }
    
    // Method to update a progress record
    func updateUserProgress(username: String, courseId: String, newProgress: Int, newScore: Int? = nil) async -> Bool {
        // Check if we already have this progress value stored locally
        if let existingRecord = getProgressRecord(username: username, courseId: courseId) {
            // If the progress value is the same, no need to update the server
            if existingRecord.progress == newProgress && 
               (newScore == nil || existingRecord.score == newScore) {
                print("Skipping API call - progress already up to date for course \(courseId)")
                return true
            }
        }
        
        // Create update payload
        struct UpdateProgressPayload: Encodable {
            let username: String
            let courseId: String
            let progress: Int
            let score: Int?
        }
        
        let payload = UpdateProgressPayload(
            username: username,
            courseId: courseId,
            progress: newProgress,
            score: newScore
        )
        
        do {
            let url = URL(string: APIConfig.userProgressEndpoint)!
            var request = URLRequest(url: url)
            request.httpMethod = "PUT"
            request.addValue("application/json", forHTTPHeaderField: "Content-Type")
            
            let jsonData = try JSONEncoder().encode(payload)
            request.httpBody = jsonData
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("Invalid response when updating progress")
                return false
            }
            
            if httpResponse.statusCode != 200 {
                // Try to decode an error message if available
                if let errorJson = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorJson["error"] as? String {
                    print("Error updating progress: \(errorMessage)")
                } else {
                    print("Server error updating progress: \(httpResponse.statusCode)")
                }
                return false
            }
            
            // Update the local record
            if let index = userProgressRecords.firstIndex(where: { 
                $0.username == username && $0.courseId == courseId 
            }) {
                let updatedProgress = userProgressRecords[index]
                let newRecord = UserProgress(
                    id: updatedProgress.id,
                    username: updatedProgress.username,
                    courseId: updatedProgress.courseId,
                    progress: newProgress,
                    score: newScore ?? updatedProgress.score,
                    lessonsProgress: updatedProgress.lessonsProgress
                )
                userProgressRecords[index] = newRecord
            }
            
            return true
        } catch {
            print("Error updating progress: \(error.localizedDescription)")
            return false
        }
    }
}
