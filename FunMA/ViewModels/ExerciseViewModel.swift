import Foundation
import SwiftUI

@MainActor
class ExerciseViewModel: ObservableObject {
    @Published var exercises: [Exercise] = []
    @Published var isLoading = false
    @Published var error: String?
    
    init() {
        // Initialize with empty state
    }
    
    /// Load all exercises for the current teacher
    func getAllExercises() async throws {
        isLoading = true
        error = nil
        
        do {
            guard let url = URL(string: "\(APIConfig.baseURL)/api/teacher/exercises") else {
                throw APIError.invalidURL
            }
            
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            
            // Add authorization header
            if let token = UserManager.shared.token {
                request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
            }
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            if let httpResponse = response as? HTTPURLResponse {
                print("🎯 ExerciseViewModel: Get exercises response status: \(httpResponse.statusCode)")
                
                if httpResponse.statusCode == 200 {
                    let exercisesList = try JSONDecoder().decode([Exercise].self, from: data)
                    self.exercises = exercisesList
                    print("✅ ExerciseViewModel: Loaded \(exercisesList.count) exercises")
                } else {
                    let errorResponse = try? JSONDecoder().decode(APIErrorResponse.self, from: data)
                    throw APIError.serverError(errorResponse?.message ?? "Failed to load exercises")
                }
            }
        } catch {
            print("❌ ExerciseViewModel: Load exercises error: \(error.localizedDescription)")
            self.error = error.localizedDescription
            throw error
        }
        
        isLoading = false
    }
    
    /// Clear any error messages
    func clearError() {
        error = nil
    }
} 