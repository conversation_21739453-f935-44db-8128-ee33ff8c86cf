import Foundation
import SwiftUI

@MainActor
class MarketplaceViewModel: ObservableObject {
    @Published var courses: [Course] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String? = nil
    @ObservedObject private var userManager = UserManager.shared
    
    init() {
        // Load courses on initialization
        Task {
            await fetchAllCourses()
        }
    }
    
    // Fetch all available courses from the API
    func fetchAllCourses() async {
        // Prevent reentrant calls
        if isLoading {
            return
        }
        
        DispatchQueue.main.async {
            self.isLoading = true
            self.errorMessage = nil
        }
        
        do {
            // Create URL for the API endpoint
            guard let url = URL(string: APIConfig.courseEndpoint) else {
                throw NSError(domain: "MarketplaceViewModel", code: 400, 
                             userInfo: [NSLocalizedDescriptionKey: "Invalid URL"])
            }
            
            // Create and configure URL request
            var request = URLRequest(url: url)
            request.httpMethod = "GET"
            request.addValue("application/json", forHTTPHeaderField: "Accept")
            
            // Add authorization header if user is logged in
            if let authHeader = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeader {
                    request.addValue(value, forHTTPHeaderField: key)
                }
            }
            
            // Perform the request
            let (data, response) = try await URLSession.shared.data(for: request)
            
            // Check response status code
            guard let httpResponse = response as? HTTPURLResponse else {
                throw NSError(domain: "MarketplaceViewModel", code: 400, 
                             userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
            }
            
            if httpResponse.statusCode != 200 {
                // Try to decode an error message if available
                if let errorJson = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let errorMessage = errorJson["error"] as? String {
                    throw NSError(domain: "MarketplaceViewModel", code: httpResponse.statusCode, 
                                 userInfo: [NSLocalizedDescriptionKey: errorMessage])
                } else {
                    throw NSError(domain: "MarketplaceViewModel", code: httpResponse.statusCode, 
                                 userInfo: [NSLocalizedDescriptionKey: "Server error: \(httpResponse.statusCode)"])
                }
            }
            
            // Decode the JSON response
            let fetchedCourses = try JSONDecoder().decode([Course].self, from: data)
            
            // For each course, check if the user is already enrolled
            let username = userManager.currentUser.username
            if !username.isEmpty {
                await markEnrolledCourses(courses: fetchedCourses, username: username)
            } else {
                // Update courses on the main thread
                await MainActor.run {
                    self.courses = fetchedCourses
                    self.isLoading = false
                }
            }
        } catch {
            // Handle errors
            print("Error fetching courses: \(error.localizedDescription)")
            await MainActor.run {
                self.errorMessage = "Failed to fetch courses: \(error.localizedDescription)"
                self.isLoading = false
            }
        }
    }
    
    // Helper to mark which courses the user is enrolled in
    private func markEnrolledCourses(courses: [Course], username: String) async {
        do {
            // Get user's enrolled courses
            var urlComponents = URLComponents(string: APIConfig.userCourseEndpoint)!
            urlComponents.queryItems = [URLQueryItem(name: "username", value: username)]
            
            guard let url = urlComponents.url else {
                throw NSError(domain: "MarketplaceViewModel", code: 400, 
                             userInfo: [NSLocalizedDescriptionKey: "Invalid URL for user courses"])
            }
            
            var request = URLRequest(url: url)
            
            if let authHeaders = UserManager.shared.getAuthorizationHeader() {
                for (key, value) in authHeaders {
                    request.addValue(value, forHTTPHeaderField: key)
                }
            }
            
            let (data, response) = try await URLSession.shared.data(from: url)
            
            guard let httpResponse = response as? HTTPURLResponse, httpResponse.statusCode == 200 else {
                throw NSError(domain: "MarketplaceViewModel", code: 400, 
                             userInfo: [NSLocalizedDescriptionKey: "Failed to fetch enrolled courses"])
            }
            
            // Define a struct to match the API response format
            struct UserCourseProgress: Decodable {
                let courseId: String
                let courseName: String
                let progress: Int
                let score: Int
            }
            
            let enrolledCourses = try JSONDecoder().decode([UserCourseProgress].self, from: data)
            let enrolledCourseIds = Set(enrolledCourses.map { $0.courseId })
            
            // Mark courses as enrolled
            var marketplaceCourses = courses
            for i in 0..<marketplaceCourses.count {
                // Create a mutable copy of the course, update isEnrolled, and replace in array
                var course = marketplaceCourses[i]
                if enrolledCourseIds.contains(course.courseID) {
                    course.isEnrolled = true
                    marketplaceCourses[i] = course
                }
            }
            
            // Update courses on the main thread
            await MainActor.run {
                self.courses = marketplaceCourses
                self.isLoading = false
            }
        } catch {
            print("Error marking enrolled courses: \(error.localizedDescription)")
            await MainActor.run {
                self.courses = courses  // Just use the original unmarked courses
                self.isLoading = false
            }
        }
    }
    
    // Method to enroll in a course
    func purchaseCourse(_ course: Course) async -> Result<Bool, Error> {
        // Set loading state
        DispatchQueue.main.async {
            self.isLoading = true
        }
        
        do {
            // Create a defer block to ensure loading state is reset
            defer {
                DispatchQueue.main.async {
                    self.isLoading = false
                }
            }
            
            // Ensure we have a current user with a valid username
            let username = UserManager.shared.currentUser.username
            if username.isEmpty {
                return .failure(NSError(
                    domain: "MarketplaceViewModel",
                    code: 401,
                    userInfo: [NSLocalizedDescriptionKey: "Username is empty"]
                ))
            }
            
            // Create the enrollment payload
            let enrollmentPayload = [
                "username": username,
                "courseId": course.courseID
            ]
            
            // Convert payload to JSON data
            let jsonData = try JSONSerialization.data(withJSONObject: enrollmentPayload)
            
            // Create the request
            let url = URL(string: "\(APIConfig.baseURL)/enrollCourse")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.httpBody = jsonData
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            
            // Send the request
            let (_, response) = try await URLSession.shared.data(for: request)
            
            // Check response
            if let httpResponse = response as? HTTPURLResponse {
                if (200...299).contains(httpResponse.statusCode) {
                    return .success(true)
                } else {
                    return .failure(NSError(
                        domain: "MarketplaceViewModel",
                        code: httpResponse.statusCode,
                        userInfo: [NSLocalizedDescriptionKey: "Enrollment failed: \(httpResponse.statusCode)"]
                    ))
                }
            }
            
            // Return a default error if the response couldn't be cast to HTTPURLResponse
            return .failure(NSError(
                domain: "MarketplaceViewModel",
                code: 0,
                userInfo: [NSLocalizedDescriptionKey: "Invalid server response"]
            ))
        } catch {
            return .failure(error)
        }
    }
    
} 