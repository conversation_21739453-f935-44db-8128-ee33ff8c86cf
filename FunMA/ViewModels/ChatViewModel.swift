//
//  ChatViewModel.swift
//  Luminous Education
//
//  Created by <PERSON> on 2/3/2025.
//

import Foundation
import Combine

class ChatViewModel: ObservableObject {
    @Published var messages: [ChatMessage] = []
    @Published var newMessageText: String = ""
    @Published var isLoading: Bool = false
    @Published var error: String? = nil
    @Published var currentStreamingResponse: String = ""
    @Published var isStreaming: Bool = false
    
    private let anythingLLMService = AnythingLLMService()
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        // Add the initial welcome message
        messages.append(ChatMessage(
            content: "Hello! I'm your AI learning assistant. What could I help you with today?",
            isUser: false,
            role: .assistant
        ))
        
        // Subscribe to changes in the API service state
        anythingLLMService.$isLoading
            .assign(to: &$isLoading)
        
        anythingLLMService.$error
            .assign(to: &$error)
    }
    
    // Get visible messages (excluding system prompts)
    var visibleMessages: [ChatMessage] {
        return messages.filter { $0.role != .system }
    }
    
    // Send a new message with streaming response
    func sendMessage() {
        guard !newMessageText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }
        
        // Add user message to the chat
        let userMessage = ChatMessage(
            content: newMessageText,
            isUser: true,
            role: .user
        )
        messages.append(userMessage)
        
        // Clear the input field
        newMessageText = ""
        
        // Create a placeholder for the streaming response
        currentStreamingResponse = ""
        isStreaming = true
        
        // Add a temporary message for the streaming response
        let assistantMessage = ChatMessage(
            content: "",
            isUser: false,
            role: .assistant
        )
        messages.append(assistantMessage)
        
        // Send only user messages to the API (system prompt is handled server-side)
        let userMessages = messages.filter { $0.role == .user }
        anythingLLMService.sendStreamingMessage(messages: userMessages)
            .sink(
                receiveCompletion: { [weak self] completion in
                    guard let self = self else { return }
                    
                    self.isStreaming = false
                    
                    if case .failure(let error) = completion {
                        self.error = error.localizedDescription
                        
                        // Remove the placeholder message on error
                        if let lastIndex = self.messages.lastIndex(where: { !$0.isUser && $0.content.isEmpty }) {
                            self.messages.remove(at: lastIndex)
                        }
                    } else {
                        // Update the placeholder message with the complete response
                        if let lastIndex = self.messages.lastIndex(where: { !$0.isUser }) {
                            let finalMessage = ChatMessage(
                                content: self.currentStreamingResponse,
                                isUser: false,
                                role: .assistant
                            )
                            self.messages[lastIndex] = finalMessage
                        }
                    }
                },
                receiveValue: { [weak self] streamText in
                    guard let self = self else { return }
                    
                    // Append the new text to the streaming response
                    self.currentStreamingResponse += streamText
                    
                    // Update the placeholder message with the current streaming text
                    if let lastIndex = self.messages.lastIndex(where: { !$0.isUser }) {
                        let updatedMessage = ChatMessage(
                            content: self.currentStreamingResponse,
                            isUser: false,
                            role: .assistant
                        )
                        self.messages[lastIndex] = updatedMessage
                    }
                }
            )
            .store(in: &cancellables)
    }
    
    // Clear all messages and start a new conversation
    func clearChat() {
        // Reset messages with welcome message only
        messages = [
            ChatMessage(
                content: "Hello! I'm your AI learning assistant. What could I help you with today?",
                isUser: false, 
                role: .assistant
            )
        ]
        
        // Reset streaming state
        currentStreamingResponse = ""
        isStreaming = false
        
        // Reset the AnythingLLM session
        anythingLLMService.resetSession()
    }
} 
