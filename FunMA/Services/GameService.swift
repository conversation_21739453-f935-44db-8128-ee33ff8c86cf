//
//  GameService.swift
//  FunMA
//
//  Created by ki on 5/6/2025.
//

import Foundation
import Network
import Combine

class GameService: NSObject, ObservableObject {
    static let shared = GameService()
    private let baseURL = APIConfig.gameEndpoint
    private let wsURL = APIConfig.webSocketGameEndpoint
    private let session: URLSession
    private let monitor: NWPathMonitor
    private var isNetworkAvailable = false
    private var webSocketTask: URLSessionWebSocketTask?
    var isConnected = false
    private var connectionAttempts = 0
    private let maxConnectionAttempts = 3
    
    // Store available games for URL lookup
    private var availableGames: [Game] = []
    
    // Cache for MongoDB data to avoid repeated API calls
    private var gamesCache: [String: [Game]] = [:] // Key: "grade_topic"
    private var topicsCache: [String: [Topic]] = [:] // Key: grade
    private var gradesCache: [Grade] = []
    
    // Connection state management
    private var isConnecting = false
    private var connectionTimer: Timer?
    private let connectionTimeout: TimeInterval = 5.0 // Reduced from 10.0 since we use initial ping
    
    // Message queue for when WebSocket is connecting
    private var messageQueue: [WebSocketMessage] = []
    
    // Add response wrappers to match server response format
    private struct APIResponse<T: Codable>: Codable {
        let status: String
        let message: String
        let data: T?
    }
    
    // Response structure for grades endpoint
    private struct GradesData: Codable {
        let grades: [GradeInfo]
        let totalGrades: Int
        
        enum CodingKeys: String, CodingKey {
            case grades
            case totalGrades = "total_grades"
        }
    }
    
    private struct GradeInfo: Codable {
        let grade: String
        let totalGames: Int
        let topics: [String]
        
        enum CodingKeys: String, CodingKey {
            case grade
            case totalGames = "total_games"
            case topics
        }
    }
    
    // Response structure for games with grade and topic
    private struct GradeTopicGames: Codable {
        let grade: String
        let topic: String
        let games: [Game]
    }
    
    // Response structure for games endpoint
    private struct GameResponse: Codable {
        let id: String
        let grade: String
        let topic: String
        let games: [MongoDBGameInfo]
        
        enum CodingKeys: String, CodingKey {
            case id = "_id"
            case grade
            case topic
            case games
        }
    }
    
    private struct MongoDBGameInfo: Codable {
        let subTopic: String
        let gameName: String
        let description: String
        let gameType: String
        let url: String
        let gameId: String
        
        enum CodingKeys: String, CodingKey {
            case subTopic = "sub_topic"
            case gameName = "gamename"
            case description
            case gameType = "game_type"
            case url
            case gameId = "game_id"
        }
    }
    
    // WebSocket message types for server communication - Updated for MongoDB integration
    enum WebSocketMessage: Codable {
        case createRoom(grade: String, topic: String, game: String, teacherId: String, teacherName: String, gameId: String, gameUrl: String?)
        case joinRoom(pin: String, studentName: String, studentId: String)
        case leaveRoom
        case startGame
        case endGame
        
        // Server response cases - Updated to support enhanced format
        case roomCreated(room: GameRoom)
        case roomJoined(room: GameRoom)
        case playerJoined(player: Player)
        case playerLeft(playerId: String)
        case gameStarted(data: GameStartedData?)
        case gameEnded(data: GameEndedData?)
        case roomClosed
        case gameUrl(url: String)
        case ping
        case pong
        
        enum CodingKeys: String, CodingKey {
            case type
            case data
            case grade
            case topic
            case game
            case teacherId
            case teacherName
            case gameId
            case gameUrl
            case pin
            case studentName
            case studentId
            case room
            case player
            case playerId
            case url
        }
        
        func encode(to encoder: Encoder) throws {
            var container = encoder.container(keyedBy: CodingKeys.self)
            
            switch self {
            case .createRoom(let grade, let topic, let game, let teacherId, let teacherName, let gameId, let gameUrl):
                try container.encode("createRoom", forKey: .type)
                try container.encode(grade, forKey: .grade)
                try container.encode(topic, forKey: .topic)
                try container.encode(game, forKey: .game)
                try container.encode(teacherId, forKey: .teacherId)
                try container.encode(teacherName, forKey: .teacherName)
                try container.encode(gameId, forKey: .gameId)
                if let gameUrl = gameUrl {
                    try container.encode(gameUrl, forKey: .gameUrl)
                }
                
            case .joinRoom(let pin, let studentName, let studentId):
                try container.encode("joinRoom", forKey: .type)
                try container.encode(pin, forKey: .pin)
                try container.encode(studentName, forKey: .studentName)
                try container.encode(studentId, forKey: .studentId)
                
            case .leaveRoom:
                try container.encode("leaveRoom", forKey: .type)
                
            case .startGame:
                try container.encode("startGame", forKey: .type)
                
            case .endGame:
                try container.encode("endGame", forKey: .type)
                
            case .roomCreated(let room):
                try container.encode("roomCreated", forKey: .type)
                try container.encode(room, forKey: .room)
                
            case .roomJoined(let room):
                try container.encode("roomJoined", forKey: .type)
                try container.encode(room, forKey: .room)
                
            case .playerJoined(let player):
                try container.encode("playerJoined", forKey: .type)
                try container.encode(player, forKey: .player)
                
            case .playerLeft(let playerId):
                try container.encode("playerLeft", forKey: .type)
                try container.encode(playerId, forKey: .playerId)
                
            case .gameStarted(let data):
                try container.encode("gameStarted", forKey: .type)
                if let data = data {
                    try container.encode(data, forKey: .data)
                }
                
            case .gameEnded(let data):
                try container.encode("gameEnded", forKey: .type)
                if let data = data {
                    try container.encode(data, forKey: .data)
                }
                
            case .roomClosed:
                try container.encode("roomClosed", forKey: .type)
                
            case .gameUrl(let url):
                try container.encode("gameUrl", forKey: .type)
                try container.encode(url, forKey: .url)
                
            case .ping:
                try container.encode("ping", forKey: .type)
                
            case .pong:
                try container.encode("pong", forKey: .type)
            }
        }
        
        init(from decoder: Decoder) throws {
            let container = try decoder.container(keyedBy: CodingKeys.self)
            let type = try container.decode(String.self, forKey: .type)
            
            switch type {
            case "createRoom":
                let grade = try container.decode(String.self, forKey: .grade)
                let topic = try container.decode(String.self, forKey: .topic)
                let game = try container.decode(String.self, forKey: .game)
                let teacherId = try container.decode(String.self, forKey: .teacherId)
                let teacherName = try container.decode(String.self, forKey: .teacherName)
                let gameId = try container.decode(String.self, forKey: .gameId)
                let gameUrl = try container.decodeIfPresent(String.self, forKey: .gameUrl)
                self = .createRoom(grade: grade, topic: topic, game: game, teacherId: teacherId, teacherName: teacherName, gameId: gameId, gameUrl: gameUrl)
                
            case "joinRoom":
                let pin = try container.decode(String.self, forKey: .pin)
                let studentName = try container.decode(String.self, forKey: .studentName)
                let studentId = try container.decode(String.self, forKey: .studentId)
                self = .joinRoom(pin: pin, studentName: studentName, studentId: studentId)
                
            case "leaveRoom":
                self = .leaveRoom
                
            case "startGame":
                self = .startGame
                
            case "endGame":
                self = .endGame
                
            case "roomCreated":
                let room = try container.decode(GameRoom.self, forKey: .room)
                self = .roomCreated(room: room)
                
            case "roomJoined":
                let room = try container.decode(GameRoom.self, forKey: .room)
                self = .roomJoined(room: room)
                
            case "playerJoined":
                let player = try container.decode(Player.self, forKey: .player)
                self = .playerJoined(player: player)
                
            case "playerLeft":
                let playerId = try container.decode(String.self, forKey: .playerId)
                self = .playerLeft(playerId: playerId)
                
            case "gameStarted":
                let data = try container.decodeIfPresent(GameStartedData.self, forKey: .data)
                self = .gameStarted(data: data)
                
            case "gameEnded":
                let data = try container.decodeIfPresent(GameEndedData.self, forKey: .data)
                self = .gameEnded(data: data)
                
            case "roomClosed":
                self = .roomClosed
                
            case "gameUrl":
                let url = try container.decode(String.self, forKey: .url)
                self = .gameUrl(url: url)
                
            case "ping":
                self = .ping
                
            case "pong":
                self = .pong
                
            default:
                throw DecodingError.dataCorruptedError(forKey: .type, in: container, debugDescription: "Unknown type: \(type)")
            }
        }
    }
    
    // Enhanced data structures for game events
    struct GameStartedData: Codable {
        let roomId: String?
        let game: GameData?
        let grade: String?
        let topic: String?
        let startedAt: String?
        let playerCount: Int?
        let players: [PlayerInfo]?
        let isGameStarted: Bool?
        let timestamp: String?
    }
    
    struct GameEndedData: Codable {
        let roomId: String?
        let duration: Int?
        let players: [Player]?
        let timestamp: String?
    }
    
    struct GameData: Codable {
        let id: String
        let name: String
        let url: String
    }
    
    // Server response types - Updated to match working system
    struct ServerResponse: Codable {
        let type: String
        let room: GameRoomInfo?
        let data: ServerResponseData?
        let timestamp: String?
    }
    
    // Error response structure for simple error messages with timestamp
    struct ErrorResponse: Codable {
        let type: String
        let data: String
        let timestamp: String
    }
    
    struct ServerResponseData: Codable {
        let player: PlayerInfo?
        let roomId: String?
        let game: WebSocketGameInfo?
        let grade: String?
        let topic: String?
        let startedAt: String?
        let playerCount: Int?
        let players: [PlayerInfo]?
        let isGameStarted: Bool?
    }
    
    struct GameRoomInfo: Codable {
        let id: String
        let pinCode: String
        let hostId: String
        let grade: String
        let topic: String
        let game: WebSocketGameInfo
        let status: String
        let players: [PlayerInfo]
        let playerCount: Int
        let createdAt: String
        let maxPlayers: Int
        let isGameStarted: Bool  // New flag from server
        
        enum CodingKeys: String, CodingKey {
            case id
            case pinCode
            case hostId
            case grade
            case topic
            case game
            case status
            case players
            case playerCount
            case createdAt
            case maxPlayers
            case isGameStarted
        }
    }
    
    struct WebSocketGameInfo: Codable {
        let id: String
        let name: String
        let url: String
    }
    
    struct PlayerInfo: Codable {
        let id: String
        let username: String
        let role: String
        let joinedAt: String?
    }
    
    // WebSocket delegate
    var delegate: GameServiceDelegate?
    
    private var isDisconnecting = false
    
    // MARK: - Game State Management
    enum GameState {
        case waiting
        case active
        case completed
        case cancelled
    }
    
    @Published var gameState: GameState = .waiting
    @Published var webViewURL: String?
    @Published var gameDuration: Int = 0
    
    private override init() {
        // Configure URLSession with custom configuration
        let config = URLSessionConfiguration.default
        config.waitsForConnectivity = true
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 300
        config.connectionProxyDictionary = nil // Disable any proxy settings
        config.httpShouldUsePipelining = false // Disable pipelining
        config.httpMaximumConnectionsPerHost = 1 // Limit connections
        config.requestCachePolicy = .reloadIgnoringLocalAndRemoteCacheData // Force reload from server
        config.urlCache = nil // Disable URL cache
        
        // Create a custom URLProtocol to handle the connection
        let operationQueue = OperationQueue()
        operationQueue.maxConcurrentOperationCount = 1
        self.session = URLSession(configuration: config, delegate: nil, delegateQueue: operationQueue)
        
        // Create network monitor
        self.monitor = NWPathMonitor()
        
        super.init()
        
        // Clear any existing URL cache
        URLCache.shared.removeAllCachedResponses()
        
        // Set up network monitoring after session is initialized
        self.monitor.pathUpdateHandler = { [weak self] path in
            let isAvailable = path.status == .satisfied
            self?.isNetworkAvailable = isAvailable
            print("Network status: \(path.status)")
            print("Network interfaces: \(path.availableInterfaces.map { $0.name })")
            print("Network is available: \(isAvailable)")
            
            // Print more detailed network information
            if path.status == .satisfied {
                print("Network is satisfied")
                print("Is WiFi: \(path.usesInterfaceType(.wifi))")
                print("Is Cellular: \(path.usesInterfaceType(.cellular))")
                print("Is Ethernet: \(path.usesInterfaceType(.wiredEthernet))")
                print("Is Expensive: \(path.isExpensive)")
                print("Is Constrained: \(path.isConstrained)")
            } else {
                print("Network is not satisfied")
                print("Reason: \(path.status)")
            }
        }
        self.monitor.start(queue: DispatchQueue.global())
    }
    
    deinit {
        monitor.cancel()
    }
    
    // Fetch all available grades
    func fetchGrades() async throws -> [Grade] {
        // Check if grades are already cached
        if isGradesCached() {
            print("GameService: Returning cached grades")
            return gradesCache
        }
        
        // Check network availability with a more robust check
        guard await checkNetworkAvailability() else {
            print("GameService: Network is not available for fetchGrades")
            throw URLError(.notConnectedToInternet)
        }
        
        let urlString = "\(baseURL)"
        print("GameService: Fetching grades from: \(urlString)")
        
        guard let url = URL(string: urlString) else {
            print("GameService: Invalid URL: \(urlString)")
            throw URLError(.badURL)
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.cachePolicy = .useProtocolCachePolicy
        request.timeoutInterval = 30
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        do {
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("GameService: Invalid response type")
                throw URLError(.badServerResponse)
            }
            
            print("GameService: Response status code: \(httpResponse.statusCode)")
            
            guard (200...299).contains(httpResponse.statusCode) else {
                print("GameService: Server error: \(httpResponse.statusCode)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("GameService: Server response: \(responseString)")
                }
                throw URLError(.badServerResponse)
            }
            
            // Print received data for debugging
            if let jsonString = String(data: data, encoding: .utf8) {
                print("GameService: Received data: \(jsonString)")
            }
            
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .useDefaultKeys
            
            // Handle the case where GET /api/game returns null (as per API documentation)
            // For grades, we should use predefined grades since the API returns null for all data
            if let jsonString = String(data: data, encoding: .utf8), jsonString.contains("null") {
                print("GameService: API returned null for all games, using predefined grades")
                let predefinedGrades: [Grade] = [.form1, .form2, .form3]
                gradesCache = predefinedGrades
                return predefinedGrades
            }
            
            // Try to decode if the API returns actual data
            let apiResponse = try decoder.decode(APIResponse<GradesData>.self, from: data)
            
            guard let gradesData = apiResponse.data else {
                print("GameService: No data in response, using predefined grades")
                let predefinedGrades: [Grade] = [.form1, .form2, .form3]
                gradesCache = predefinedGrades
                return predefinedGrades
            }
            
            print("GameService: Successfully decoded grades data with \(gradesData.grades.count) grades")
            
            // Convert grade strings to Grade enum values
            let grades = gradesData.grades.compactMap { gradeInfo -> Grade? in
                print("GameService: Processing grade: \(gradeInfo.grade)")
                switch gradeInfo.grade {
                case "S1": return .form1
                case "S2": return .form2
                case "S3": return .form3
                default:
                    print("GameService: Unknown grade: \(gradeInfo.grade)")
                    return nil
                }
            }
            
            print("GameService: Converted to \(grades.count) Grade enum values")
            
            // Cache the grades
            gradesCache = grades
            print("GameService: Cached \(grades.count) grades")
            
            return grades
            
        } catch {
            print("GameService: Network error in fetchGrades: \(error.localizedDescription)")
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("GameService: Missing key: \(key.stringValue), context: \(context.debugDescription)")
                case .typeMismatch(let type, let context):
                    print("GameService: Type mismatch: expected \(type), context: \(context.debugDescription)")
                case .valueNotFound(let type, let context):
                    print("GameService: Value not found: expected \(type), context: \(context.debugDescription)")
                case .dataCorrupted(let context):
                    print("GameService: Data corrupted: \(context.debugDescription)")
                @unknown default:
                    print("GameService: Unknown decoding error: \(decodingError)")
                }
            }
            throw error
        }
    }
    
    // Helper method to check network availability more robustly
    private func checkNetworkAvailability() async -> Bool {
        // First check the stored network status
        if isNetworkAvailable {
            return true
        }
        
        // If stored status is false, try a quick connectivity test
        print("GameService: Stored network status is false, performing connectivity test")
        
        // Try to make a simple HTTP request to check connectivity
        let testURL = "https://www.apple.com" // Use a reliable test URL
        guard let url = URL(string: testURL) else {
            return false
        }
        
        do {
            let (_, response) = try await session.data(from: url)
            if let httpResponse = response as? HTTPURLResponse {
                let isConnected = (200...299).contains(httpResponse.statusCode)
                print("GameService: Connectivity test result: \(isConnected)")
                return isConnected
            }
        } catch {
            print("GameService: Connectivity test failed: \(error.localizedDescription)")
        }
        
        return false
    }
    
    // Fetch topics for a specific grade
    func fetchTopics(for grade: Grade) async throws -> [Topic] {
        print("GameService: fetchTopics called for grade: \(grade.rawValue)")
        
        // Check if topics are already cached for this grade
        if isTopicsCached(for: grade) {
            print("GameService: Returning cached topics for grade: \(grade.rawValue)")
            return topicsCache[cacheKey(grade: grade)] ?? []
        }
        
        // Check network availability with a more robust check
        guard await checkNetworkAvailability() else {
            print("GameService: Network is not available for fetchTopics")
            throw URLError(.notConnectedToInternet)
        }
        
        let urlString = "\(baseURL)"
        print("GameService: Fetching topics from: \(urlString)")
        
        guard let url = URL(string: urlString) else {
            print("GameService: Invalid URL: \(urlString)")
            throw URLError(.badURL)
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.cachePolicy = .useProtocolCachePolicy
        request.timeoutInterval = 30
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        // Add grade as a query parameter
        var components = URLComponents(url: url, resolvingAgainstBaseURL: true)!
        components.queryItems = [URLQueryItem(name: "grade", value: grade.rawValue)]
        request.url = components.url
        
        print("GameService: Final request URL: \(request.url?.absoluteString ?? "nil")")
        
        do {
            print("GameService: Making network request...")
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("GameService: Invalid response type")
                throw URLError(.badServerResponse)
            }
            
            print("GameService: Response status code: \(httpResponse.statusCode)")
            
            guard (200...299).contains(httpResponse.statusCode) else {
                print("GameService: Server error: \(httpResponse.statusCode)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("GameService: Server response: \(responseString)")
                }
                throw URLError(.badServerResponse)
            }
            
            // Print received data for debugging
            if let jsonString = String(data: data, encoding: .utf8) {
                print("GameService: Received data: \(jsonString)")
            }
            
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .useDefaultKeys
            
            // Decode the response where data is a direct array of topic strings
            let apiResponse = try decoder.decode(APIResponse<[String]>.self, from: data)
            
            guard let topicStrings = apiResponse.data else {
                print("GameService: No topics data in response")
                return []
            }
            
            print("GameService: Successfully decoded \(topicStrings.count) topic strings")
            
            // Convert topic strings to Topic objects
            let topics = topicStrings.map { topicName in
                Topic(
                    id: topicName,  // Using topic name as ID
                    name: topicName,
                    description: "Games for \(topicName)",
                    grade: grade,
                    games: []  // Games will be fetched separately when topic is selected
                )
            }
            
            print("GameService: Created \(topics.count) topic objects for grade \(grade.rawValue)")
            
            // Cache the topics
            topicsCache[cacheKey(grade: grade)] = topics
            print("GameService: Cached \(topics.count) topics for grade: \(grade.rawValue)")
            
            return topics
            
        } catch {
            print("GameService: Network error in fetchTopics: \(error.localizedDescription)")
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("GameService: Missing key: \(key.stringValue), context: \(context.debugDescription)")
                case .typeMismatch(let type, let context):
                    print("GameService: Type mismatch: expected \(type), context: \(context.debugDescription)")
                case .valueNotFound(let type, let context):
                    print("GameService: Value not found: expected \(type), context: \(context.debugDescription)")
                case .dataCorrupted(let context):
                    print("GameService: Data corrupted: \(context.debugDescription)")
                @unknown default:
                    print("GameService: Unknown decoding error: \(decodingError)")
                }
            }
            throw error
        }
    }
    
    // Fetch games for a specific grade and topic
    func fetchGames(for grade: Grade, topic: Topic) async throws -> [Game] {
        print("GameService: fetchGames called for grade: \(grade.rawValue), topic: \(topic.name)")
        
        // Check if games are already cached for this grade and topic
        if isGamesCached(for: grade, topic: topic) {
            print("GameService: Returning cached games for grade: \(grade.rawValue), topic: \(topic.name)")
            return gamesCache[cacheKey(grade: grade, topic: topic)] ?? []
        }
        
        // If not cached, fetch from network
        print("GameService: Games not cached, fetching from network...")
        
        // Check network availability with a more robust check
        guard await checkNetworkAvailability() else {
            print("GameService: Network is not available for fetchGames")
            throw URLError(.notConnectedToInternet)
        }
        
        let urlString = "\(baseURL)"
        print("GameService: Fetching games from: \(urlString)")
        
        guard let url = URL(string: urlString) else {
            print("GameService: Invalid URL: \(urlString)")
            throw URLError(.badURL)
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.cachePolicy = .useProtocolCachePolicy
        request.timeoutInterval = 30
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        // Add grade and topic as query parameters (topic first, then grade as per API example)
        var components = URLComponents(url: url, resolvingAgainstBaseURL: true)!
        components.queryItems = [
            URLQueryItem(name: "topic", value: topic.name),
            URLQueryItem(name: "grade", value: grade.rawValue)
        ]
        request.url = components.url
        
        print("GameService: Final request URL: \(request.url?.absoluteString ?? "nil")")
        
        do {
            print("GameService: Making network request...")
            let (data, response) = try await session.data(for: request)
            
            guard let httpResponse = response as? HTTPURLResponse else {
                print("GameService: Invalid response type")
                throw URLError(.badServerResponse)
            }
            
            print("GameService: Response status code: \(httpResponse.statusCode)")
            
            guard (200...299).contains(httpResponse.statusCode) else {
                print("GameService: Server error: \(httpResponse.statusCode)")
                if let responseString = String(data: data, encoding: .utf8) {
                    print("GameService: Server response: \(responseString)")
                }
                throw URLError(.badServerResponse)
            }
            
            // Print received data for debugging
            if let jsonString = String(data: data, encoding: .utf8) {
                print("GameService: Received data: \(jsonString)")
            }
            
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .useDefaultKeys
            
            // Decode the response where data is an array of game responses
            let apiResponse = try decoder.decode(APIResponse<[GameResponse]>.self, from: data)
            
            guard let gameResponses = apiResponse.data else {
                print("GameService: No games data in response")
                return []
            }
            
            print("GameService: Successfully decoded \(gameResponses.count) game responses")
            
            // Convert game responses to Game objects
            let games = gameResponses.flatMap { gameResponse in
                gameResponse.games.map { gameInfo in
                    // Determine game type and handle URL accordingly
                    let gameType: GameType
                    let finalURL: String
                    
                    if gameInfo.url.hasPrefix("funma-game://") {
                        gameType = .native
                        finalURL = gameInfo.url // Keep native URL as is
                    } else {
                        // Fix: Match the actual enum rawValues with fallback for case variations
                        gameType = GameType(rawValue: gameInfo.gameType) ?? {
                            // Fallback: try case-insensitive matching
                            switch gameInfo.gameType.lowercased() {
                            case "scratch":
                                return .scratch
                            case "geogebra":
                                return .geogebra
                            case "quiz":
                                return .quiz
                            case "native":
                                return .native
                            default:
                                print("GameService: Unknown game type '\(gameInfo.gameType)', defaulting to .scratch")
                                return .scratch
                            }
                        }()
                        finalURL = self.convertToScratchURL(gameInfo.url, gameId: gameInfo.gameId)
                        
                        // Debug print to help diagnose game type issues
                        print("GameService: Game '\(gameInfo.gameName)' - DB gameType: '\(gameInfo.gameType)' → Enum gameType: \(gameType)")
                    }
                    
                    return Game(
                        id: gameInfo.gameId,
                        name: gameInfo.gameName,
                        type: gameType,
                        url: finalURL,
                        description: gameInfo.description,
                        grade: grade
                    )
                }
            }
            
            print("GameService: Created \(games.count) games for grade \(grade.rawValue), topic \(topic.name)")
            
            // Cache the games
            gamesCache[cacheKey(grade: grade, topic: topic)] = games
            print("GameService: Cached \(games.count) games for grade: \(grade.rawValue), topic: \(topic.name)")
            
            return games
            
        } catch {
            print("GameService: Network error in fetchGames: \(error.localizedDescription)")
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .keyNotFound(let key, let context):
                    print("GameService: Missing key: \(key.stringValue), context: \(context.debugDescription)")
                case .typeMismatch(let type, let context):
                    print("GameService: Type mismatch: expected \(type), context: \(context.debugDescription)")
                case .valueNotFound(let type, let context):
                    print("GameService: Value not found: expected \(type), context: \(context.debugDescription)")
                case .dataCorrupted(let context):
                    print("GameService: Data corrupted: \(context.debugDescription)")
                @unknown default:
                    print("GameService: Unknown decoding error: \(decodingError)")
                }
            }
            throw error
        }
    }
    
    // Cache management methods
    func clearAllCaches() {
        print("GameService: Clearing all caches")
        gamesCache.removeAll()
        topicsCache.removeAll()
        gradesCache.removeAll()
        availableGames.removeAll()
        URLCache.shared.removeAllCachedResponses()
        session.configuration.urlCache?.removeAllCachedResponses()
    }
    
    func clearGamesCache() {
        print("GameService: Clearing games cache")
        gamesCache.removeAll()
        availableGames.removeAll()
    }
    
    func clearTopicsCache() {
        print("GameService: Clearing topics cache")
        topicsCache.removeAll()
    }
    
    func clearGradesCache() {
        print("GameService: Clearing grades cache")
        gradesCache.removeAll()
    }
    
    // Cache key helpers
    private func cacheKey(grade: Grade, topic: Topic) -> String {
        return "\(grade.rawValue)_\(topic.id)"
    }
    
    private func cacheKey(grade: Grade) -> String {
        return grade.rawValue
    }
    
    // Check if data is cached
    private func isGradesCached() -> Bool {
        return !gradesCache.isEmpty
    }
    
    private func isTopicsCached(for grade: Grade) -> Bool {
        return topicsCache[cacheKey(grade: grade)] != nil
    }
    
    private func isGamesCached(for grade: Grade, topic: Topic) -> Bool {
        return gamesCache[cacheKey(grade: grade, topic: topic)] != nil
    }
    
    // WebSocket methods
    func connectToGameRoom(gameRoomId: String) {
        print("GameService: Attempting to connect to WebSocket")
        print("GameService: WebSocket URL: \(wsURL)")
        
        // Reset connection state
        resetConnectionState()
        
        guard let url = URL(string: wsURL) else { 
            print("GameService: Failed to create WebSocket URL from: \(wsURL)")
            return 
        }
        
        print("GameService: Connecting to WebSocket URL: \(url)")
        
        // Check if we're already connected
        if webSocketTask != nil {
            print("GameService: Already connected, disconnecting first")
            disconnectFromGameRoom()
        }
        
        isConnecting = true
        connectionAttempts += 1
        
        webSocketTask = session.webSocketTask(with: url)
        webSocketTask?.resume()
        
        print("GameService: WebSocket task resumed, starting to receive messages")
        
        // Set connected state immediately when WebSocket task is created
        isConnected = true
        isConnecting = false
        
        // Start receiving messages
        receiveMessage()
        
        // Send initial ping to verify connection after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            self?.sendInitialPing()
        }
        
        // Set a timeout for connection
        startConnectionTimer()
        
        // Send any queued messages immediately
        sendQueuedMessages()
    }
    
    func disconnectFromGameRoom() {
        print("GameService: Disconnecting from WebSocket")
        
        // Set disconnecting flag to prevent new messages
        isDisconnecting = true
        
        // Cancel connection timer
        connectionTimer?.invalidate()
        connectionTimer = nil
        
        // Clear any queued messages since we're disconnecting
        messageQueue.removeAll()
        
        // Cancel WebSocket task
        webSocketTask?.cancel(with: .normalClosure, reason: nil)
        webSocketTask = nil
        
        // Reset connection state
        resetConnectionState()
        
        print("GameService: WebSocket disconnected and state reset")
    }
    
    private func resetConnectionState() {
        isConnected = false
        isConnecting = false
        isDisconnecting = false
        connectionAttempts = 0
        connectionTimer?.invalidate()
        connectionTimer = nil
        messageQueue.removeAll() // Clear any queued messages
    }
    
    private func startConnectionTimer() {
        connectionTimer?.invalidate()
        connectionTimer = Timer.scheduledTimer(withTimeInterval: connectionTimeout, repeats: false) { [weak self] _ in
            guard let self = self else { return }
            
            if !self.isConnected {
                print("GameService: WebSocket connection timeout after \(self.connectionTimeout) seconds")
                self.handleConnectionFailure()
            }
        }
    }
    
    private func handleConnectionFailure() {
        print("GameService: Handling connection failure")
        
        if connectionAttempts < maxConnectionAttempts {
            print("GameService: Attempting reconnection (attempt \(connectionAttempts + 1)/\(maxConnectionAttempts))")
            
            // Retry connection after a short delay
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
                self?.connectToGameRoom(gameRoomId: "retry")
            }
        } else {
            print("GameService: Max connection attempts reached")
            self.delegate?.gameService(self, didReceiveError: NSError(domain: "GameService", code: -1, userInfo: [NSLocalizedDescriptionKey: "WebSocket connection failed after \(maxConnectionAttempts) attempts"]))
        }
    }
    
    private func sendInitialPing() {
        guard let webSocketTask = webSocketTask else { return }
        
        print("GameService: Sending initial ping to verify connection")
        
        webSocketTask.sendPing { [weak self] error in
            if let error = error {
                print("GameService: Initial ping failed: \(error)")
                // Don't reset connection state immediately, let the timeout handle it
                self?.handleConnectionFailure()
            } else {
                print("GameService: Initial ping successful, connection verified")
                
                // Cancel connection timer since we're now verified
                self?.connectionTimer?.invalidate()
                self?.connectionTimer = nil
                
                // Start periodic ping schedule
                self?.schedulePing()
            }
        }
    }
    
    private func sendQueuedMessages() {
        guard !messageQueue.isEmpty else { return }
        
        print("GameService: Sending \(messageQueue.count) queued messages")
        
        // Send all queued messages
        for message in messageQueue {
            sendMessageDirectly(message)
        }
        
        // Clear the queue
        messageQueue.removeAll()
    }
    
    private func sendMessageDirectly(_ message: WebSocketMessage) {
        guard let webSocketTask = webSocketTask, isConnected else {
            print("GameService: Cannot send message directly - not connected")
            return
        }
        
        guard let data = try? JSONEncoder().encode(message) else {
            print("GameService: Failed to encode message to JSON")
            return
        }
        
        let jsonString = String(data: data, encoding: .utf8) ?? "failed to convert"
        print("GameService: Sending message to WebSocket server:")
        print("GameService: JSON: \(jsonString)")
        
        webSocketTask.send(.data(data)) { error in
            if let error = error {
                print("GameService: WebSocket send error: \(error)")
                self.delegate?.gameService(self, didReceiveError: error)
            } else {
                print("GameService: Message sent successfully to WebSocket server")
            }
        }
    }
    
    private func schedulePing() {
        guard let webSocketTask = webSocketTask else { return }
        
        webSocketTask.sendPing { [weak self] error in
            if let error = error {
                print("WebSocket ping failed: \(error)")
                self?.isConnected = false
                self?.handleConnectionFailure()
            } else {
                self?.isConnected = true
                // Schedule next ping
                DispatchQueue.main.asyncAfter(deadline: .now() + 30) { [weak self] in
                    self?.schedulePing()
                }
            }
        }
    }
    
    private func receiveMessage() {
        guard let webSocketTask = webSocketTask else {
            print("GameService: WebSocket task is nil, cannot receive messages")
            return
        }
        
        webSocketTask.receive { [weak self] result in
            guard let self = self else { return }
            
            switch result {
            case .success(let message):
                switch message {
                case .string(let text):
                    print("GameService: Received string message: \(text)")
                    self.handleMessage(text)
                case .data(let data):
                    if let text = String(data: data, encoding: .utf8) {
                        print("GameService: Received data message: \(text)")
                        self.handleMessage(text)
                    } else {
                        print("GameService: Received binary data that couldn't be converted to string")
                    }
                @unknown default:
                    print("GameService: Received unknown message type")
                }
                
                // Continue receiving messages
                self.receiveMessage()
                
            case .failure(let error):
                print("GameService: WebSocket receive error: \(error)")
                
                // Check if this is a disconnection error
                if let posixError = error as? POSIXError, posixError.code == .ENOTCONN {
                    print("GameService: WebSocket disconnected (Socket is not connected)")
                    DispatchQueue.main.async {
                        self.resetConnectionState()
                    }
                } else {
                    // For other errors, try to reconnect
                    DispatchQueue.main.async {
                        self.delegate?.gameService(self, didReceiveError: error)
                    }
                }
            }
        }
    }
    
    private func handleMessage(_ text: String) {
        print("GameService: Handling message: \(text)")
        
        // First try to decode as the new WebSocketMessage format
        if let data = text.data(using: .utf8),
           let message = try? JSONDecoder().decode(WebSocketMessage.self, from: data) {
            print("GameService: Decoded as WebSocketMessage: \(message)")
            handleWebSocketMessage(message)
            return
        }
        
        // If that fails, try to decode as the old ServerResponse format
        if let data = text.data(using: .utf8),
           let serverResponse = try? JSONDecoder().decode(ServerResponse.self, from: data) {
            print("GameService: Decoded as ServerResponse: \(serverResponse)")
            handleServerResponse(serverResponse)
            return
        }
        
        // If that fails, try to decode as a simple error message with timestamp
        if let data = text.data(using: .utf8) {
            do {
                let errorResponse = try JSONDecoder().decode(ErrorResponse.self, from: data)
                print("GameService: Decoded as ErrorResponse: \(errorResponse)")
                handleErrorResponse(errorResponse)
                return
            } catch {
                print("GameService: Failed to decode as ErrorResponse: \(error)")
            }
        }
        
        print("GameService: Failed to decode message in any format: \(text)")
    }
    
    private func handleWebSocketMessage(_ message: WebSocketMessage) {
        switch message {
        case .roomCreated(let room):
            print("GameService: Room created: \(room)")
            DispatchQueue.main.async {
                self.delegate?.gameService(self, createdRoom: room)
            }
            
        case .roomJoined(let room):
            print("GameService: Room joined: \(room)")
            DispatchQueue.main.async {
                self.delegate?.gameService(self, joinedRoom: room)
            }
            
        case .playerJoined(let player):
            print("GameService: Player joined: \(player)")
            DispatchQueue.main.async {
                self.delegate?.gameService(self, playerJoined: player)
            }
            
        case .playerLeft(let playerId):
            print("GameService: Player left: \(playerId)")
            DispatchQueue.main.async {
                self.delegate?.gameService(self, playerLeft: playerId)
            }
            
        case .gameStarted(let data):
            print("GameService: Processing gameStarted message (WebSocketMessage case)")
            
            // Update game state
            DispatchQueue.main.async {
                self.gameState = .active
            }
            
            if let data = data {
                print("GameService: Game started data available: \(data)")
                
                // Extract game URL from the GameStartedData structure
                var gameURL: String?
                
                if let game = data.game {
                    gameURL = game.url
                    print("GameService: Found game URL in game object: \(gameURL ?? "nil")")
                } else {
                    print("GameService: No game object found in gameStarted data")
                }
                
                // Process and set the URL if found
                if let foundURL = gameURL, !foundURL.isEmpty {
                    // Determine game type based on URL format
                    let gameType: GameType
                    if foundURL.hasPrefix("funma-game://") {
                        gameType = .native
                    } else if foundURL.contains("scratch.mit.edu") {
                        gameType = .scratch
                    } else if foundURL.contains("geogebra.org") {
                        gameType = .geogebra
                    } else {
                        gameType = .quiz // Default fallback
                    }
                    
                    let processedUrl = self.handleGameUrl(foundURL, gameType: gameType)
                    print("GameService: Processed URL: \(processedUrl)")
                    
                    DispatchQueue.main.async {
                        self.webViewURL = processedUrl
                        self.delegate?.gameService(self, receivedGameUrl: processedUrl)
                    }
                } else {
                    print("GameService: No valid game URL available in gameStarted message")
                }
            } else {
                print("GameService: No data available in gameStarted message")
            }
            
            DispatchQueue.main.async {
                self.delegate?.gameServiceDidStartGame(self)
            }
            
        case .gameEnded(let data):
            print("GameService: Game ended")
            if let duration = data?.duration {
                print("GameService: Game duration: \(duration) seconds")
                DispatchQueue.main.async {
                    self.gameDuration = duration
                }
            }
            
            // Update game state and clear WebView URL
            DispatchQueue.main.async {
                self.gameState = .completed
                self.webViewURL = nil
            }
            
            DispatchQueue.main.async {
                self.delegate?.gameServiceDidEndGame(self)
            }
            
        case .roomClosed:
            print("GameService: Room closed by host")
            DispatchQueue.main.async {
                self.handleRoomClosed()
            }
            
        case .gameUrl(let url):
            print("GameService: Received game URL: \(url)")
            DispatchQueue.main.async {
                self.webViewURL = url
                self.delegate?.gameService(self, receivedGameUrl: url)
            }
            
        case .ping:
            print("GameService: Received ping, sending pong")
            sendMessage(.pong)
            
        case .pong:
            print("GameService: Received pong")
            
        case .leaveRoom, .createRoom, .joinRoom, .startGame, .endGame:
            print("GameService: Received command message (should not happen): \(message)")
        }
    }
    
    private func handleServerResponse(_ serverResponse: ServerResponse) {
        print("GameService: Processing server response type: \(serverResponse.type)")
        
        switch serverResponse.type {
        case "roomCreated":
            print("GameService: Processing roomCreated message")
            if let roomData = serverResponse.room {
                print("GameService: Room data received - hostId: \(roomData.hostId)")
                
                // Convert server room data to app's GameRoom format
                let grade = Grade(rawValue: roomData.grade) ?? .form1
                let topic = Topic(
                    id: roomData.topic,
                    name: roomData.topic,
                    description: "Topic for \(roomData.topic)",
                    grade: grade,
                    games: []
                )
                
                let game = Game(
                    id: roomData.game.id,
                    name: roomData.game.name,
                    type: .quiz, // Default type
                    url: roomData.game.url,
                    description: "Game for \(roomData.game.name)",
                    grade: grade
                )
                
                let gameRoom = GameRoom(
                    id: roomData.id,
                    pinCode: roomData.pinCode,
                    hostId: roomData.hostId,
                    hostName: roomData.players.first(where: { $0.role == "host" })?.username ?? "Host",
                    grade: grade,
                    topic: topic,
                    game: game,
                    createdAt: Date(),
                    status: GameRoomStatus(rawValue: roomData.status) ?? .waiting,
                    players: roomData.players.map { playerInfo in
                        Player(
                            id: playerInfo.id,
                            username: playerInfo.username,
                            role: PlayerRole(rawValue: playerInfo.role) ?? .player,
                            joinedAt: Date()
                        )
                    },
                    isGameStarted: roomData.isGameStarted,
                    chargedStudentIds: Set<String>()  // Initialize empty charged students set
                )
                
                print("GameService: Created GameRoom with hostId: \(gameRoom.hostId)")
                DispatchQueue.main.async {
                    self.delegate?.gameService(self, createdRoom: gameRoom)
                }
            } else {
                print("GameService: No room data in roomCreated message")
            }
            
        case "roomJoined":
            print("GameService: Processing roomJoined message")
            if let roomData = serverResponse.room {
                print("GameService: Room data received for student join - roomId: \(roomData.id)")
                print("GameService: Game started flag: \(roomData.isGameStarted)")
                
                // Convert server room data to app's GameRoom format
                let grade = Grade(rawValue: roomData.grade) ?? .form1
                let topic = Topic(
                    id: roomData.topic,
                    name: roomData.topic,
                    description: "Topic for \(roomData.topic)",
                    grade: grade,
                    games: []
                )
                
                let game = Game(
                    id: roomData.game.id,
                    name: roomData.game.name,
                    type: .quiz,
                    url: roomData.game.url,
                    description: "Game for \(roomData.game.name)",
                    grade: grade
                )
                
                let gameRoom = GameRoom(
                    id: roomData.id,
                    pinCode: roomData.pinCode,
                    hostId: roomData.hostId,
                    hostName: roomData.players.first(where: { $0.role == "host" })?.username ?? "Host",
                    grade: grade,
                    topic: topic,
                    game: game,
                    createdAt: Date(),
                    status: GameRoomStatus(rawValue: roomData.status) ?? .waiting,
                    players: roomData.players.map { playerInfo in
                        Player(
                            id: playerInfo.id,
                            username: playerInfo.username,
                            role: PlayerRole(rawValue: playerInfo.role) ?? .player,
                            joinedAt: Date()
                        )
                    },
                    isGameStarted: roomData.isGameStarted,
                    chargedStudentIds: Set<String>()  // Initialize empty charged students set
                )
                
                DispatchQueue.main.async {
                    self.delegate?.gameService(self, joinedRoom: gameRoom)
                    
                    // If game has already started, immediately show game interface
                    if roomData.isGameStarted {
                        print("GameService: Game already started, updating game state to active")
                        self.gameState = .active
                        
                        // Set the game URL for immediate access
                        if !roomData.game.url.isEmpty {
                            // Determine game type based on URL format
                            let gameType: GameType
                            if roomData.game.url.hasPrefix("funma-game://") {
                                gameType = .native
                            } else if roomData.game.url.contains("scratch.mit.edu") {
                                gameType = .scratch
                            } else if roomData.game.url.contains("geogebra.org") {
                                gameType = .geogebra
                            } else {
                                gameType = .quiz // Default fallback
                            }
                            
                            let processedUrl = self.handleGameUrl(roomData.game.url, gameType: gameType)
                            print("GameService: Setting webViewURL for active game: \(processedUrl)")
                            self.webViewURL = processedUrl
                            self.delegate?.gameService(self, receivedGameUrl: processedUrl)
                        } else {
                            print("GameService: No game URL available in active game")
                        }
                        
                        // Notify delegate that game has started
                        self.delegate?.gameServiceDidStartGame(self)
                    }
                }
            } else {
                print("GameService: No room data in roomJoined message")
            }
            
        case "playerJoined":
            if let playerData = serverResponse.data?.player {
                let player = Player(
                    id: playerData.id,
                    username: playerData.username,
                    role: PlayerRole(rawValue: playerData.role) ?? .player,
                    joinedAt: Date()
                )
                DispatchQueue.main.async {
                    self.delegate?.gameService(self, playerJoined: player)
                }
            }
            
        case "playerLeft":
            if let playerData = serverResponse.data?.player {
                DispatchQueue.main.async {
                    self.delegate?.gameService(self, playerLeft: playerData.id)
                }
            }
            
        case "gameStarted":
            print("GameService: Processing gameStarted message")
            
            // Update game state
            DispatchQueue.main.async {
                self.gameState = .active
            }
            
            if let data = serverResponse.data {
                print("GameService: Game started data available: \(data)")
                
                // Extract game URL from the properly structured data
                var gameURL: String?
                
                if let game = data.game {
                    gameURL = game.url
                    print("GameService: Found game URL in game object: \(gameURL ?? "nil")")
                } else {
                    print("GameService: No game object found in gameStarted data")
                }
                
                // Process and set the URL if found
                if let foundURL = gameURL, !foundURL.isEmpty {
                    // Determine game type based on URL format
                    let gameType: GameType
                    if foundURL.hasPrefix("funma-game://") {
                        gameType = .native
                    } else if foundURL.contains("scratch.mit.edu") {
                        gameType = .scratch
                    } else if foundURL.contains("geogebra.org") {
                        gameType = .geogebra
                    } else {
                        gameType = .quiz // Default fallback
                    }
                    
                    let processedUrl = self.handleGameUrl(foundURL, gameType: gameType)
                    print("GameService: Processed URL: \(processedUrl)")
                    
                    DispatchQueue.main.async {
                        self.webViewURL = processedUrl
                        self.delegate?.gameService(self, receivedGameUrl: processedUrl)
                    }
                } else {
                    print("GameService: No valid game URL available in gameStarted message")
                }
            } else {
                print("GameService: No data available in gameStarted message")
            }
            
            DispatchQueue.main.async {
                self.delegate?.gameServiceDidStartGame(self)
            }
            
        case "gameEnded":
            print("GameService: Processing gameEnded message")
            
            if let data = serverResponse.data {
                print("GameService: Game ended data available")
                if let gameData = data as? [String: Any],
                   let duration = gameData["duration"] as? Int {
                    print("GameService: Game duration: \(duration) seconds")
                    DispatchQueue.main.async {
                        self.gameDuration = duration
                    }
                }
            }
            
            // Update game state and clear WebView URL
            DispatchQueue.main.async {
                self.gameState = .completed
                self.webViewURL = nil
            }
            
            DispatchQueue.main.async {
                self.delegate?.gameServiceDidEndGame(self)
            }
            
        case "roomClosed":
            print("GameService: Processing roomClosed message")
            DispatchQueue.main.async {
                self.handleRoomClosed()
            }

        case "error":
            if let errorData = serverResponse.data {
                let errorMessage: String
                
                // Handle specific error messages from WebSocket guide
                if let errorString = errorData as? String {
                    errorMessage = errorString
                } else {
                    errorMessage = "Server error: \(errorData)"
                }
                
                print("GameService: Received error message: \(errorMessage)")
                
                // Create appropriate error based on message content
                let error: Error
                if errorMessage.contains("Invalid PIN code") || errorMessage.contains("Room not found") {
                    error = NSError(domain: "GameService", code: 404, userInfo: [NSLocalizedDescriptionKey: "Invalid PIN code. Please check the 6-digit code from your teacher."])
                } else if errorMessage.contains("Student name is required") {
                    error = NSError(domain: "GameService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Please enter your name to join the game."])
                } else if errorMessage.contains("Room is full") {
                    error = NSError(domain: "GameService", code: 429, userInfo: [NSLocalizedDescriptionKey: "This game room is full. Maximum 50 players allowed."])
                } else if errorMessage.contains("not accepting players") {
                    error = NSError(domain: "GameService", code: 403, userInfo: [NSLocalizedDescriptionKey: "The game has already started. You cannot join now."])
                } else if errorMessage.contains("Game can only be started when status is 'waiting'") {
                    error = NSError(domain: "GameService", code: 409, userInfo: [NSLocalizedDescriptionKey: "Game has already been started."])
                } else if errorMessage.contains("This game room is no longer available") {
                    error = NSError(domain: "GameService", code: 410, userInfo: [NSLocalizedDescriptionKey: "This game room is no longer available."])
                } else {
                    error = NSError(domain: "GameService", code: -1, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                }
                
                DispatchQueue.main.async {
                    self.delegate?.gameService(self, didReceiveError: error)
                }
            }
            
        default:
            print("GameService: Unknown server response type: \(serverResponse.type)")
        }
    }
    
    private func handleErrorResponse(_ errorResponse: ErrorResponse) {
        print("GameService: Processing error response: \(errorResponse.data)")
        
        // Create appropriate error based on message content
        let error: Error
        if errorResponse.data.contains("Invalid PIN code") || errorResponse.data.contains("Room not found") {
            error = NSError(domain: "GameService", code: 404, userInfo: [NSLocalizedDescriptionKey: "Invalid PIN code. Please check the 6-digit code from your teacher."])
        } else if errorResponse.data.contains("Student name is required") {
            error = NSError(domain: "GameService", code: 400, userInfo: [NSLocalizedDescriptionKey: "Please enter your name to join the game."])
        } else if errorResponse.data.contains("Room is full") {
            error = NSError(domain: "GameService", code: 429, userInfo: [NSLocalizedDescriptionKey: "This game room is full. Maximum 50 players allowed."])
        } else if errorResponse.data.contains("not accepting players") {
            error = NSError(domain: "GameService", code: 403, userInfo: [NSLocalizedDescriptionKey: "The game has already started. You cannot join now."])
        } else if errorResponse.data.contains("This game room is no longer available") {
            error = NSError(domain: "GameService", code: 410, userInfo: [NSLocalizedDescriptionKey: "This game room is no longer available."])
        } else {
            error = NSError(domain: "GameService", code: -1, userInfo: [NSLocalizedDescriptionKey: errorResponse.data])
        }
        
        DispatchQueue.main.async {
            self.delegate?.gameService(self, didReceiveError: error)
        }
    }
    
    func sendMessage(_ message: WebSocketMessage) {
        print("GameService: Attempting to send message: \(message)")
        
        // Check if we're in the process of disconnecting
        guard !isDisconnecting && webSocketTask != nil else {
            print("GameService: WebSocket is not available (disconnecting or disconnected), cannot send message")
            self.delegate?.gameService(self, didReceiveError: NSError(domain: "GameService", code: -1, userInfo: [NSLocalizedDescriptionKey: "WebSocket is not available"]))
            return
        }
        
        // If we're still connecting, queue the message
        if isConnecting {
            print("GameService: WebSocket is still connecting, queuing message")
            messageQueue.append(message)
            return
        }
        
        // If we're connected, send immediately
        if isConnected {
            sendMessageDirectly(message)
            return
        }
        
        // If not connected, queue the message and try to connect
        print("GameService: WebSocket is not connected, queuing message and attempting to connect...")
        messageQueue.append(message)
        connectToGameRoom(gameRoomId: "reconnect")
    }
    
    // Test connection method
    func testConnection() {
        print("GameService: Testing WebSocket connection to: \(wsURL)")
        
        // Connect to WebSocket
        connectToGameRoom(gameRoomId: "test")
        
        // Send a test message after a short delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            self.sendMessage(.joinRoom(
                pin: "123456",
                studentName: "TestUser",
                studentId: "test123"
            ))
        }
    }
    
    // Method to check if WebSocket server is reachable
    func checkWebSocketServerReachability() async -> Bool {
        print("GameService: Checking WebSocket server reachability...")
        print("GameService: WebSocket URL: \(wsURL)")
        
        // For localhost WebSocket servers, try a different approach
        if wsURL.contains("localhost") {
            print("GameService: Checking localhost WebSocket server...")
            return await testLocalWebSocketConnection()
        }
        
        // For remote servers, try to establish a WebSocket connection directly
        // instead of using HTTP which might violate ATS policy
        print("GameService: Checking remote WebSocket server via direct connection...")
        return await testWebSocketConnection()
    }
    
    private func testLocalWebSocketConnection() async -> Bool {
        print("GameService: Testing local WebSocket connection...")
        return await testWebSocketConnection()
    }
    
    private func testWebSocketConnection() async -> Bool {
        print("GameService: Testing WebSocket connection...")
        
        // Create a temporary WebSocket task to test connection
        guard let url = URL(string: wsURL) else {
            print("GameService: Invalid WebSocket URL for testing")
            return false
        }
        
        let testTask = session.webSocketTask(with: url)
        testTask.resume()
        
        // Try to send a ping with a timeout
        let pingResult = await withTaskGroup(of: Bool.self) { group in
            group.addTask {
                // Timeout task - returns false after 5 seconds
                try? await Task.sleep(nanoseconds: 5_000_000_000) // 5 seconds
                return false
            }
            
            group.addTask {
                // Ping task
                await withCheckedContinuation { continuation in
                    testTask.sendPing { error in
                        continuation.resume(returning: error == nil)
                    }
                }
            }
            
            // Return the first result and cancel other tasks
            if let result = await group.next() {
                group.cancelAll()
                return result
            }
            return false
        }
        
        // Cancel the test task
        testTask.cancel(with: .normalClosure, reason: nil)
        
        if pingResult {
            print("GameService: WebSocket server is reachable")
        } else {
            print("GameService: WebSocket server is NOT reachable")
            if wsURL.contains("localhost") {
                print("GameService: Make sure your local WebSocket server is running on \(wsURL)")
                print("GameService: You can start a test WebSocket server using Python: python -m websockets.serve --port 8765")
            } else {
                print("GameService: Make sure the remote WebSocket server is running and accessible at \(wsURL)")
                print("GameService: Check network connectivity and server status")
            }
        }
        
        return pingResult
    }
    
    private func findGameByName(_ name: String, grade: Grade, topic: String) -> Game? {
        // Search through available games to find a matching game with URL
        return availableGames.first { game in
            game.name.lowercased() == name.lowercased() && 
            game.grade == grade
        }
    }
    
    // Helper function to convert MongoDB game URL to proper Scratch URL format
    private func convertToScratchURL(_ originalURL: String, gameId: String) -> String {
        // If the URL is already a proper Scratch URL, return it as is
        if originalURL.contains("scratch.mit.edu/projects/") {
            return originalURL
        }
        
        // If the URL contains a project ID, construct the proper Scratch URL
        if let projectId = extractProjectId(from: originalURL) {
            return "https://scratch.mit.edu/projects/\(projectId)"
        }
        
        // If we have a gameId that looks like a project ID, use it
        if gameId.count >= 9 && gameId.allSatisfy({ $0.isNumber }) {
            return "https://scratch.mit.edu/projects/\(gameId)"
        }
        
        // Fallback: return the original URL
        print("GameService: Could not convert URL to Scratch format, using original: \(originalURL)")
        return originalURL
    }
    
    // Helper function to extract project ID from various URL formats
    private func extractProjectId(from url: String) -> String? {
        // Handle various URL formats that might contain project IDs
        let patterns = [
            #"scratch\.mit\.edu/projects/(\d+)"#,
            #"projects/(\d+)"#,
            #"(\d{9,})"#,  // 9+ digit numbers (Scratch project IDs are typically 9+ digits)
            #"project_id=(\d+)"#,
            #"id=(\d+)"#
        ]
        
        for pattern in patterns {
            if let regex = try? NSRegularExpression(pattern: pattern, options: []),
               let match = regex.firstMatch(in: url, options: [], range: NSRange(url.startIndex..., in: url)) {
                let range = Range(match.range(at: 1), in: url)!
                return String(url[range])
            }
        }
        
        return nil
    }
    
    // Test method to verify URL conversion
    func testURLConversion() {
        let testCases = [
            ("https://scratch.mit.edu/projects/1181745148", "1181745148"),
            ("1181745148", "1181745148"),
            ("https://scratch.mit.edu/projects/1181745148/embed", "1181745148"),
            ("https://some-other-url.com/projects/1181745148", "1181745148"),
            ("invalid-url", "invalid-url")
        ]
        
        for (input, expectedId) in testCases {
            let result = convertToScratchURL(input, gameId: expectedId)
            print("GameService: URL conversion test - Input: \(input) -> Output: \(result)")
        }
    }
    
    // Public method to get cached games
    func getCachedGames(for grade: Grade, topic: Topic) -> [Game]? {
        return gamesCache[cacheKey(grade: grade, topic: topic)]
    }
    
    // Public method to get cached topics
    func getCachedTopics(for grade: Grade) -> [Topic]? {
        return topicsCache[cacheKey(grade: grade)]
    }
    
    // Public method to get cached grades
    func getCachedGrades() -> [Grade]? {
        return gradesCache.isEmpty ? nil : gradesCache
    }
    
    // Public method to check if WebSocket is connected
    func isWebSocketConnected() -> Bool {
        return isConnected && webSocketTask != nil
    }
    
    // Public method to get connection state
    func getConnectionState() -> (isConnected: Bool, isConnecting: Bool) {
        return (isConnected: isConnected, isConnecting: isConnecting)
    }
    
    func isWebSocketAvailable() -> Bool {
        return !isDisconnecting && !isConnecting && webSocketTask != nil && isConnected
    }
    
    func handleRoomClosed() {
        print("GameService: Handling room closed by server")
        
        // Reset game state
        DispatchQueue.main.async {
            self.gameState = .waiting
            self.webViewURL = nil
            self.gameDuration = 0
        }
        
        // Disconnect from WebSocket
        disconnectFromGameRoom()
        
        // Notify delegate on main thread
        DispatchQueue.main.async {
            self.delegate?.gameServiceDidLeaveRoom(self)
        }
    }
    
    func resetGameState() {
        print("GameService: Resetting game state")
        DispatchQueue.main.async {
            self.gameState = .waiting
            self.webViewURL = nil
            self.gameDuration = 0
        }
    }
    
    // MARK: - Game URL Handling
    
    /// Handle different game types and their URLs
    /// Server no longer processes URLs, so app must handle different game types
    func handleGameUrl(_ url: String, gameType: GameType) -> String {
        switch gameType {
        case .scratch:
            // Handle Scratch URLs (already in correct format)
            return url
        case .geogebra:
            // Handle GeoGebra URLs
            return url
        case .quiz:
            // Handle quiz URLs
            return url
        case .native:
            // Handle native game URLs (funma-game:// format)
            return url
        }
    }
    
    /// Check if a URL is a native game URL
    func isNativeGameUrl(_ url: String) -> Bool {
        return url.hasPrefix("funma-game://")
    }
    
    /// Extract the view name from a native game URL
    func extractNativeGameViewName(_ url: String) -> String? {
        guard isNativeGameUrl(url) else { return nil }
        let components = url.components(separatedBy: "funma-game://")
        guard components.count == 2 else { return nil }
        return components[1]
    }
    
    /// Validate if a URL is accessible, trying alternative formats if needed
    func validateGameUrl(_ url: String) async -> (isValid: Bool, workingUrl: String?) {
        print("GameService: Validating URL: \(url)")
        
        // For native game URLs, skip HTTP validation since they are built-in views
        if isNativeGameUrl(url) {
            print("GameService: Native game URL detected, skipping HTTP validation")
            return (true, url)
        }
        
        let alternativeUrls = tryAlternativeUrlFormats(url)
        
        for alternativeUrl in alternativeUrls {
            guard let urlObj = URL(string: alternativeUrl) else {
                print("GameService: Invalid URL format: \(alternativeUrl)")
                continue
            }
            
            do {
                let (_, response) = try await URLSession.shared.data(from: urlObj)
                if let httpResponse = response as? HTTPURLResponse {
                    let isValid = httpResponse.statusCode >= 200 && httpResponse.statusCode < 400
                    print("GameService: URL validation result for \(alternativeUrl) - Status: \(httpResponse.statusCode), Valid: \(isValid)")
                    if isValid {
                        return (true, alternativeUrl)
                    }
                }
            } catch {
                print("GameService: URL validation failed for \(alternativeUrl): \(error.localizedDescription)")
            }
        }
        
        print("GameService: No working URL found among alternatives")
        return (false, nil)
    }
    
    /// Try alternative URL formats if the original URL fails
    func tryAlternativeUrlFormats(_ originalUrl: String) -> [String] {
        var alternatives: [String] = []
        
        // Add the original URL
        alternatives.append(originalUrl)
        
        // Try adding https:// if it's missing
        if !originalUrl.hasPrefix("http://") && !originalUrl.hasPrefix("https://") {
            alternatives.append("https://\(originalUrl)")
            alternatives.append("http://\(originalUrl)")
        }
        
        // Try common game hosting platforms
        if originalUrl.contains("scratch.mit.edu") {
            alternatives.append(originalUrl.replacingOccurrences(of: "http://", with: "https://"))
        }
        
        if originalUrl.contains("geogebra.org") {
            alternatives.append(originalUrl.replacingOccurrences(of: "http://", with: "https://"))
        }
        
        print("GameService: Alternative URLs to try: \(alternatives)")
        return alternatives
    }
}

// WebSocket delegate protocol
protocol GameServiceDelegate {
    func gameService(_ service: GameService, createdRoom room: GameRoom)
    func gameService(_ service: GameService, joinedRoom room: GameRoom)
    func gameService(_ service: GameService, playerJoined player: Player)
    func gameService(_ service: GameService, playerLeft playerId: String)
    func gameServiceDidStartGame(_ service: GameService)
    func gameServiceDidEndGame(_ service: GameService)
    func gameServiceDidLeaveRoom(_ service: GameService)
    func gameService(_ service: GameService, didReceiveError error: Error)
    func gameService(_ service: GameService, receivedGameUrl url: String)
}
