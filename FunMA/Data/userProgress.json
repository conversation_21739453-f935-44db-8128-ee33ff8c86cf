[{"username": "U1", "courseId": "MATH102", "progress": 75, "score": 85, "lessonsProgress": [{"lessonNumber": 1, "lessonId": "L1", "score": 85, "status": "Not Started"}, {"lessonNumber": 2, "lessonId": "L2", "score": 85, "status": "Completed"}, {"lessonNumber": 3, "lessonId": "L3", "score": 85, "status": "Completed"}]}, {"username": "U1", "courseId": "MATH101", "progress": 70, "score": 78, "lessonsProgress": [{"lessonNumber": 1, "lessonId": "L1", "score": 85, "status": "Completed"}, {"lessonNumber": 2, "lessonId": "L2", "score": 85, "status": "Completed"}, {"lessonNumber": 3, "lessonId": "L3", "score": 85, "status": "Completed"}]}, {"username": "U2", "courseId": "MATH102", "progress": 100, "score": 92, "lessonsProgress": [{"lessonNumber": 1, "lessonId": "L1", "score": 85, "status": "Completed"}, {"lessonNumber": 2, "lessonId": "L2", "score": 85, "status": "Completed"}, {"lessonNumber": 3, "lessonId": "L3", "score": 85, "status": "Completed"}]}]