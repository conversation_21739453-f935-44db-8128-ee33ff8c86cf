# `/api/llm/generate-long-questions` Endpoint Implementation Summary

## **Endpoint Overview**
- **URL**: `/api/llm/generate-long-questions`
- **Method**: `POST`
- **Authentication**: Required (JWT token)
- **Purpose**: Generates comprehensive, long-form educational questions with detailed solutions

## **Updated App Implementation**

### **Request Format**
Your app now sends the correct request format:

```swift
var payload: [String: Any] = [
    "topic": topic,                    // Required
    "difficulty": difficultyLevel.apiValue,  // "beginner", "intermediate", "advanced"
    "num_questions": longAnswerCount,  // Optional: defaults to 3, max 10
    "deduct_credits": false           // Optional: set to false for testing
]

// Optional fields
if !subtopic.isEmpty {
    payload["subtopic"] = subtopic
}

if !customPrompt.isEmpty {
    payload["custom_prompt"] = customPrompt
}

// Additional optional fields you can add:
// payload["grade_level"] = "9th grade"
// payload["learning_objectives"] = "Solve complex problems"
```

### **Response Handling**
The app now properly handles the actual API response format:

```json
{
  "success": true,
  "generated_content": {
    "id": "e265f11c-d28c-47f0-8c5e-b492a8679b53",
    "type": "textResponse",
    "close": true,
    "error": null,
    "chatId": 431,
    "textResponse": "```json\n{\n    \"questions\": [\n        {\n            \"questionText\": \"Comprehensive question requiring detailed response\",\n            \"questionType\": \"long_question\",\n            \"timeAllocation\": \"15-20 minutes\",\n            \"maxPoints\": 15,\n            \"rubric\": {\n                \"excellent\": \"Criteria for 90-100% score\",\n                \"good\": \"Criteria for 75-89% score\",\n                \"satisfactory\": \"Criteria for 60-74% score\",\n                \"needs_improvement\": \"Criteria for below 60% score\"\n            },\n            \"sampleAnswer\": \"Detailed exemplary response\",\n            \"keyPoints\": [\n                \"Essential point 1\",\n                \"Essential point 2\",\n                \"Essential point 3\"\n            ],\n            \"commonMisconceptions\": [\n                \"Common error students might have\",\n                \"Another potential misunderstanding\"\n            ],\n            \"learningObjective\": \"What deep understanding this question assesses\",\n            \"difficulty\": 4,\n            \"cognitiveLevel\": \"analysis/synthesis/evaluation\"\n        }\n    ]\n}\n```",
    "metrics": {
      "prompt_tokens": 79804,
      "completion_tokens": 4090,
      "total_tokens": 84670,
      "outputTps": 181.30236269338178,
      "duration": 22.559
    }
  },
  "metadata": {
    "topic": "Angles and Parallel Lines",
    "subtopic": "Angles Related to Intersecting lines",
    "difficulty": "intermediate",
    "num_questions": 2,
    "questionType": "long_question",
    "grade_level": "",
    "learning_objectives": "",
    "credits_deducted": 0,
    "user_id": "683e6d0e8877f9fff65e7d99"
  }
}
```

**Key Points:**
- Questions are embedded in `generated_content.textResponse` as a JSON string
- The JSON string is wrapped in markdown code blocks (```json ... ```)
- The app extracts and parses this JSON string to get the questions array
- Additional metadata is available in the `metadata` object

## **Key Features Implemented**

### **1. Enhanced Question Structure**
Each generated long question includes:
- **Comprehensive question text** with LaTeX support
- **Time allocation** recommendations (15-20 minutes)
- **Maximum points** (typically 15)
- **Detailed scoring rubric** with 4 levels
- **Sample exemplary answer**
- **Key points** that should be addressed
- **Common misconceptions** to watch for
- **Learning objectives** assessment
- **Difficulty rating** (1-5 scale)
- **Cognitive level** classification

### **2. Credit System Integration**
- **Cost**: 3 credits per question
- **Maximum**: 10 questions per request (30 credits max)
- **Testing mode**: Set `deduct_credits: false` to avoid credit deduction

### **3. Error Handling**
The app now handles all error responses:

```swift
switch httpResponse.statusCode {
case 400:
    errorMessage = "Bad request - check your parameters"
case 401:
    errorMessage = "Authentication failed. Please log in again."
case 402:
    errorMessage = "Insufficient credits for question generation"
case 404:
    errorMessage = "API endpoint not found"
case 429:
    errorMessage = "Rate limit exceeded. Please try again later."
case 500:
    errorMessage = "Server error occurred while generating questions."
default:
    errorMessage = "Server returned status code \(httpResponse.statusCode)"
}
```

### **4. Response Parsing**
The app now correctly parses the actual response format:
- **JSON extraction** from `generated_content.textResponse` (removes markdown code blocks)
- **Questions array parsing** from the extracted JSON string
- **Enhanced logging** for debugging the parsing process
- **Fallback parsing** for malformed responses
- **LaTeX normalization** for mathematical expressions
- **Comprehensive error handling** for JSON parsing failures

## **Usage in Your App**

### **Teacher Interface**
Teachers can generate long questions through:
1. **InClassExerciseView** → **AIQuestionGenerationView**
2. Set **Long Answer Count** > 0
3. Choose **Topic** and **Subtopic**
4. Select **Difficulty Level**
5. Optionally add **Custom Prompt**
6. Click **Generate Questions**

### **Generated Question Types**
- **Analytical essays** requiring critical thinking
- **Multi-step problem solving** with explanations
- **Comparative analysis** questions
- **Real-world application** scenarios
- **Synthesis and evaluation** questions

## **API Configuration**

The endpoint is configured in `FunMA/Models/APIConfig.swift`:
```swift
static let llmGenerateLongQuestionsEndpoint = "\(baseURL)/llm/generate-long-questions"
```

Current base URL: `http://ec2-52-76-76-177.ap-southeast-1.compute.amazonaws.com:5001/api`

## **Testing Recommendations**

1. **Start with small batches**: Generate 1-2 questions first
2. **Test credit deduction**: Set `deduct_credits: false` initially
3. **Verify response parsing**: Check console logs for parsing success
4. **Test error scenarios**: Try invalid topics, insufficient credits, etc.
5. **Validate question quality**: Review generated questions for appropriateness

## **Future Enhancements**

Consider adding these UI improvements:
1. **Grade level selection** dropdown
2. **Learning objectives** text field
3. **Credit balance display** before generation
4. **Question preview** before adding to exercise
5. **Rubric display** for teachers to review

## **Backend Requirements**

Your backend should implement:
1. **JWT authentication** validation
2. **Credit deduction** system
3. **Rate limiting** (429 responses)
4. **AnythingLLM integration** for question generation
5. **Comprehensive error handling**
6. **Transaction logging** for credit deductions

## **Debugging Tips**

Check these console logs for issues:
```
🤖 Long Generation: Request payload: {...}
🔍 Long Generation: Outer response structure: {...}
🤖 Long Generation: Found X question dictionaries
✅ AI Generation: Successfully created X questions
```

Common issues:
- **401 errors**: Check JWT token validity
- **402 errors**: Insufficient user credits
- **429 errors**: Rate limiting, wait and retry
- **Parsing errors**: Check response format matches expected structure

This implementation provides a robust foundation for generating high-quality, comprehensive educational questions that require detailed analysis and extended responses from students. 