# Submission Decoding Fix

## Problem Description

The app was failing to decode student submissions from the backend with the error:
```
❌ ERROR: Failed to decode submissions: The data couldn't be read because it is missing.
```

However, the backend was actually returning valid JSON data with submission information.

## Root Cause Analysis

The issue was a **mismatch between the backend response format and the iOS model expectations**:

### Backend Response Format:
```json
[{
  "_id": "685e42c347a7004ad3d5f2e8",
  "exerciseId": "B4DECAA1-CBEC-488D-9B03-E77411A47DD3",
  "answers": [
    {
      "questionId": "4E1C1BF8-9BD6-4D3A-B3B7-B37C8CAB0D09",
      "answer": "Basic understanding",
      "isCorrect": true,
      "pointsEarned": 2
    },
    {
      "questionId": "1154E452-DDCC-4666-AB88-16A8649CA536",
      "answer": "Practical only",
      "isCorrect": false,
      "pointsEarned": 0
    },
    {
      "questionId": "E29BB107-4716-4342-A1DB-EA13036915FA",
      "answer": "1",
      "isCorrect": null,
      "pointsEarned": 0,
      "needsManualGrading": true
    }
  ],
  "studentId": "67d1cf038f749f11ece01eef",
  "id": "773e7f34-24b9-42b2-b0d2-3392aa561ddd",
  "startTime": "2025-06-27T07:05:39.419606+00:00",
  "endTime": "2025-06-27T07:05:39.419621+00:00",
  "isLateSubmission": false,
  "score": 28.57142857142857,
  "earnedPoints": 2,
  "totalPoints": 7,
  "gradingStatus": "pending",
  "exerciseTitle": "Ex1",
  "attempts": 1
}]
```

### Issues Identified:

1. **Missing `id` field in answers**: Backend answers don't have individual `id` fields, but iOS `QuestionSubmission` required them
2. **Extra fields in submissions**: Backend sends additional fields not defined in iOS models:
   - `_id` (MongoDB ObjectId)
   - `isLateSubmission`
   - `earnedPoints`
   - `totalPoints`
   - `gradingStatus`
   - `exerciseTitle`
   - `attempts`
3. **Extra fields in answers**: Backend sends `needsManualGrading` field not in iOS model

## Solution Implemented

### 1. Updated `StudentSubmission` Model

Added support for additional backend fields:

```swift
struct StudentSubmission: Identifiable, Codable {
    let id: UUID
    let exerciseId: UUID
    let studentId: String
    var answers: [QuestionSubmission]
    var startTime: Date
    var endTime: Date?
    var score: Double?
    
    // *** NEW: Additional fields from backend ***
    var mongoId: String? // Backend's MongoDB _id
    var isLateSubmission: Bool?
    var earnedPoints: Double?
    var totalPoints: Double?
    var gradingStatus: String?
    var exerciseTitle: String?
    var attempts: Int?
}
```

Updated `CodingKeys` to map `_id` to `mongoId`:
```swift
enum CodingKeys: String, CodingKey {
    case id
    case exerciseId
    case studentId
    case answers
    case startTime
    case endTime
    case score
    case mongoId = "_id"
    case isLateSubmission
    case earnedPoints
    case totalPoints
    case gradingStatus
    case exerciseTitle
    case attempts
}
```

### 2. Updated `QuestionSubmission` Model

Fixed the missing `id` field issue and added support for additional fields:

```swift
struct QuestionSubmission: Identifiable, Codable {
    let id: UUID
    let questionId: UUID
    var answer: String
    var isCorrect: Bool?
    var pointsEarned: Double?
    
    // *** NEW: Additional fields from backend ***
    var needsManualGrading: Bool?
}
```

**Key Fix**: Handle missing `id` field in backend response:
```swift
// Handle missing id field from backend - generate one if not present
if let idString = try container.decodeIfPresent(String.self, forKey: .id),
   let uuid = UUID(uuidString: idString) {
    self.id = uuid
} else {
    // Generate a new UUID if backend doesn't provide one
    self.id = UUID()
}
```

### 3. Enhanced Logging

Added comprehensive debugging to understand decoding failures:

```swift
// Log the raw JSON for debugging before decoding
if let responseString = String(data: data, encoding: .utf8) {
    ExerciseLogger.shared.log("Raw submission response before decoding: \(responseString)", type: .debug)
}

// Additional debugging for decoding error
if let decodingError = error as? DecodingError {
    ExerciseLogger.shared.log("Decoding error details:", type: .error)
    switch decodingError {
    case .dataCorrupted(let context):
        ExerciseLogger.shared.log("Data corrupted: \(context.debugDescription)", type: .error)
    case .keyNotFound(let key, let context):
        ExerciseLogger.shared.log("Key '\(key.stringValue)' not found: \(context.debugDescription)", type: .error)
    // ... more detailed error handling
    }
}
```

## Expected Results

After this fix:

✅ **Student submissions will decode successfully**  
✅ **Exercise completion status will work correctly**  
✅ **Additional backend fields will be preserved**  
✅ **Detailed error logging for future debugging**  

### Console Output (Success):
```
📚 ExerciseTakingView: Loading student submissions to determine completion status
📚 ExerciseTakingView: Successfully loaded 1 student submissions
📚 ExerciseTakingView: Completed exercise IDs: [B4DECAA1-CBEC-488D-9B03-E77411A47DD3]
📚 Exercise 'Ex1': COMPLETED
```

### Detailed Submission Logging:
```
✅ Successfully decoded 1 submissions for exercise B4DECAA1-CBEC-488D-9B03-E77411A47DD3
🔍 Submission 1: Student 67d1cf038f749f11ece01eef (ID: 773e7f34-24b9-42b2-b0d2-3392aa561ddd)
🔍   - Exercise ID: B4DECAA1-CBEC-488D-9B03-E77411A47DD3
🔍   - Score: 28.57142857142857
🔍   - Grading Status: pending
🔍   - Exercise Title: Ex1
🔍   - Answers Count: 3
```

## Testing

1. **Submit an exercise** and verify the submission is recorded
2. **Check console logs** for successful decoding messages
3. **Refresh the page** and verify the exercise stays in "completed" tab
4. **Use ExerciseDiagnosticView** to test "All Student Submissions (GET)"

## Backward Compatibility

The fix maintains backward compatibility:
- All new fields are optional
- Existing functionality is preserved
- Works with both old and new backend response formats

## Benefits of Enhanced Model

The updated models now capture additional valuable information from the backend:
- **Grading status** tracking
- **Exercise metadata** (title, points)
- **Submission metadata** (late submission, attempts)
- **Manual grading flags** for long-answer questions

This provides a foundation for more advanced features like:
- Late submission warnings
- Attempt tracking
- Grading status indicators
- Point breakdowns 