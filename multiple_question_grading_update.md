# Multiple Question Grading API Update

## Summary

This update implements support for the new teacher grading endpoint that allows grading **multiple questions in a single request**. The backend now accepts a `grades` array instead of just a `score` field, making grading more efficient and keeping the data model clean and consistent.

## What Changed

### Backend API Changes
- The `/api/submission` `PUT` endpoint now accepts a `grades` array with individual question grades
- Teachers can update `pointsEarned`, `isCorrect`, and `feedback` for any number of questions
- The backend recalculates total `earnedPoints` and updates grading status
- Backward compatibility maintained: sending only `score` and `feedback` still works

### Frontend Implementation

#### 1. New Data Model
Added `QuestionGrade` struct to represent individual question grades:

```swift
struct QuestionGrade {
    let questionId: UUID
    let pointsEarned: Double
    let isCorrect: Bool
    let feedback: String
    
    init(questionId: UUID, pointsEarned: Double, isCorrect: Bool, feedback: String = "") {
        self.questionId = questionId
        self.pointsEarned = pointsEarned
        self.isCorrect = isCorrect
        self.feedback = feedback
    }
}
```

#### 2. New Grading Functions
Added two new functions to `ExerciseViewModel`:

```swift
// Grade submission with multiple questions using submission object
func gradeSubmissionWithMultipleQuestions(
    submission: StudentSubmission, 
    grades: [QuestionGrade], 
    feedback: String, 
    gradingMethod: String = "manual"
) async throws

// Grade submission with multiple questions using MongoDB ObjectId
func gradeSubmissionWithMultipleQuestions(
    mongoId: String, 
    grades: [QuestionGrade], 
    feedback: String, 
    gradingMethod: String = "manual"
) async throws
```

#### 3. Updated Teacher Grading Interface
Modified `TeacherSubmissionReviewSheet.saveGrades()` to:
- Collect all question grades into a `grades` array
- Send the array in a single `PUT` request
- Include individual feedback for each question
- Combine individual feedback into overall feedback

#### 4. API Request Format
The new request format sends:

```json
{
  "grades": [
    {
      "questionId": "QID1",
      "pointsEarned": 10,
      "isCorrect": true,
      "feedback": "Excellent explanation!"
    },
    {
      "questionId": "QID2", 
      "pointsEarned": 7,
      "isCorrect": false,
      "feedback": "Check your math on this one."
    }
  ],
  "feedback": "Great effort overall!",
  "gradingMethod": "manual"
}
```

## Benefits

1. **Efficiency**: Grade multiple questions in a single API call
2. **Granular Feedback**: Provide specific feedback for each question
3. **Data Consistency**: Backend handles score recalculation automatically
4. **Backward Compatibility**: Existing code continues to work
5. **Better UX**: Teachers can grade all questions at once

## Testing

Added diagnostic test in `ExerciseDiagnosticView.swift`:
- `testMultipleQuestionGrading()` function
- Tests the new API with sample grades
- Verifies successful grading of multiple questions

## Migration Guide

### For Existing Code
- **No changes required** - existing `gradeSubmission()` functions still work
- The old `score`-based approach is still supported for backward compatibility

### For New Features
- Use `gradeSubmissionWithMultipleQuestions()` for better efficiency
- Collect grades in a `[QuestionGrade]` array
- Include individual feedback for each question

## Example Usage

```swift
// Create grades for multiple questions
let grades = [
    QuestionGrade(
        questionId: question1.id,
        pointsEarned: 8.0,
        isCorrect: true,
        feedback: "Excellent work!"
    ),
    QuestionGrade(
        questionId: question2.id,
        pointsEarned: 6.0,
        isCorrect: false,
        feedback: "Check your calculation."
    )
]

// Grade the submission
try await viewModel.gradeSubmissionWithMultipleQuestions(
    mongoId: submission.mongoId!,
    grades: grades,
    feedback: "Good effort overall!"
)
```

## Files Modified

1. **FunMA/Models/ExerciseModel.swift**
   - Added `QuestionGrade` struct
   - Added `gradeSubmissionWithMultipleQuestions()` functions
   - Maintained backward compatibility

2. **FunMA/View/Teacher/TeacherSubmissionReviewSheet.swift**
   - Updated `saveGrades()` to use new API format
   - Collects individual question grades and feedback
   - Sends grades array instead of single score

3. **ExerciseDiagnosticView.swift**
   - Added `testMultipleQuestionGrading()` function
   - Tests the new API with sample data

## Backward Compatibility

The implementation maintains full backward compatibility:
- Existing `gradeSubmission()` functions continue to work
- Old `score`-based requests are still accepted by the backend
- No breaking changes to existing code

This update makes grading more efficient while ensuring existing functionality continues to work seamlessly. 