# FunMA App Structure

```mermaid
flowchart TD
    %% Main App Structure
    App[FunMA App] -->|Initializes| SideBar[SideBarView]
    App -->|Loads| ContentView[ContentView]
    
    %% User Management
    UserManager{UserManager} -->|Authenticates| Student((Student))
    UserManager -->|Authenticates| Teacher((Teacher))
    UserManager -->|Authenticates| Guest((Guest))
    
    %% Main Navigation Structure
    SideBar -->|Student Access| StudentDashboard[DashboardView]
    SideBar -->|Teacher Access| TeacherDashboard[DashboardView]
    SideBar -->|Guest Access| GuestGameJoin[Game Join View]
    
    %% Student Features
    StudentDashboard -->|Shows| CourseOverview[Course Overview]
    StudentDashboard -->|Displays| PerformanceChart[Performance Chart]
    StudentDashboard -->|Recommends| Recommendations[Course Recommendations]
    
    CourseOverview -->|Opens| CourseView[Course View]
    CourseView -->|Plays| VideoView[Video Player]
    CourseView -->|Starts| QuizView[Quiz Interface]
    
    %% Teacher Features
    TeacherDashboard -->|Creates| InClassExercise[Exercise Creator]
    TeacherDashboard -->|Manages| StudentManagement[Student List]
    TeacherDashboard -->|Organizes| CourseManagement[Course Manager]
    TeacherDashboard -->|Views| Analytics[Analytics Dashboard]
    
    %% Interactive Tools
    InteractiveTools[Tools Hub] -->|Opens| WebView[Web Interface]
    InteractiveTools -->|Launches| ARExperience[AR Viewer]
    InteractiveTools -->|Shows| ModelViewer[3D Models]
    InteractiveTools -->|Activates| AIAssistant[AI Helper]
    
    %% AR Components
    ARExperience -->|Selects| ShapeSelection[Shape Explorer]
    ShapeSelection -->|Displays| CubeView[Cube Model]
    ShapeSelection -->|Shows| ConeView[Cone Model]
    ShapeSelection -->|Renders| SphereView[Sphere Model]
    ShapeSelection -->|Demonstrates| TriangleFolding[Triangle Model]
    
    %% Marketplace
    Marketplace[Course Store] -->|Shows Details| CourseDetail[Course Info]
    Marketplace -->|Filters| FilterButton[Search Tools]
    
    %% Exercise System
    InClassExercise -->|Assigns| ExerciseTaking[Exercise View]
    ExerciseTaking -->|Contains| QuizComponents[Question Types]
    
    %% Quiz Components
    QuizComponents -->|Supports| QuestionImageText[Image + Text Q]
    QuizComponents -->|Shows| QuestionImageOnly[Image Only Q]
    QuizComponents -->|Displays| QuestionTextOnly[Text Only Q]
    QuizComponents -->|Presents| ResponseMC[Multiple Choice]
    QuizComponents -->|Accepts| ResponseSAQ[Short Answer]
    
    %% Data Flow
    CoursesViewModel((Courses VM)) -->|Updates| CourseView
    MarketplaceViewModel((Marketplace VM)) -->|Manages| Marketplace
    ExerciseViewModel((Exercise VM)) -->|Controls| InClassExercise
    ExerciseViewModel -->|Handles| ExerciseTaking
    
    %% Styling
    classDef view fill:#f9f,stroke:#333,stroke-width:2px
    classDef model fill:#bbf,stroke:#333,stroke-width:2px
    classDef manager fill:#bfb,stroke:#333,stroke-width:2px
    classDef vm fill:#fbb,stroke:#333,stroke-width:2px
    
    class App,ContentView,SideBar,StudentDashboard,TeacherDashboard,CourseView,VideoView,QuizView,InClassExercise,InteractiveTools,Marketplace,CourseDetail,ExerciseTaking view
    class UserManager manager
    class Student,Teacher,Guest model
    class CoursesViewModel,MarketplaceViewModel,ExerciseViewModel vm
```

## How to View the Chart

You can view this Mermaid chart in several ways:

1. **GitHub**: If you push this file to GitHub, it will automatically render the Mermaid chart in the markdown preview.

2. **VS Code**: Install the "Markdown Preview Mermaid Support" extension to view the chart directly in VS Code.

3. **Online Mermaid Editor**: 
   - Copy the content between the ```mermaid tags
   - Visit [Mermaid Live Editor](https://mermaid.live)
   - Paste the content and see the rendered chart

4. **Mermaid CLI**: If you want to generate an image file, you can use the Mermaid CLI tool.

## Chart Legend

- **Square Nodes [View]**: Represent SwiftUI views and UI components
- **Diamond Nodes {Manager}**: Represent managers and controllers
- **Circle Nodes ((Model))**: Represent data models and user roles
- **Double Circle Nodes ((VM))**: Represent ViewModels
- **Connections -->|Label|**: Show the relationship between components

## Structure Explanation

The chart shows the hierarchical structure of the FunMA app, including:
- Main app components with descriptive connections
- User role management with authentication flow
- Navigation structure with access control
- Feature modules with their relationships
- Data flow with ViewModel connections
- Component relationships with labeled connections 