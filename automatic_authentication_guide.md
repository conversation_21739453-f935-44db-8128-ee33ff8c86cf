# Automatic Authentication with APIService

## Overview

The `APIService` and `UserManager` have been enhanced to automatically embed the `accessToken` in every API request. This eliminates the need to manually add authorization headers and provides automatic token refresh functionality.

## 🚀 Key Features

- **Automatic Authentication**: All API requests automatically include the Bearer token
- **Token Refresh**: Automatic token refresh when receiving 401 errors
- **Retry Logic**: Failed requests are automatically retried after token refresh
- **Error Handling**: Consistent error handling across all API calls
- **Type Safety**: Full Swift type safety with generics

## 📋 How to Use

### Option 1: Direct APIService Usage

Use `APIService.shared` for basic API operations:

```swift
// GET request
let result: Result<[Course], APIError> = await APIService.shared.fetch(
    endpoint: "courses", 
    responseType: [Course].self
)

// POST request
let result: Result<Course, APIError> = await APIService.shared.post(
    endpoint: "courses",
    body: newCourse,
    responseType: Course.self
)

// PUT request  
let result: Result<Course, APIError> = await APIService.shared.put(
    endpoint: "courses/\(courseId)",
    body: updatedCourse,
    responseType: Course.self
)

// DELETE request
let result: Result<EmptyResponse, APIError> = await APIService.shared.delete(
    endpoint: "courses/\(courseId)"
)
```

### Option 2: UserManager.api (Recommended)

Use `UserManager.shared.api` for automatic authentication validation:

```swift
do {
    // GET request with automatic auth validation
    let courses: [Course] = try await UserManager.shared.api.get("courses")
    
    // POST request with automatic auth validation
    let newCourse: Course = try await UserManager.shared.api.post("courses", body: courseData)
    
    // PUT request with automatic auth validation
    let updatedCourse: Course = try await UserManager.shared.api.put("courses/\(id)", body: courseData)
    
    // DELETE request with automatic auth validation
    try await UserManager.shared.api.delete("courses/\(id)")
    
    // Fetch raw data (images, files, etc.)
    let imageData: Data = try await UserManager.shared.api.fetchData("images/profile.jpg")
    
} catch {
    print("API Error: \(error)")
}
```

## 🔄 Migration Guide

### Old Manual Approach (❌ Don't use this anymore)

```swift
// OLD WAY - Manual auth header management
func fetchUserProfile() async -> Bool {
    guard let url = URL(string: APIConfig.userProfileEndpoint) else { return false }
    
    var request = URLRequest(url: url)
    request.httpMethod = "GET"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    
    // Manually add authorization header
    if let authHeaders = UserManager.shared.getAuthorizationHeader() {
        for (key, value) in authHeaders {
            request.addValue(value, forHTTPHeaderField: key)
        }
    }
    
    // Manual URLSession handling...
}
```

### New Automatic Approach (✅ Use this)

```swift
// NEW WAY - Automatic authentication
func fetchUserProfile() async throws -> User {
    return try await UserManager.shared.api.get("user/profile", responseType: User.self)
}
```

## 🛡️ Authentication Features

### Automatic Token Refresh

When a request receives a 401 (Unauthorized) response:

1. **Automatic Refresh**: The system automatically attempts to refresh the access token
2. **Retry Logic**: If refresh succeeds, the original request is retried with the new token
3. **Logout on Failure**: If refresh fails, the user is automatically logged out

### Error Handling

```swift
do {
    let data = try await UserManager.shared.api.get("protected-endpoint")
    // Handle success
} catch APIError.authenticationFailed {
    // User was logged out due to auth failure
    print("Please log in again")
} catch APIError.accessDenied {
    // User doesn't have permission
    print("Access denied")
} catch APIError.serverError(let statusCode) {
    // Server error
    print("Server error: \(statusCode)")
} catch {
    // Other errors
    print("Error: \(error)")
}
```

## 📊 Benefits

### Before (Manual Authentication)
- ❌ Repetitive code for adding auth headers
- ❌ No automatic token refresh
- ❌ Inconsistent error handling
- ❌ Manual URLSession management
- ❌ Easy to forget authentication

### After (Automatic Authentication)
- ✅ Zero authentication boilerplate
- ✅ Automatic token refresh
- ✅ Consistent error handling
- ✅ Type-safe API calls
- ✅ Impossible to forget authentication

## 🔧 Implementation Examples

### Fetching User Data
```swift
// Simple and clean
func loadUserCourses() async {
    do {
        let courses: [Course] = try await UserManager.shared.api.get("user/courses")
        await MainActor.run {
            self.courses = courses
        }
    } catch {
        await MainActor.run {
            self.errorMessage = error.localizedDescription
        }
    }
}
```

### Creating New Resources
```swift
func createExercise(_ exercise: Exercise) async {
    do {
        let createdExercise: Exercise = try await UserManager.shared.api.post(
            "exercises", 
            body: exercise
        )
        await MainActor.run {
            self.exercises.append(createdExercise)
        }
    } catch {
        await MainActor.run {
            self.errorMessage = "Failed to create exercise: \(error.localizedDescription)"
        }
    }
}
```

### Updating Resources
```swift
func updateExercise(_ exercise: Exercise) async {
    do {
        let updatedExercise: Exercise = try await UserManager.shared.api.put(
            "exercises/\(exercise.id)",
            body: exercise
        )
        await MainActor.run {
            if let index = exercises.firstIndex(where: { $0.id == exercise.id }) {
                exercises[index] = updatedExercise
            }
        }
    } catch {
        await MainActor.run {
            self.errorMessage = "Failed to update exercise: \(error.localizedDescription)"
        }
    }
}
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Your Code     │───▶│  UserManager.api │───▶│   APIService    │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │                         │
                              ▼                         ▼
                       ┌──────────────┐         ┌─────────────────┐
                       │ Auth Check   │         │ Auto Auth       │
                       │ requireAuth  │         │ Header          │
                       └──────────────┘         └─────────────────┘
                                                         │
                                                         ▼
                                                ┌─────────────────┐
                                                │ URLSession +    │
                                                │ Token Refresh   │
                                                └─────────────────┘
```

## 📝 Notes

- The access token is automatically included in all requests
- Token refresh happens transparently 
- Failed requests are automatically retried after token refresh
- Users are logged out if token refresh fails
- All network calls should now use the new `APIService` or `UserManager.api` 