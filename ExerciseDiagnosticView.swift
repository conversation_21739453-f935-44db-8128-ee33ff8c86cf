import SwiftUI

struct ExerciseDiagnosticView: View {
    @StateObject private var exerciseViewModel = ExerciseViewModel()
    @State private var serverPingStatus: TestStatus = .notStarted
    @State private var exerciseRetrievalStatus: TestStatus = .notStarted
    @State private var exerciseCreationStatus: TestStatus = .notStarted
    @State private var jsonTestStatus: TestStatus = .notStarted
    @State private var authCheckStatus: TestStatus = .notStarted
    @State private var dateFormatTestStatus: TestStatus = .notStarted
    
    // *** NEW: CRUD Operation Test States ***
    @State private var updateExerciseStatus: TestStatus = .notStarted
    @State private var deleteExerciseStatus: TestStatus = .notStarted
    @State private var studentExerciseStatus: TestStatus = .notStarted
    @State private var submissionTestStatus: TestStatus = .notStarted
    @State private var gradingTestStatus: TestStatus = .notStarted
    
    @State private var serverPingMessage = ""
    @State private var exerciseRetrievalMessage = ""
    @State private var exerciseCreationMessage = ""
    @State private var jsonTestMessage = ""
    @State private var authCheckMessage = ""
    @State private var dateFormatTestMessage = ""
    
    // *** NEW: CRUD Operation Messages ***
    @State private var updateExerciseMessage = ""
    @State private var deleteExerciseMessage = ""
    @State private var studentExerciseMessage = ""
    @State private var submissionTestMessage = ""
    @State private var gradingTestMessage = ""
    
    // *** Test Exercise Storage ***
    @State private var testExerciseId: UUID?
    
    // Test score calculation fix
    @State private var scoreCalculationTestStatus: TestStatus = .notStarted
    @State private var scoreCalculationTestMessage = ""
    
    // Test specific submission analysis
    @State private var submissionAnalysisStatus: TestStatus = .notStarted
    @State private var submissionAnalysisMessage = ""
    
    // *** NEW: Test for user's specific data ***
    @State private var userDataTestStatus: TestStatus = .notStarted
    @State private var userDataTestMessage = ""
    
    // *** NEW: Test multiple question grading API ***
    @State private var multipleQuestionGradingTestStatus: TestStatus = .notStarted
    @State private var multipleQuestionGradingTestMessage = ""
    
    enum TestStatus {
        case notStarted
        case running
        case success
        case failure
        
        var color: Color {
            switch self {
            case .notStarted: return .gray
            case .running: return .blue
            case .success: return .green
            case .failure: return .red
            }
        }
        
        var icon: String {
            switch self {
            case .notStarted: return "circle"
            case .running: return "clock"
            case .success: return "checkmark.circle"
            case .failure: return "xmark.circle"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    Text("Exercise System Diagnostics")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    // Authentication Status Check
                    DiagnosticRow(
                        title: "Authentication Status",
                        status: authCheckStatus,
                        message: authCheckMessage,
                        action: checkAuthenticationStatus
                    )
                    
                    // Server Ping Test
                    DiagnosticRow(
                        title: "Server Ping Test",
                        status: serverPingStatus,
                        message: serverPingMessage,
                        action: pingServer
                    )
                    
                    // Exercise Retrieval Test
                    DiagnosticRow(
                        title: "Exercise Retrieval",
                        status: exerciseRetrievalStatus,
                        message: exerciseRetrievalMessage,
                        action: testExerciseRetrieval
                    )
                    
                    // Exercise Creation Test
                    DiagnosticRow(
                        title: "Exercise Creation",
                        status: exerciseCreationStatus,
                        message: exerciseCreationMessage,
                        action: testExerciseCreation
                    )
                    
                    // JSON Encoding/Decoding Test
                    DiagnosticRow(
                        title: "JSON Processing",
                        status: jsonTestStatus,
                        message: jsonTestMessage,
                        action: testJSONProcessing
                    )
                    
                    // Date Format Test
                    DiagnosticRow(
                        title: "Date Format",
                        status: dateFormatTestStatus,
                        message: dateFormatTestMessage,
                        action: testDateFormat
                    )
                    
                    // Simple Date Test
                    DiagnosticRow(
                        title: "Simple Date Exercise",
                        status: dateFormatTestStatus,
                        message: dateFormatTestMessage,
                        action: testSimpleDateExercise
                    )
                    
                    // Minimal Exercise Test
                    DiagnosticRow(
                        title: "Minimal Exercise Test",
                        status: exerciseCreationStatus,
                        message: exerciseCreationMessage,
                        action: testMinimalExercise
                    )
                    
                    // Manual JSON Test
                    DiagnosticRow(
                        title: "Manual JSON Test",
                        status: jsonTestStatus,
                        message: jsonTestMessage,
                        action: testManualJSON
                    )
                    
                    Divider()
                    
                    Text("*** NEW BACKEND API CRUD TESTS ***")
                        .font(.headline)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                        .padding(.top)
                    
                    // Exercise Update Test
                    DiagnosticRow(
                        title: "Exercise Update (PUT)",
                        status: updateExerciseStatus,
                        message: updateExerciseMessage,
                        action: testExerciseUpdate
                    )
                    
                    // Exercise Deletion Test
                    DiagnosticRow(
                        title: "Exercise Delete (DELETE)",
                        status: deleteExerciseStatus,
                        message: deleteExerciseMessage,
                        action: testExerciseDelete
                    )
                    
                    // Student Exercise Retrieval Test
                    DiagnosticRow(
                        title: "Student Exercises (GET)",
                        status: studentExerciseStatus,
                        message: studentExerciseMessage,
                        action: testStudentExercises
                    )
                    
                    // *** NEW: All Student Submissions Test ***
                    DiagnosticRow(
                        title: "All Student Submissions (GET)",
                        status: submissionTestStatus,
                        message: submissionTestMessage,
                        action: testAllStudentSubmissions
                    )
                    
                    // Submission Test
                    DiagnosticRow(
                        title: "Exercise Submission (POST)",
                        status: submissionTestStatus,
                        message: submissionTestMessage,
                        action: testExerciseSubmission
                    )
                    
                    // Grading Test
                    DiagnosticRow(
                        title: "Submission Grading (PUT)",
                        status: gradingTestStatus,
                        message: gradingTestMessage,
                        action: testSubmissionGrading
                    )
                    
                    // Test score calculation fix
                    DiagnosticRow(
                        title: "Score Calculation Fix",
                        status: scoreCalculationTestStatus,
                        message: scoreCalculationTestMessage,
                        action: testScoreCalculationFix
                    )
                    
                    // Test specific submission analysis
                    DiagnosticRow(
                        title: "Submission Analysis",
                        status: submissionAnalysisStatus,
                        message: submissionAnalysisMessage,
                        action: testSpecificSubmissionAnalysis
                    )
                    
                    // *** NEW: Test for user's specific data ***
                    DiagnosticRow(
                        title: "User's Data Test",
                        status: userDataTestStatus,
                        message: userDataTestMessage,
                        action: testUserSpecificData
                    )
                    
                    // *** NEW: Test multiple question grading API ***
                    DiagnosticRow(
                        title: "Multiple Question Grading API",
                        status: multipleQuestionGradingTestStatus,
                        message: multipleQuestionGradingTestMessage,
                        action: testMultipleQuestionGrading
                    )
                    
                    Spacer(minLength: 20)
                    
                    HStack(spacing: 12) {
                        Button("Run All Tests") {
                            runAllTests()
                        }
                        .buttonStyle(.borderedProminent)
                        .frame(maxWidth: .infinity)
                        
                        Button("Run CRUD Only") {
                            runCRUDTests()
                        }
                        .buttonStyle(.bordered)
                        .frame(maxWidth: .infinity)
                    }
                    
                    Button("Reset All Tests") {
                        resetAllTests()
                    }
                    .buttonStyle(.bordered)
                    .frame(maxWidth: .infinity)
                }
                .padding()
            }
        }
    }
    
    private func checkAuthenticationStatus() {
        authCheckStatus = .running
        authCheckMessage = "Checking authentication..."
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let userManager = UserManager.shared
            
            if userManager.isLoggedIn {
                if let authHeader = userManager.getAuthorizationHeader() {
                    authCheckStatus = .success
                    authCheckMessage = "✅ User is logged in as '\(userManager.currentUser.username)' with valid token"
                } else {
                    authCheckStatus = .failure
                    authCheckMessage = "❌ User appears logged in but no auth token available"
                }
            } else {
                authCheckStatus = .failure
                authCheckMessage = "❌ User is not logged in. Please log in first."
            }
        }
    }
    
    private func pingServer() {
        serverPingStatus = .running
        serverPingMessage = "Pinging server..."
        
        Task {
            do {
                let startTime = Date()
                try await exerciseViewModel.pingServer()
                let responseTime = Date().timeIntervalSince(startTime)
                
                await MainActor.run {
                    serverPingStatus = .success
                    serverPingMessage = "✅ Server responded in \(String(format: "%.2f", responseTime))s"
                }
            } catch {
                await MainActor.run {
                    serverPingStatus = .failure
                    serverPingMessage = "❌ Server ping failed: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testExerciseRetrieval() {
        exerciseRetrievalStatus = .running
        exerciseRetrievalMessage = "Retrieving exercises..."
        
        Task {
            do {
                try await exerciseViewModel.getAllExercises()
                await MainActor.run {
                    exerciseRetrievalStatus = .success
                    exerciseRetrievalMessage = "✅ Retrieved \(exerciseViewModel.exercises.count) exercises"
                }
            } catch {
                await MainActor.run {
                    exerciseRetrievalStatus = .failure
                    exerciseRetrievalMessage = "❌ Failed to retrieve exercises: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testExerciseCreation() {
        exerciseCreationStatus = .running
        exerciseCreationMessage = "Testing exercise creation..."
        
        Task {
            let testExercise = Exercise(
                id: UUID(),
                title: "Diagnostic Test Exercise",
                topic: "Testing",
                subtopic: "System Diagnostics",
                grade: .form1,
                assignedClasses: ["TEST"],
                questions: [
                    Question(
                        id: UUID(),
                        questionText: "What is 2 + 2?",
                        questionType: .multipleChoice,
                        options: ["3", "4", "5", "6"],
                        correctAnswer: "4",
                        points: 1
                    )
                ],
                createdBy: UserManager.shared.currentUser.id,
                createdAt: Date(),
                dueDate: Date().addingTimeInterval(24 * 60 * 60) // Due tomorrow
            )
            
            do {
                try await exerciseViewModel.createExercise(testExercise)
                await MainActor.run {
                    exerciseCreationStatus = .success
                    exerciseCreationMessage = "✅ Test exercise created successfully via POST /api/exercise"
                    testExerciseId = testExercise.id // Store for update/delete tests
                }
            } catch {
                await MainActor.run {
                    exerciseCreationStatus = .failure
                    exerciseCreationMessage = "❌ Failed to create exercise: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testJSONProcessing() {
        jsonTestStatus = .running
        jsonTestMessage = "Testing JSON encoding/decoding..."
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            do {
                let testExercise = Exercise(
                    id: UUID(),
                    title: "JSON Test Exercise",
                    topic: "Testing",
                    subtopic: "JSON Processing",
                    grade: .form1,
                    assignedClasses: ["TEST"],
                    questions: [],
                    createdBy: UserManager.shared.currentUser.id,
                    createdAt: Date(),
                    dueDate: Date().addingTimeInterval(24 * 60 * 60)
                )
                
                // Test encoding
                let jsonData = try JSONEncoder().encode(testExercise)
                
                // Test decoding
                let decodedExercise = try JSONDecoder().decode(Exercise.self, from: jsonData)
                
                if decodedExercise.title == testExercise.title {
                    jsonTestStatus = .success
                    jsonTestMessage = "✅ JSON encoding/decoding works correctly"
                } else {
                    jsonTestStatus = .failure
                    jsonTestMessage = "❌ JSON data integrity check failed"
                }
            } catch {
                jsonTestStatus = .failure
                jsonTestMessage = "❌ JSON processing failed: \(error.localizedDescription)"
            }
        }
    }
    
    private func testDateFormat() {
        dateFormatTestStatus = .running
        dateFormatTestMessage = "Testing date formats..."
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let testDate = Date()
            var formatTests: [String] = []
            
            // ISO8601 format
            let iso8601Formatter = ISO8601DateFormatter()
            let iso8601String = iso8601Formatter.string(from: testDate)
            formatTests.append("ISO8601: \(iso8601String)")
            
            // Custom UTC format
            let utcFormatter = DateFormatter()
            utcFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
            utcFormatter.timeZone = TimeZone(abbreviation: "UTC")
            let utcString = utcFormatter.string(from: testDate)
            formatTests.append("UTC: \(utcString)")
            
            // Simple date format
            let simpleDateFormatter = DateFormatter()
            simpleDateFormatter.dateFormat = "yyyy-MM-dd"
            let simpleDateString = simpleDateFormatter.string(from: testDate)
            formatTests.append("Simple: \(simpleDateString)")
            
            // RFC3339 format
            let rfc3339Formatter = DateFormatter()
            rfc3339Formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssXXXXX"
            rfc3339Formatter.timeZone = TimeZone(abbreviation: "UTC")
            let rfc3339String = rfc3339Formatter.string(from: testDate)
            formatTests.append("RFC3339: \(rfc3339String)")
            
            // MongoDB ISODate format
            let mongoFormatter = DateFormatter()
            mongoFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
            mongoFormatter.timeZone = TimeZone(abbreviation: "UTC")
            let mongoString = mongoFormatter.string(from: testDate)
            formatTests.append("MongoDB: \(mongoString)")
            
            dateFormatTestStatus = .success
            dateFormatTestMessage = "✅ Date formats generated successfully. Check logs for details."
            
            // Log all formats for debugging
            print("📅 Date Format Test Results:")
            for format in formatTests {
                print("📅 \(format)")
            }
        }
    }
    
    private func testSimpleDateExercise() {
        dateFormatTestStatus = .running
        dateFormatTestMessage = "Testing simple date exercise..."
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let testDate = Date()
            let simpleDateFormatter = DateFormatter()
            simpleDateFormatter.dateFormat = "yyyy-MM-dd"
            let simpleDateString = simpleDateFormatter.string(from: testDate)
            
            dateFormatTestStatus = .success
            dateFormatTestMessage = "✅ Simple date exercise completed successfully. Result: \(simpleDateString)"
        }
    }
    
    private func testMinimalExercise() {
        exerciseCreationStatus = .running
        exerciseCreationMessage = "Testing minimal exercise creation..."
        
        Task {
            let testExercise = Exercise(
                id: UUID(),
                title: "Minimal Exercise Test",
                topic: "Testing",
                subtopic: "System Diagnostics",
                grade: .form1,
                assignedClasses: ["TEST"],
                questions: [],
                createdBy: UserManager.shared.currentUser.id,
                createdAt: Date(),
                dueDate: Date().addingTimeInterval(24 * 60 * 60) // Due tomorrow
            )
            
            do {
                try await exerciseViewModel.createExercise(testExercise)
                await MainActor.run {
                    exerciseCreationStatus = .success
                    exerciseCreationMessage = "✅ Minimal exercise created successfully"
                }
            } catch {
                await MainActor.run {
                    exerciseCreationStatus = .failure
                    exerciseCreationMessage = "❌ Failed to create minimal exercise: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testManualJSON() {
        jsonTestStatus = .running
        jsonTestMessage = "Testing manual JSON..."
        
        Task {
            do {
                let success = await exerciseViewModel.testManualJSONCreation()
                await MainActor.run {
                    if success {
                        jsonTestStatus = .success
                        jsonTestMessage = "✅ Manual JSON test successful"
                    } else {
                        jsonTestStatus = .failure
                        jsonTestMessage = "❌ Manual JSON test failed"
                    }
                }
            } catch {
                await MainActor.run {
                    jsonTestStatus = .failure
                    jsonTestMessage = "❌ Manual JSON test error: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // *** NEW: CRUD Operation Test Functions ***
    
    private func testExerciseUpdate() {
        guard let exerciseId = testExerciseId else {
            updateExerciseStatus = .failure
            updateExerciseMessage = "❌ No test exercise available. Create one first."
            return
        }
        
        updateExerciseStatus = .running
        updateExerciseMessage = "Testing exercise update..."
        
        Task {
            let updatedExercise = Exercise(
                id: exerciseId,
                title: "UPDATED: Diagnostic Test Exercise",
                topic: "Updated Testing",
                subtopic: "System Diagnostics - Modified",
                grade: .form2, // Changed grade
                assignedClasses: ["TEST", "UPDATE"], // Added class
                questions: [
                    Question(
                        id: UUID(),
                        questionText: "What is 3 + 3? (Updated)",
                        questionType: .multipleChoice,
                        options: ["5", "6", "7", "8"],
                        correctAnswer: "6",
                        points: 2
                    )
                ],
                createdBy: UserManager.shared.currentUser.id,
                createdAt: Date(),
                dueDate: Date().addingTimeInterval(48 * 60 * 60) // Due in 2 days
            )
            
            do {
                try await exerciseViewModel.updateExercise(updatedExercise)
                await MainActor.run {
                    updateExerciseStatus = .success
                    updateExerciseMessage = "✅ Exercise updated successfully via PUT /api/exercise/\(exerciseId.uuidString)"
                }
            } catch {
                await MainActor.run {
                    updateExerciseStatus = .failure
                    updateExerciseMessage = "❌ Failed to update exercise: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testExerciseDelete() {
        guard let exerciseId = testExerciseId else {
            deleteExerciseStatus = .failure
            deleteExerciseMessage = "❌ No test exercise available. Create one first."
            return
        }
        
        deleteExerciseStatus = .running
        deleteExerciseMessage = "Testing exercise deletion..."
        
        Task {
            do {
                try await exerciseViewModel.deleteExercise(exerciseId)
                await MainActor.run {
                    deleteExerciseStatus = .success
                    deleteExerciseMessage = "✅ Exercise deleted successfully via DELETE /api/exercise/\(exerciseId.uuidString)"
                    testExerciseId = nil // Clear the test exercise ID
                }
            } catch {
                await MainActor.run {
                    deleteExerciseStatus = .failure
                    deleteExerciseMessage = "❌ Failed to delete exercise: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testStudentExercises() {
        studentExerciseStatus = .running
        studentExerciseMessage = "Testing student exercise retrieval..."
        
        Task {
            do {
                try await exerciseViewModel.getStudentExercises()
                await MainActor.run {
                    studentExerciseStatus = .success
                    studentExerciseMessage = "✅ Retrieved \(exerciseViewModel.exercises.count) student exercises via GET /api/student/exercises (JWT authenticated)"
                }
            } catch {
                await MainActor.run {
                    studentExerciseStatus = .failure
                    studentExerciseMessage = "❌ Failed to retrieve student exercises: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testAllStudentSubmissions() {
        submissionTestStatus = .running
        submissionTestMessage = "Testing optimized single API call for all student submissions..."
        
        Task {
            do {
                try await exerciseViewModel.getAllStudentSubmissions()
                
                submissionTestStatus = .success
                submissionTestMessage = "✅ Retrieved \(exerciseViewModel.studentSubmissions.count) student submissions via single optimized GET /api/submission call (JWT authenticated)"
                
                // Debug: Log submission details
                let completedExerciseIds = Set(exerciseViewModel.studentSubmissions.map { $0.exerciseId })
                print("🧪 Diagnostic: Found submissions for \(completedExerciseIds.count) exercises")
                
                for submission in exerciseViewModel.studentSubmissions {
                    print("🧪 Submission: Exercise \(submission.exerciseId.uuidString) - Score: \(submission.score?.description ?? "nil")")
                }
                
            } catch {
                submissionTestStatus = .failure
                submissionTestMessage = "❌ Failed to retrieve student submissions with optimized method: \(error.localizedDescription)"
            }
        }
    }
    
    private func testExerciseSubmission() {
        guard let exerciseId = testExerciseId ?? exerciseViewModel.exercises.first?.id else {
            submissionTestStatus = .failure
            submissionTestMessage = "❌ No exercise available for submission. Create one first."
            return
        }
        
        submissionTestStatus = .running
        submissionTestMessage = "Testing exercise submission..."
        
        Task {
            // Create a test submission
            let testSubmission = StudentSubmission(
                id: UUID(),
                exerciseId: exerciseId,
                studentId: UserManager.shared.currentUser.id,
                answers: [
                    QuestionSubmission(
                        id: UUID(),
                        questionId: UUID(), // In real usage, this would be from the exercise
                        answer: "Test answer"
                    )
                ],
                startTime: Date().addingTimeInterval(-300) // Started 5 minutes ago
            )
            
            // Set end time
            var submissionWithEndTime = testSubmission
            submissionWithEndTime.endTime = Date()
            
            do {
                try await exerciseViewModel.submitSubmission(submissionWithEndTime)
                await MainActor.run {
                    submissionTestStatus = .success
                    submissionTestMessage = "✅ Exercise submission successful via POST /api/submissions"
                }
            } catch {
                await MainActor.run {
                    submissionTestStatus = .failure
                    submissionTestMessage = "❌ Failed to submit exercise: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func testSubmissionGrading() {
        guard let firstSubmission = exerciseViewModel.studentSubmissions.first else {
            gradingTestStatus = .failure
            gradingTestMessage = "❌ No submission available for grading. Submit one first."
            return
        }
        
        gradingTestStatus = .running
        gradingTestMessage = "Testing submission grading..."
        
        Task {
            do {
                if let mongoId = firstSubmission.mongoId {
                    try await exerciseViewModel.gradeSubmission(
                        mongoId: mongoId,
                        score: 85.0,
                        feedback: "Good work! This is a test grading via the diagnostic system.",
                        gradingMethod: "manual"
                    )
                    await MainActor.run {
                        gradingTestStatus = .success
                        gradingTestMessage = "✅ Submission graded successfully via PUT /api/submission?submission_id=\(mongoId)"
                    }
                } else {
                    await MainActor.run {
                        gradingTestStatus = .failure
                        gradingTestMessage = "❌ Failed to grade submission: MongoDB ObjectId is missing"
                    }
                }
            } catch {
                await MainActor.run {
                    gradingTestStatus = .failure
                    gradingTestMessage = "❌ Failed to grade submission: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // Test score calculation fix
    private func testScoreCalculationFix() {
        scoreCalculationTestStatus = .running
        scoreCalculationTestMessage = "Testing score calculation fix..."
        
        Task {
            do {
                // Create a test exercise with known questions
                let testExercise = Exercise(
                    id: UUID(uuidString: "6BBC7580-C087-4614-9E47-390F3CAA8353")!,
                    title: "Test Exercise for Score Calculation",
                    topic: "Test Topic",
                    subtopic: "Test Subtopic",
                    classroomIds: ["test-classroom"],
                    questions: [
                        Question(
                            id: UUID(uuidString: "BF0ECFA5-BBAD-4CAF-90A1-47398F829377")!,
                            questionText: "What is 2 + 2?",
                            questionType: .multipleChoice,
                            options: ["3", "4", "5", "6"],
                            correctAnswerIndex: 1, // Answer "4" is correct
                            points: 5
                        ),
                        Question(
                            id: UUID(uuidString: "FB9BD9A4-B6F4-4658-BA00-46ED8616233A")!,
                            questionText: "What is 3 x 3?",
                            questionType: .multipleChoice,
                            options: ["6", "9", "12", "15"],
                            correctAnswerIndex: 1, // Answer "9" is correct
                            points: 5
                        ),
                        Question(
                            id: UUID(uuidString: "47940621-E4AC-492E-8F08-C775B1DB8C64")!,
                            questionText: "Explain your reasoning",
                            questionType: .longQuestion,
                            options: nil,
                            correctAnswerIndex: 0,
                            points: 10
                        )
                    ],
                    createdBy: "test-teacher",
                    createdAt: Date(),
                    dueDate: Date().addingTimeInterval(86400)
                )
                
                // Create a test submission with correct answers
                let submission = StudentSubmission(
                    exerciseId: testExercise.id,
                    studentId: "67d1cf038f749f11ece01eef",
                    answers: [
                        QuestionSubmission(
                            questionId: UUID(uuidString: "BF0ECFA5-BBAD-4CAF-90A1-47398F829377")!,
                            answer: "1", // Student answered "4" (index 1), which is correct
                            isCorrect: false, // Backend incorrectly marked as false
                            pointsEarned: 0
                        ),
                        QuestionSubmission(
                            questionId: UUID(uuidString: "FB9BD9A4-B6F4-4658-BA00-46ED8616233A")!,
                            answer: "1", // Student answered "9" (index 1), which is correct
                            isCorrect: false, // Backend incorrectly marked as false
                            pointsEarned: 0
                        ),
                        QuestionSubmission(
                            questionId: UUID(uuidString: "47940621-E4AC-492E-8F08-C775B1DB8C64")!,
                            answer: "I used addition and multiplication to solve these problems.",
                            isCorrect: nil,
                            pointsEarned: nil
                        )
                    ],
                    startTime: Date(),
                    endTime: Date(),
                    score: 0.0, // Backend calculated 0% but should be 50%
                    earnedPoints: 0,
                    totalPoints: 20,
                    gradingStatus: "pending"
                )
                
                // Test the local score calculation
                let localScore = exerciseViewModel.calculateScore(for: submission, exercise: testExercise)
                let expectedScore = 50.0 // 10 points earned out of 20 total = 50%
                
                await MainActor.run {
                    if abs(localScore - expectedScore) < 0.1 {
                        scoreCalculationTestStatus = .success
                        scoreCalculationTestMessage = "✅ Score calculation fix working! Local calculation: \(String(format: "%.1f", localScore))% (expected: \(String(format: "%.1f", expectedScore))%)"
                    } else {
                        scoreCalculationTestStatus = .failure
                        scoreCalculationTestMessage = "❌ Score calculation still incorrect. Local: \(String(format: "%.1f", localScore))%, Expected: \(String(format: "%.1f", expectedScore))%"
                    }
                }
            } catch {
                await MainActor.run {
                    scoreCalculationTestStatus = .failure
                    scoreCalculationTestMessage = "❌ Error testing score calculation: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // *** NEW: Test multiple question grading API ***
    private func testMultipleQuestionGrading() {
        multipleQuestionGradingTestStatus = .running
        multipleQuestionGradingTestMessage = "Testing multiple question grading API..."
        
        Task {
            do {
                guard let firstSubmission = exerciseViewModel.studentSubmissions.first else {
                    await MainActor.run {
                        multipleQuestionGradingTestStatus = .failure
                        multipleQuestionGradingTestMessage = "❌ No submission available for testing. Submit one first."
                    }
                    return
                }
                
                // Create test grades for the new API format
                let testGrades = [
                    QuestionGrade(
                        questionId: UUID(uuidString: "BF0ECFA5-BBAD-4CAF-90A1-47398F829377")!,
                        pointsEarned: 8.0,
                        isCorrect: true,
                        feedback: "Excellent explanation!"
                    ),
                    QuestionGrade(
                        questionId: UUID(uuidString: "FB9BD9A4-B6F4-4658-BA00-46ED8616233A")!,
                        pointsEarned: 7.0,
                        isCorrect: false,
                        feedback: "Check your math on this one."
                    )
                ]
                
                if let mongoId = firstSubmission.mongoId {
                    try await exerciseViewModel.gradeSubmissionWithMultipleQuestions(
                        mongoId: mongoId,
                        grades: testGrades,
                        feedback: "Great effort overall! This is a test of the new multiple question grading API.",
                        gradingMethod: "manual"
                    )
                    
                    await MainActor.run {
                        multipleQuestionGradingTestStatus = .success
                        multipleQuestionGradingTestMessage = "✅ Multiple question grading API test successful! Graded \(testGrades.count) questions via PUT /api/submission?submission_id=\(mongoId)"
                    }
                } else {
                    await MainActor.run {
                        multipleQuestionGradingTestStatus = .failure
                        multipleQuestionGradingTestMessage = "❌ Failed to test multiple question grading: MongoDB ObjectId is missing"
                    }
                }
            } catch {
                await MainActor.run {
                    multipleQuestionGradingTestStatus = .failure
                    multipleQuestionGradingTestMessage = "❌ Failed to test multiple question grading: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // Old calculation method for comparison
    private func calculateScoreOldMethod(submission: StudentSubmission, exercise: Exercise) -> Double {
        var totalPoints = 0.0
        var earnedPoints = 0.0
        
        for question in exercise.questions {
            totalPoints += Double(question.points)
            if let answer = submission.answers.first(where: { $0.questionId == question.id }) {
                if answer.isCorrect == true {
                    earnedPoints += Double(question.points)
                }
            }
        }
        
        return totalPoints > 0 ? (earnedPoints / totalPoints) * 100 : 0
    }
    
    // Test specific submission analysis
    private func testSpecificSubmissionAnalysis() {
        submissionAnalysisStatus = .running
        submissionAnalysisMessage = "Analyzing specific submission data..."
        
        Task {
            do {
                // The specific exercise ID from the user's data
                let exerciseId = UUID(uuidString: "6BBC7580-C087-4614-9E47-390F3CAA8353")!
                
                // Fetch the exercise from backend to get correct answers
                let exercise = try await exerciseViewModel.getExercise(by: exerciseId)
                
                // Create the submission from user's data
                let submission = StudentSubmission(
                    exerciseId: exerciseId,
                    studentId: "67d1cf038f749f11ece01eef",
                    answers: [
                        QuestionSubmission(
                            questionId: UUID(uuidString: "BF0ECFA5-BBAD-4CAF-90A1-47398F829377")!,
                            answer: "1",
                            isCorrect: false,
                            pointsEarned: 0
                        ),
                        QuestionSubmission(
                            questionId: UUID(uuidString: "FB9BD9A4-B6F4-4658-BA00-46ED8616233A")!,
                            answer: "1",
                            isCorrect: false,
                            pointsEarned: 0
                        ),
                        QuestionSubmission(
                            questionId: UUID(uuidString: "47940621-E4AC-492E-8F08-C775B1DB8C64")!,
                            answer: "2",
                            isCorrect: false,
                            pointsEarned: 0
                        ),
                        QuestionSubmission(
                            questionId: UUID(uuidString: "10C4A2A9-1201-4882-AB6B-8E473F059D7F")!,
                            answer: "1",
                            isCorrect: false,
                            pointsEarned: 0
                        ),
                        QuestionSubmission(
                            questionId: UUID(uuidString: "319556CC-89E7-435F-AE20-C55C2F5B73E2")!,
                            answer: "2",
                            isCorrect: false,
                            pointsEarned: 0
                        )
                    ],
                    startTime: Date(),
                    endTime: Date(),
                    score: 0.0,
                    earnedPoints: 0,
                    totalPoints: 20,
                    gradingStatus: "completed"
                )
                
                // Calculate scores using different methods
                let oldScore = calculateScoreOldMethod(submission: submission, exercise: exercise)
                let newScore = exerciseViewModel.calculateScore(for: submission, exercise: exercise)
                
                // Analyze each answer
                var analysis = "📊 Submission Analysis for Exercise '\(exercise.title)':\n\n"
                analysis += "Student Answers vs Correct Answers:\n"
                
                var correctCount = 0
                for (index, answer) in submission.answers.enumerated() {
                    if index < exercise.questions.count {
                        let question = exercise.questions[index]
                        let studentAnswerIndex = Int(answer.answer) ?? -1
                        let isCorrect = studentAnswerIndex == question.correctAnswerIndex
                        
                        if isCorrect {
                            correctCount += 1
                        }
                        
                        analysis += "Q\(index + 1): Student chose '\(answer.answer)' (index \(studentAnswerIndex)), "
                        analysis += "Correct is '\(question.correctAnswerIndex)' "
                        analysis += isCorrect ? "✅" : "❌"
                        analysis += "\n"
                    }
                }
                
                let expectedScore = Double(correctCount) / Double(exercise.questions.count) * 100
                
                analysis += "\n📈 Score Calculation Results:\n"
                analysis += "• Old Method (using isCorrect field): \(String(format: "%.1f", oldScore))%\n"
                analysis += "• New Method (comparing with correctAnswerIndex): \(String(format: "%.1f", newScore))%\n"
                analysis += "• Expected Score (manual calculation): \(String(format: "%.1f", expectedScore))%\n"
                analysis += "• Correct Answers: \(correctCount)/\(exercise.questions.count)\n\n"
                
                analysis += "🔍 Analysis:\n"
                if oldScore != newScore {
                    analysis += "✅ The fix is working! Old method shows \(String(format: "%.1f", oldScore))% but new method shows \(String(format: "%.1f", newScore))%\n"
                    analysis += "✅ Backend incorrectly set all isCorrect to false\n"
                    analysis += "✅ New method correctly calculates the actual score\n"
                } else {
                    analysis += "⚠️ Both methods show the same score - this might indicate the student actually got 0% correct\n"
                }
                
                await MainActor.run {
                    submissionAnalysisStatus = .success
                    submissionAnalysisMessage = analysis
                }
                
            } catch {
                await MainActor.run {
                    submissionAnalysisStatus = .failure
                    submissionAnalysisMessage = "❌ Failed to analyze submission: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // *** NEW: Test for user's specific data ***
    private func testUserSpecificData() {
        userDataTestStatus = .running
        userDataTestMessage = "Testing user's actual submission data..."
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            // Recreate the exact exercise from the user's data
            let exercise = Exercise(
                id: UUID(uuidString: "34B15F96-8F8F-4BD2-8668-4E9C52DDC27D")!,
                title: "Testing123",
                topic: "Approximation Values and Numerical Estimation",
                subtopic: "Numerical Estimation",
                classroomIds: ["685a6b003ed361d2fc0d00de"],
                questions: [
                    Question(
                        id: UUID(uuidString: "0715A718-70B5-41B1-9D89-EF9627764C2B")!,
                        questionText: "Estimate the value of $3.14 \\times 19.8$.",
                        questionType: .multipleChoice,
                        options: ["30", "60", "90", "120"],
                        correctAnswerIndex: 1, // Correct answer is "60" (index 1)
                        points: 4
                    ),
                    Question(
                        id: UUID(uuidString: "1F014358-41F5-4195-A8AB-062E5D248251")!,
                        questionText: "A store sells apples for $0.48 per pound. Approximately how much would 5.2 pounds of apples cost?",
                        questionType: .multipleChoice,
                        options: ["$1.50", "$2.00", "$2.50", "$3.00"],
                        correctAnswerIndex: 2, // Correct answer is "$2.50" (index 2)
                        points: 4
                    ),
                    Question(
                        id: UUID(uuidString: "A991757A-472F-4F23-842D-3339E736B2B6")!,
                        questionText: "Estimate the sum of $1987 + 3052 + 4991$.",
                        questionType: .multipleChoice,
                        options: ["8000", "9000", "10000", "11000"],
                        correctAnswerIndex: 2, // Correct answer is "10000" (index 2)
                        points: 4
                    ),
                    Question(
                        id: UUID(uuidString: "7F968079-9AAA-4556-8D03-BF19F3BE6213")!,
                        questionText: "A car travels at an average speed of 58 miles per hour. Approximately how far will it travel in 3.9 hours?",
                        questionType: .multipleChoice,
                        options: ["180 miles", "200 miles", "220 miles", "240 miles"],
                        correctAnswerIndex: 2, // Correct answer is "220 miles" (index 2)
                        points: 4
                    ),
                    Question(
                        id: UUID(uuidString: "2DFED575-02FE-4D52-8311-80BEB8E68FB7")!,
                        questionText: "Estimate the result of $49.7 \\div 5.1$.",
                        questionType: .multipleChoice,
                        options: ["5", "8", "10", "12"],
                        correctAnswerIndex: 2, // Correct answer is "10" (index 2)
                        points: 4
                    )
                ],
                createdBy: "683e6d0e8877f9fff65e7d99",
                createdAt: Date(),
                dueDate: Date()
            )
            
            // Recreate the exact submission from the user's data (backend response)
            let submission = StudentSubmission(
                exerciseId: exercise.id,
                studentId: "67d1cf038f749f11ece01eef",
                answers: [
                    QuestionSubmission(
                        questionId: UUID(uuidString: "0715A718-70B5-41B1-9D89-EF9627764C2B")!,
                        answer: "1", // Student answered index 1 (correct!)
                        isCorrect: false, // Backend incorrectly marked as false
                        pointsEarned: 0
                    ),
                    QuestionSubmission(
                        questionId: UUID(uuidString: "1F014358-41F5-4195-A8AB-062E5D248251")!,
                        answer: "2", // Student answered index 2 (correct!)
                        isCorrect: false, // Backend incorrectly marked as false
                        pointsEarned: 0
                    ),
                    QuestionSubmission(
                        questionId: UUID(uuidString: "A991757A-472F-4F23-842D-3339E736B2B6")!,
                        answer: "2", // Student answered index 2 (correct!)
                        isCorrect: false, // Backend incorrectly marked as false
                        pointsEarned: 0
                    ),
                    QuestionSubmission(
                        questionId: UUID(uuidString: "7F968079-9AAA-4556-8D03-BF19F3BE6213")!,
                        answer: "2", // Student answered index 2 (correct!)
                        isCorrect: false, // Backend incorrectly marked as false
                        pointsEarned: 0
                    ),
                    QuestionSubmission(
                        questionId: UUID(uuidString: "2DFED575-02FE-4D52-8311-80BEB8E68FB7")!,
                        answer: "2", // Student answered index 2 (correct!)
                        isCorrect: false, // Backend incorrectly marked as false
                        pointsEarned: 0
                    )
                ],
                startTime: Date(),
                endTime: Date(),
                score: 0.0, // Backend incorrectly calculated 0%
                earnedPoints: 0, // Backend incorrectly calculated 0
                totalPoints: 20,
                gradingStatus: "completed"
            )
            
            // Calculate scores using different methods
            let backendScore = submission.score ?? 0.0
            let clientScore = exerciseViewModel.calculateScore(for: submission, exercise: exercise)
            
            // Manually verify each answer
            var analysis = "🔍 DETAILED ANALYSIS OF USER'S SUBMISSION:\n\n"
            analysis += "Backend Response Summary:\n"
            analysis += "• multipleChoiceCorrect: 0 (WRONG - should be 5)\n"
            analysis += "• score: 0.0 (WRONG - should be 100.0)\n"
            analysis += "• All isCorrect fields set to false (WRONG)\n\n"
            
            analysis += "Question-by-Question Analysis:\n"
            
            var correctCount = 0
            for (index, question) in exercise.questions.enumerated() {
                let answer = submission.answers[index]
                let studentAnswerIndex = Int(answer.answer) ?? -1
                let isActuallyCorrect = studentAnswerIndex == question.correctAnswerIndex
                
                if isActuallyCorrect {
                    correctCount += 1
                }
                
                analysis += "Q\(index + 1): \(question.questionText)\n"
                analysis += "  Options: \(question.options ?? [])\n"
                analysis += "  Student chose: \"\(question.options?[studentAnswerIndex] ?? "Invalid")\" (index \(studentAnswerIndex))\n"
                analysis += "  Correct answer: \"\(question.options?[question.correctAnswerIndex] ?? "Invalid")\" (index \(question.correctAnswerIndex))\n"
                analysis += "  Backend marked as: \(answer.isCorrect == true ? "CORRECT" : "INCORRECT")\n"
                analysis += "  Actually: \(isActuallyCorrect ? "CORRECT ✅" : "INCORRECT ❌")\n\n"
            }
            
            analysis += "📊 SCORE COMPARISON:\n"
            analysis += "• Backend Score: \(String(format: "%.1f", backendScore))%\n"
            analysis += "• Client Calculated Score: \(String(format: "%.1f", clientScore))%\n"
            analysis += "• Actual Correct Answers: \(correctCount)/\(exercise.questions.count) = \(String(format: "%.1f", Double(correctCount)/Double(exercise.questions.count)*100))%\n\n"
            
            analysis += "🔍 ROOT CAUSE ANALYSIS:\n"
            if correctCount == 5 && backendScore == 0.0 {
                analysis += "✅ CONFIRMED: This is a backend bug!\n"
                analysis += "• Student got ALL 5 questions correct (100%)\n"
                analysis += "• Backend incorrectly marked all as wrong (0%)\n"
                analysis += "• Client-side calculateScore() correctly shows 100%\n"
                analysis += "• The iOS app's score calculation logic is working correctly\n\n"
                analysis += "🔧 BACKEND ISSUE:\n"
                analysis += "The backend grading logic is not properly comparing student answers\n"
                analysis += "with the correctAnswerIndex values. It's marking all answers as incorrect\n"
                analysis += "even when they match the correct answer indices exactly."
            } else {
                analysis += "⚠️ Unexpected result - needs further investigation"
            }
            
            userDataTestStatus = .success
            userDataTestMessage = analysis
        }
    }
    
    private func runAllTests() {
        checkAuthenticationStatus()
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            pingServer()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            testJSONProcessing()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            testExerciseRetrieval()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
            testExerciseCreation()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            testDateFormat()
        }
        // *** NEW: CRUD Tests ***
        DispatchQueue.main.asyncAfter(deadline: .now() + 6) {
            testStudentExercises()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 7) {
            testAllStudentSubmissions()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 8) {
            testExerciseUpdate()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 9) {
            testExerciseSubmission()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 10) {
            testSubmissionGrading()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 11) {
            testExerciseDelete()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 12) {
            testScoreCalculationFix()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 13) {
            testSpecificSubmissionAnalysis()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 14) {
            testUserSpecificData()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 15) {
            testMultipleQuestionGrading()
        }
    }
    
    private func runCRUDTests() {
        // Run only the CRUD tests for the new backend
        checkAuthenticationStatus()
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            testExerciseCreation()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            testStudentExercises()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            testAllStudentSubmissions()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
            testExerciseUpdate()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
            testExerciseSubmission()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 6) {
            testSubmissionGrading()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 7) {
            testExerciseDelete()
        }
    }
    
    private func resetAllTests() {
        // Reset all test states
        serverPingStatus = .notStarted
        exerciseRetrievalStatus = .notStarted
        exerciseCreationStatus = .notStarted
        jsonTestStatus = .notStarted
        authCheckStatus = .notStarted
        dateFormatTestStatus = .notStarted
        updateExerciseStatus = .notStarted
        deleteExerciseStatus = .notStarted
        studentExerciseStatus = .notStarted
        submissionTestStatus = .notStarted
        gradingTestStatus = .notStarted
        
        // Clear all messages
        serverPingMessage = ""
        exerciseRetrievalMessage = ""
        exerciseCreationMessage = ""
        jsonTestMessage = ""
        authCheckMessage = ""
        dateFormatTestMessage = ""
        updateExerciseMessage = ""
        deleteExerciseMessage = ""
        studentExerciseMessage = ""
        submissionTestMessage = ""
        gradingTestMessage = ""
        
        // Clear test exercise ID
        testExerciseId = nil
        
        // Clear score calculation test message
        scoreCalculationTestMessage = ""
        
        // Clear submission analysis message
        submissionAnalysisMessage = ""
        
        // Clear user-specific data test message
        userDataTestMessage = ""

        // Clear multiple question grading test message
        multipleQuestionGradingTestMessage = ""
    }
}

struct DiagnosticRow: View {
    let title: String
    let status: ExerciseDiagnosticView.TestStatus
    let message: String
    let action: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: status.icon)
                    .foregroundColor(status.color)
                    .frame(width: 20)
                
                Text(title)
                    .font(.headline)
                    .fontWeight(.medium)
                
                Spacer()
                
                Button("Test") {
                    action()
                }
                .buttonStyle(.bordered)
                .disabled(status == .running)
            }
            
            if !message.isEmpty {
                Text(message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.leading, 28)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }
}

#Preview {
    ExerciseDiagnosticView()
} 