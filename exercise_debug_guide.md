# Exercise Data Debug Logging Guide

## Overview
I've added comprehensive logging and debug messages throughout the exercise data flow to help you track how data is handled when sending to the backend or MongoDB. This will help you identify where issues occur in the data transmission process.

## 🆕 NEW: Enhanced Error Messages
The error handling has been improved to show **exact HTTP status codes and server responses** instead of generic error messages. Now you'll see detailed errors like:
- `Server Error (500): Database connection failed`
- `Server Error (404): Exercise not found`
- `Server Error (400): Invalid JSON format`

## 🆕 NEW: Diagnostic Tools
- **ExerciseDiagnosticView**: A comprehensive diagnostic tool that tests all aspects of exercise data connectivity
- **Server Ping Test**: Quick connectivity test to see if the server is reachable
- **Performance Logging**: Tracks how long operations take to complete

## What's Been Added

### 1. ExerciseLogger Class
- **Location**: `FunMA/Models/ExerciseModel.swift`
- **Purpose**: Centralized logging utility with different log levels (info, debug, error, fault)
- **Features**:
  - Structured logging with file names and line numbers
  - Color-coded console output with emojis for easy identification
  - Specialized methods for logging exercise data, submission data, and network requests/responses

### 2. Enhanced ExerciseViewModel Methods
All network operations now include detailed logging:

#### `getAllExercises()`
- Logs the URL being fetched from
- Logs network request details
- Logs response status codes and data
- Logs successful decoding of exercises
- Logs any errors that occur

#### `createExercise()`
- Logs complete exercise data before sending
- Logs JSON encoding process
- Logs network request with headers and body
- Logs server response and any errors
- Logs successful creation or updates
- **🆕 Enhanced error messages with exact HTTP status codes**

#### `submitSubmission()`
- Logs complete submission data before sending
- Logs JSON encoding process
- Logs network request details
- Logs server response and any errors
- Logs successful submission
- **🆕 Enhanced error messages with exact HTTP status codes**

#### `getExercises(for grade:)`
- Logs grade-specific requests
- Logs network operations and responses
- Logs successful data retrieval

#### `getStudentSubmissions(for exerciseId:)`
- Logs submission retrieval requests
- Logs network operations and responses
- Logs successful data retrieval

#### `gradeSubmission()`
- Logs grading data before sending
- Logs network operations and responses
- Logs successful grading

#### **🆕 `pingServer()`**
- Quick connectivity test using HEAD requests
- Measures response time
- Determines if server is reachable

### 3. Enhanced View Logging
- **CreateExerciseView**: Logs when teachers create new exercises
- **EditExerciseView**: Logs when teachers update existing exercises  
- **ExerciseTakingView**: Logs when students submit exercise answers
- **🆕 InClassExerciseView**: Performance logging for exercise loading

### 4. Test Functions
- **`testLogging()`**: Tests all logging functionality
- **`testAPIConnectivity()`**: Tests API connectivity
- **🆕 `pingServer()`**: Quick server reachability test

### 5. Debug Tools
- **Debug Button** in `InClassExerciseView` for easy testing
- **🆕 ExerciseDiagnosticView**: Comprehensive diagnostic interface

## How to Use the Debug System

### 1. View Logs in Xcode Console
1. Run your app in Xcode
2. Open the Debug Console (View → Debug Area → Activate Console)
3. Look for logs with these prefixes:
   - `📝` - General information logs
   - `🔍` - Debug logs
   - `❌` - Error logs
   - `🚨` - Fault logs
   - `🔧` - Debug button logs
   - **🆕 `🕐`** - Performance timing logs

### 2. Use the New Diagnostic View
1. Add `ExerciseDiagnosticView` to your app's navigation
2. Tap "Run Diagnostics" to test all connectivity aspects
3. View comprehensive test results with status indicators

### 3. Test the Logging System (Method 1 - Debug Button)
1. Navigate to the Exercise view in your app
2. Tap the "Debug" button (orange button in the top right)
3. Check the console for test output

### 4. Monitor Real Operations
When you perform these actions, detailed logs will appear:

#### Creating an Exercise (Teacher)
```
📝 CreateExerciseView: Starting createExercise
📝 CreateExerciseView: Title: Math Quiz
📝 CreateExerciseView: Topic: Algebra
📝 CreateExerciseView: Number of Questions: 5
ℹ️ INFO: [ExerciseModel.swift:123] createExercise: Starting createExercise operation
ℹ️ INFO: [ExerciseModel.swift:124] createExercise: === EXERCISE DATA LOG ===
...
```

#### Submitting an Exercise (Student)
```
📝 ExerciseTakingView: Starting submitExercise
📝 ExerciseTakingView: Exercise ID: 123e4567-e89b-12d3-a456-426614174000
📝 ExerciseTakingView: Student ID: student-123
📝 ExerciseTakingView: Number of answers: 3
ℹ️ INFO: [ExerciseModel.swift:234] submitSubmission: Starting submitSubmission operation
ℹ️ INFO: [ExerciseModel.swift:235] submitSubmission: === SUBMISSION DATA LOG ===
...
```

#### **🆕 Performance Logging**
```
🕐 InClassExerciseView: Starting to load exercises at 2024-01-20 10:30:00
✅ InClassExerciseView: Successfully loaded exercises in 2.34 seconds
```

#### Network Operations
```
ℹ️ INFO: [ExerciseModel.swift:156] createExercise: === NETWORK REQUEST LOG ===
ℹ️ INFO: [ExerciseModel.swift:157] createExercise: URL: http://ec2-54-179-124-122.ap-southeast-1.compute.amazonaws.com:5001/api/exercises
ℹ️ INFO: [ExerciseModel.swift:158] createExercise: Method: POST
🔍 DEBUG: [ExerciseModel.swift:162] createExercise: Request Body: {"id":"123e4567-e89b-12d3-a456-426614174000","title":"Math Quiz",...}
```

## Troubleshooting Common Issues

### 1. Network Connection Issues
Look for these logs:
```
❌ ERROR: [ExerciseModel.swift:189] createExercise: createExercise failed with error: The operation couldn't be completed. (NSURLErrorDomain error -1009.)
```

### **🆕 2. Enhanced Server Response Issues**
Now you'll see detailed server errors instead of generic messages:
```
❌ ERROR: [ExerciseModel.swift:175] createExercise: Server returned error status code: 500
❌ ERROR: [ExerciseModel.swift:178] createExercise: Error response body: {"error": "Database connection failed", "details": "MongoDB connection timeout"}
❌ CreateExerciseView: createExercise failed with error: Server Error (500): Database connection failed
```

### 3. Data Encoding Issues
Look for these logs:
```
❌ ERROR: [ExerciseModel.swift:165] createExercise: Failed to encode exercise data: The data couldn't be read because it is missing.
```

### 4. Data Decoding Issues
Look for these logs:
```
❌ ERROR: [ExerciseModel.swift:168] createExercise: Failed to decode created exercise: The data couldn't be read because it is missing.
🔍 DEBUG: [ExerciseModel.swift:169] createExercise: Raw response data: {"error": "Invalid JSON format"}
```

### **🆕 5. Performance Issues**
Check for slow loading times:
```
🕐 InClassExerciseView: Starting to load exercises at 2024-01-20 10:30:00
❌ InClassExerciseView: Failed to load exercises after 15.67 seconds: Server timeout
```

## API Configuration

The current API configuration is in `FunMA/Models/APIConfig.swift`:

```swift
#if DEBUG
    static let baseURL = "http://ec2-54-179-124-122.ap-southeast-1.compute.amazonaws.com:5001/api"
#else
    static let baseURL = "https://api.luminouseducation.com/api/v1"
#endif
```

## Expected API Endpoints

Based on the code, your backend should have these endpoints:

1. `GET /api/exercises` - Get all exercises
2. `GET /api/exercises?grade={grade}` - Get exercises for specific grade
3. `POST /api/exercises` - Create new exercise
4. `GET /api/submissions?exerciseId={id}` - Get submissions for exercise
5. `POST /api/submissions` - Submit student answers
6. `PUT /api/submissions/{id}/grade` - Grade a submission

## **🆕 Quick Diagnostic Steps**

### For Your Current "Failed to create exercise" Error:

1. **Run the improved diagnostics** - The new error messages will show you the exact HTTP status code and server response
2. **Check server connectivity** - Use the ping test to verify the server is reachable
3. **Monitor the exact error** - The enhanced error messages will tell you exactly what the server is saying

### Expected Error Messages You Might See:
- **Server Error (404)**: API endpoint not found on server
- **Server Error (500)**: Internal server error (database issues, etc.)
- **Server Error (400)**: Bad request (invalid JSON, missing fields, etc.)
- **Server Error (401)**: Unauthorized (authentication issues)
- **Server Error (403)**: Forbidden (permission issues)

## Next Steps

1. **Run the app** and check the console for any immediate errors
2. **🆕 Use the ExerciseDiagnosticView** for comprehensive testing
3. **Check the exact HTTP status code** in the new error messages
4. **Test the debug button** to verify logging is working
5. **Try creating an exercise** and watch the detailed logs
6. **Check your backend logs** to see if requests are reaching the server
7. **Verify your MongoDB connection** if requests are reaching the server but failing

The enhanced logging system will now tell you **exactly** what HTTP status code and error message your server is returning, making it much easier to identify and fix the issue! 