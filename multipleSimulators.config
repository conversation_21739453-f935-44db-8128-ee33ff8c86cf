# Multiple Simulators Configuration File
# .app file location based on the scheme you are trying to use. No `/` in the end
app_location=~/Library/Developer/Xcode/DerivedData/FunMA-*/Build/Products/Debug-iphonesimulator

# the bundle id based on the scheme you are trying to use
bundle_id=com.kic01.Luminous-Education

# list of simulators separated by comma. No spaces allowed between device ids.
# You can get device IDs by running: xcrun simctl list devices
# Common device IDs for testing:
# iPhone 15 Pro: 00008120-000D28C11234567E
# iPhone 15: 00008120-000D28C11234567F
# iPhone 14 Pro: 00008120-000D28C112345680
# iPhone 14: 00008120-000D28C112345681
# iPad Pro (12.9-inch): 00008120-000D28C112345682
# iPad Air: 00008120-000D28C112345683
simulators_list=iPhone 15 Pro,iPhone 15,iPad Pro (12.9-inch) 