#!/bin/bash

# Multiple Simulators Testing Script
# This script installs and launches the app across multiple iOS simulators

# installs and launches the `bundle_id` for the specified simulator device
installAndLaunch() {
    device=$1
    app_path=$2
    app_bundle_id=$3
    
    echo "Installing and launching on device: $device"
    xcrun instruments -w "$device"
    xcrun simctl install $device $app_path
    xcrun simctl launch $device $app_bundle_id &
    echo "Started app on device: $device"
}

# parses the configuration file
source ./multipleSimulators.config

echo "Testing: source $app_location"
path=$(find $app_location -name "FunMA.app" | head -n 1)
echo "Application found at: $path"

if [ -z "$path" ]; then
    echo "Error: FunMA.app not found in $app_location"
    echo "Please build the project first or check the app_location path"
    exit 1
fi

echo "<<<<<<<<<<<<<<<<<<< Initializing all simulators >>>>>>>>>>>>>>>>>>>>"

# iterates over the simulators list and start installation / launching process
IFS=',' read -ra ADDR <<< "$simulators_list"
for device in "${ADDR[@]}"; do
    installAndLaunch $device $path $bundle_id &
done

echo "All simulators have been started. Check each simulator to see your app running!"
echo "Press Ctrl+C to stop all simulators when done testing." 