# Authentication Error 5 Fix Guide

## Problem Analysis

**Error**: `❌ ClassroomViewModel: Failed to load school students: The operation couldn't be completed. (FunMA.APIError error 5.)`

**Root Cause**: APIError case 5 corresponds to `authenticationFailed` in the APIError enum:

```swift
enum APIError: Error {
    case invalidURL           // case 0
    case requestFailed(Error) // case 1  
    case invalidResponse      // case 2
    case decodingFailed(Error)// case 3
    case serverError(Int)     // case 4
    case authenticationFailed // case 5 ← THIS IS THE ERROR
    case accessDenied         // case 6
    case unknown              // case 7
}
```

This error occurs when:
1. Access token is missing or empty
2. Access token has expired
3. Access token is invalid/corrupted
4. Server rejects the authentication

## Solution Steps

### Step 1: Use Enhanced Debug Tools

I've updated the `ClassroomDetailView.swift` to use the enhanced debug version:

```swift
// Changed from:
await viewModel.loadSchoolStudents(for: classroom)

// To:
await viewModel.loadSchoolStudentsWithDebug(for: classroom)
```

### Step 2: Use Developer Debug Tools

I've added comprehensive authentication diagnostic tools to `DeveloperInteractiveToolsView.swift`:

1. **🔍 Run Auth Diagnostics** - Comprehensive authentication state analysis
2. **🔧 Fix Auth Issues** - Automatic authentication repair
3. **🏫 Test School Students Load** - Test the exact failing operation

## How to Fix the Issue

### Method 1: Using Developer Tools (Recommended)

1. Open the app and navigate to **Developer Interactive Tools**
2. Go to the **Debug Tools** tab
3. In the **🔐 Authentication Diagnostics** section:
   - Click **🔍 Run Auth Diagnostics** to see what's wrong
   - Click **🔧 Fix Auth Issues** to automatically fix authentication
   - Click **🏫 Test School Students Load** to verify the fix

### Method 2: Manual Authentication Check

The codebase has built-in authentication debugging. Look for these console logs:

```
🔍 UserManager.getAuthorizationHeader() called
   - accessToken length: 0
   - accessToken isEmpty: true
   - isLoggedIn: true
   - ❌ accessToken is empty, returning nil
```

If you see this, the authentication tokens are missing/expired.

### Method 3: Token Refresh

The system automatically attempts token refresh, but you can force it:

```swift
let refreshSuccess = await UserManager.shared.refreshToken()
```

## Root Causes and Solutions

### Cause 1: Empty Access Token
**Symptoms**: `accessToken.isEmpty = true` but `isLoggedIn = true`
**Solution**: The enhanced debug tools will restore tokens from UserDefaults or refresh them

### Cause 2: Missing School ID
**Symptoms**: User loads but `school_id` is nil
**Solution**: Enhanced tools fetch complete user profile to get `school_id`

### Cause 3: Expired Tokens
**Symptoms**: Authentication fails with 401 status
**Solution**: Automatic token refresh using refresh token

### Cause 4: Invalid Tokens
**Symptoms**: Token exists but server rejects it
**Solution**: Force logout and re-login required

## Enhanced Error Handling

The `loadSchoolStudentsWithDebug` function provides detailed error information:

```swift
❌ DEBUG: Detailed error information:
   - Error type: APIError
   - Error description: Authentication failed
   - APIError case: authenticationFailed
   - 🚨 Authentication failed - this is the issue!
```

## Prevention

To prevent future authentication issues:

1. Always use the enhanced debug versions of API calls during development
2. Monitor console logs for authentication warnings
3. Implement proper token refresh logic
4. Handle authentication failures gracefully

## Testing

After implementing the fix, test with:

1. Load school students functionality in ClassroomDetailView
2. Check console logs for successful authentication
3. Verify tokens are properly restored/refreshed
4. Confirm `school_id` is available for teachers

## Console Log Examples

**Success:**
```
✅ UserManager: Authentication is valid
✅ ClassroomViewModel: Successfully loaded 5 available students from 'Test School'
```

**Before Fix:**
```
❌ ClassroomViewModel: Failed to load school students: Authentication failed
🚨 APIError case: authenticationFailed
```

The enhanced debugging tools will automatically diagnose and fix most authentication issues, making this error much easier to resolve. 