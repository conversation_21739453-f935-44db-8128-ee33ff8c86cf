// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 70;
	objects = {

/* Begin PBXBuildFile section */
		0B0938222DABED21008BFF34 /* InteractiveToolsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B0938212DABED21008BFF34 /* InteractiveToolsModel.swift */; };
		0B0938242DAC130D008BFF34 /* QuizComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B0938232DAC130D008BFF34 /* QuizComponents.swift */; };
		0B24E12A2E0D308F002A18BE /* UserProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B24E1292E0D308F002A18BE /* UserProfileView.swift */; };
		0B2867612E26E3DC005BD290 /* BatchAccountCreationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2867602E26E3DC005BD290 /* BatchAccountCreationView.swift */; };
		0B2867632E273AD9005BD290 /* SchoolCreationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2867622E273AD9005BD290 /* SchoolCreationView.swift */; };
		0B2867662E273C29005BD290 /* SchoolViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2867652E273C29005BD290 /* SchoolViewModel.swift */; };
		0B2867692E273C98005BD290 /* SchoolModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2867682E273C98005BD290 /* SchoolModel.swift */; };
		0B2FD91E2D74711E000EEDB2 /* ChatViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2FD91D2D74711E000EEDB2 /* ChatViewModel.swift */; };
		0B2FD9202D7471BF000EEDB2 /* DeepSeekAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2FD91F2D7471BF000EEDB2 /* DeepSeekAPI.swift */; };
		0B2FD9292D747AC3000EEDB2 /* Message.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2FD9272D747AC3000EEDB2 /* Message.swift */; };
		0B2FD92A2D747AC3000EEDB2 /* MessageFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B2FD9282D747AC3000EEDB2 /* MessageFormatter.swift */; };
		0B334EAE2E0A7C3E00530A76 /* LaTeXProcessor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B334EAD2E0A7C3E00530A76 /* LaTeXProcessor.swift */; };
		0B334EB32E0AB80300530A76 /* MarkdownUI in Frameworks */ = {isa = PBXBuildFile; productRef = 0B334EB22E0AB80300530A76 /* MarkdownUI */; };
		0B334EB72E0AB85300530A76 /* MarkdownMathRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B334EB62E0AB85300530A76 /* MarkdownMathRenderer.swift */; };
		0B334EBA2E0AB89500530A76 /* SwiftMath in Frameworks */ = {isa = PBXBuildFile; productRef = 0B334EB92E0AB89500530A76 /* SwiftMath */; };
		0B40BB952D7E06F5004CC323 /* course.json in Resources */ = {isa = PBXBuildFile; fileRef = 0B40BB932D7E06F5004CC323 /* course.json */; };
		0B509C312C6B2B6B00493EFB /* QuizReviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B509C302C6B2B6B00493EFB /* QuizReviewView.swift */; };
		0B567BB92DFFFB8E0063767D /* DeveloperInteractiveToolsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B567BB82DFFFB8E0063767D /* DeveloperInteractiveToolsView.swift */; };
		0B7519852C5A452000A604CA /* CourseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B7519842C5A452000A604CA /* CourseView.swift */; };
		0B842B392E1E9D9300FD9F5E /* PencilKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 0B842B382E1E9D9300FD9F5E /* PencilKit.framework */; };
		0B89EAC42E2F676900033D21 /* EmptyStateViews.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B89EAC22E2F676900033D21 /* EmptyStateViews.swift */; };
		0B89EAC52E2F676900033D21 /* ExerciseCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B89EAC32E2F676900033D21 /* ExerciseCard.swift */; };
		0B89EAC92E2F677A00033D21 /* CreateExerciseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B89EAC62E2F677A00033D21 /* CreateExerciseView.swift */; };
		0B89EACA2E2F677A00033D21 /* QuestionManagementViews.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B89EAC82E2F677A00033D21 /* QuestionManagementViews.swift */; };
		0B89EACB2E2F677A00033D21 /* ExerciseQuestionsReviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B89EAC72E2F677A00033D21 /* ExerciseQuestionsReviewView.swift */; };
		0B8E32FE2E2A4F6A00703625 /* DraftAreaComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B8E32FD2E2A4F6A00703625 /* DraftAreaComponents.swift */; };
		0B91644B2DEFFEBB000D6854 /* InClassExerciseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B91644A2DEFFEBB000D6854 /* InClassExerciseView.swift */; };
		0B91644D2DEFFEDB000D6854 /* ExerciseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B91644C2DEFFEDB000D6854 /* ExerciseModel.swift */; };
		0B96C3662DF6B67400F90DA9 /* multipleSimulatorsStartup.sh in Resources */ = {isa = PBXBuildFile; fileRef = 0B96C3652DF6B67400F90DA9 /* multipleSimulatorsStartup.sh */; };
		0B96C3682DF6B69100F90DA9 /* multipleSimulators.config in Resources */ = {isa = PBXBuildFile; fileRef = 0B96C3672DF6B69100F90DA9 /* multipleSimulators.config */; };
		0B998F632E03FD5D00D22EE4 /* CurriculumModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B998F622E03FD5D00D22EE4 /* CurriculumModel.swift */; };
		0B998F652E0403D800D22EE4 /* curriculum.json in Resources */ = {isa = PBXBuildFile; fileRef = 0B998F642E0403D800D22EE4 /* curriculum.json */; };
		0B998F672E0A652200D22EE4 /* FlippedClassroomResourceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B998F662E0A652200D22EE4 /* FlippedClassroomResourceView.swift */; };
		0B998FEE2E0A991B00D22EE4 /* ClassroomModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B998FED2E0A991B00D22EE4 /* ClassroomModel.swift */; };
		0B998FF22E0A993800D22EE4 /* ClassroomDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B998FF02E0A993800D22EE4 /* ClassroomDetailView.swift */; };
		0B998FF32E0A993800D22EE4 /* (null) in Sources */ = {isa = PBXBuildFile; };
		0B998FF42E0A993800D22EE4 /* ClassroomManagementView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B998FF12E0A993800D22EE4 /* ClassroomManagementView.swift */; };
		0B998FFA2E0A99EB00D22EE4 /* ClassroomViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B998FF92E0A99EB00D22EE4 /* ClassroomViewModel.swift */; };
		0B9990DF2E1BA392004D99B9 /* LaTeXTextEditor.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9990DE2E1BA392004D99B9 /* LaTeXTextEditor.swift */; };
		0B9990E02E1BA392004D99B9 /* LaTeXKeyboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9990DD2E1BA392004D99B9 /* LaTeXKeyboardView.swift */; };
		0B9990E22E1BCA00004D99B9 /* AddStudentToClassroomView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9990E12E1BCA00004D99B9 /* AddStudentToClassroomView.swift */; };
		0B9EF3D82C57F49B0075159A /* FunMA.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9EF3D72C57F49B0075159A /* FunMA.swift */; };
		0B9EF3DA2C57F49B0075159A /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9EF3D92C57F49B0075159A /* ContentView.swift */; };
		0B9EF3DC2C57F49D0075159A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0B9EF3DB2C57F49D0075159A /* Assets.xcassets */; };
		0B9EF3E02C57F49D0075159A /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 0B9EF3DF2C57F49D0075159A /* Preview Assets.xcassets */; };
		0B9EF3E72C583A100075159A /* DashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9EF3E62C583A100075159A /* DashboardView.swift */; };
		0B9EF3EA2C583B210075159A /* SideBarView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0B9EF3E92C583B210075159A /* SideBarView.swift */; };
		0BA75F8A2D68C377002EF870 /* InteractiveToolsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BA75F892D68C377002EF870 /* InteractiveToolsView.swift */; };
		0BAF67642E20F4900033B607 /* LaTeXTextRenderer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BAF67632E20F4900033B607 /* LaTeXTextRenderer.swift */; };
		0BBE259D2C904540007BFBB6 /* CourseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BBE259C2C904540007BFBB6 /* CourseModel.swift */; };
		0BBE25A32C904880007BFBB6 /* CourseViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BBE25A22C904880007BFBB6 /* CourseViewModel.swift */; };
		0BC19FAE2D80AC8000D3FAC6 /* APIService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC19FA82D80AC8000D3FAC6 /* APIService.swift */; };
		0BC19FB02D80ADCC00D3FAC6 /* APIConfig.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC19FAF2D80ADCC00D3FAC6 /* APIConfig.swift */; };
		0BC22BAF2C5BD29300BFF3DB /* VideoView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC22BAE2C5BD29300BFF3DB /* VideoView.swift */; };
		0BC22BB12C5BE5C300BFF3DB /* QuizBeginView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC22BB02C5BE5C300BFF3DB /* QuizBeginView.swift */; };
		0BC89BB82C5D06A300604096 /* QuizView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC89BB72C5D06A300604096 /* QuizView.swift */; };
		0BC9BB3C2DA2B42A00A5EEB0 /* VideoQuizOverlayView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC9BB3B2DA2B42A00A5EEB0 /* VideoQuizOverlayView.swift */; };
		0BC9BB3E2DA2BADD00A5EEB0 /* VideoTimelineIndicator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BC9BB3D2DA2BADD00A5EEB0 /* VideoTimelineIndicator.swift */; };
		0BCFA6402D7ECBC3002A9721 /* UserManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BCFA63F2D7ECBC3002A9721 /* UserManager.swift */; };
		0BCFA6422D7ECEDB002A9721 /* userProgress.json in Resources */ = {isa = PBXBuildFile; fileRef = 0BCFA6412D7ECEDB002A9721 /* userProgress.json */; };
		0BDA2FD12E1FF83D001EDA06 /* ExerciseResponseView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BDA2FD02E1FF83D001EDA06 /* ExerciseResponseView.swift */; };
		0BDEBB2A2DA2A25F00872B90 /* VideoQuizModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BDEBB292DA2A25F00872B90 /* VideoQuizModel.swift */; };
		0BE65ACF2E05352900AFA325 /* ChatMessage.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE65ACE2E05352900AFA325 /* ChatMessage.swift */; };
		0BE65AD02E05352900AFA325 /* AnythingLLMService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE65ACD2E05352900AFA325 /* AnythingLLMService.swift */; };
		0BE65AD42E0546A600AFA325 /* FileUploadManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE65AD32E0546A600AFA325 /* FileUploadManager.swift */; };
		0BE7E6B52D82F03C00774BEE /* EnrolledCoursesView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE7E6B32D82F03C00774BEE /* EnrolledCoursesView.swift */; };
		0BE7E6B62D82F03C00774BEE /* MarketplaceView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE7E6B42D82F03C00774BEE /* MarketplaceView.swift */; };
		0BE7E6B82D83219200774BEE /* MarketplaceViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE7E6B72D83219200774BEE /* MarketplaceViewModel.swift */; };
		0BE7E6BA2D8346FD00774BEE /* CourseDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BE7E6B92D8346FD00774BEE /* CourseDetailView.swift */; };
		0BED43892C64BC00004F84C1 /* QuizSummaryView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BED43882C64BC00004F84C1 /* QuizSummaryView.swift */; };
		0BEF37882DEEA3CC00C3F1CE /* TeacherDashboardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BEF37862DEEA3CC00C3F1CE /* TeacherDashboardView.swift */; };
		0BEF378A2DEEAB9100C3F1CE /* GameRoomCreationView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BEF37892DEEAB9100C3F1CE /* GameRoomCreationView.swift */; };
		0BEF378C2DEEABC600C3F1CE /* GameRoom.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BEF378B2DEEABC600C3F1CE /* GameRoom.swift */; };
		0BF292332E25037E0084426D /* ComingSoonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BF292322E25037E0084426D /* ComingSoonView.swift */; };
		0BFF7F3A2E2A330D00A3DE03 /* CreditAdjustmentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 0BFF7F392E2A330D00A3DE03 /* CreditAdjustmentView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0B0938212DABED21008BFF34 /* InteractiveToolsModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InteractiveToolsModel.swift; sourceTree = "<group>"; };
		0B0938232DAC130D008BFF34 /* QuizComponents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuizComponents.swift; sourceTree = "<group>"; };
		0B24E1292E0D308F002A18BE /* UserProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserProfileView.swift; sourceTree = "<group>"; };
		0B2867602E26E3DC005BD290 /* BatchAccountCreationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BatchAccountCreationView.swift; sourceTree = "<group>"; };
		0B2867622E273AD9005BD290 /* SchoolCreationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SchoolCreationView.swift; sourceTree = "<group>"; };
		0B2867652E273C29005BD290 /* SchoolViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SchoolViewModel.swift; sourceTree = "<group>"; };
		0B2867682E273C98005BD290 /* SchoolModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SchoolModel.swift; sourceTree = "<group>"; };
		0B2FD91D2D74711E000EEDB2 /* ChatViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatViewModel.swift; sourceTree = "<group>"; xcLanguageSpecificationIdentifier = xcode.lang.xcfilelist; };
		0B2FD91F2D7471BF000EEDB2 /* DeepSeekAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeepSeekAPI.swift; sourceTree = "<group>"; };
		0B2FD9272D747AC3000EEDB2 /* Message.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Message.swift; sourceTree = "<group>"; };
		0B2FD9282D747AC3000EEDB2 /* MessageFormatter.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MessageFormatter.swift; sourceTree = "<group>"; };
		0B334EAD2E0A7C3E00530A76 /* LaTeXProcessor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaTeXProcessor.swift; sourceTree = "<group>"; };
		0B334EB62E0AB85300530A76 /* MarkdownMathRenderer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarkdownMathRenderer.swift; sourceTree = "<group>"; };
		0B40BB932D7E06F5004CC323 /* course.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = course.json; sourceTree = "<group>"; };
		0B509C302C6B2B6B00493EFB /* QuizReviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuizReviewView.swift; sourceTree = "<group>"; };
		0B567BB82DFFFB8E0063767D /* DeveloperInteractiveToolsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeveloperInteractiveToolsView.swift; sourceTree = "<group>"; };
		0B7519842C5A452000A604CA /* CourseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CourseView.swift; sourceTree = "<group>"; };
		0B842B382E1E9D9300FD9F5E /* PencilKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = PencilKit.framework; path = Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk/System/Library/Frameworks/PencilKit.framework; sourceTree = DEVELOPER_DIR; };
		0B89EAC22E2F676900033D21 /* EmptyStateViews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EmptyStateViews.swift; sourceTree = "<group>"; };
		0B89EAC32E2F676900033D21 /* ExerciseCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExerciseCard.swift; sourceTree = "<group>"; };
		0B89EAC62E2F677A00033D21 /* CreateExerciseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateExerciseView.swift; sourceTree = "<group>"; };
		0B89EAC72E2F677A00033D21 /* ExerciseQuestionsReviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExerciseQuestionsReviewView.swift; sourceTree = "<group>"; };
		0B89EAC82E2F677A00033D21 /* QuestionManagementViews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuestionManagementViews.swift; sourceTree = "<group>"; };
		0B8E32FD2E2A4F6A00703625 /* DraftAreaComponents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DraftAreaComponents.swift; sourceTree = "<group>"; };
		0B91644A2DEFFEBB000D6854 /* InClassExerciseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InClassExerciseView.swift; sourceTree = "<group>"; };
		0B91644C2DEFFEDB000D6854 /* ExerciseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExerciseModel.swift; sourceTree = "<group>"; };
		0B96C3652DF6B67400F90DA9 /* multipleSimulatorsStartup.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = multipleSimulatorsStartup.sh; sourceTree = "<group>"; };
		0B96C3672DF6B69100F90DA9 /* multipleSimulators.config */ = {isa = PBXFileReference; lastKnownFileType = text; path = multipleSimulators.config; sourceTree = "<group>"; };
		0B998F622E03FD5D00D22EE4 /* CurriculumModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CurriculumModel.swift; sourceTree = "<group>"; };
		0B998F642E0403D800D22EE4 /* curriculum.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = curriculum.json; sourceTree = "<group>"; };
		0B998F662E0A652200D22EE4 /* FlippedClassroomResourceView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FlippedClassroomResourceView.swift; sourceTree = "<group>"; };
		0B998FED2E0A991B00D22EE4 /* ClassroomModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassroomModel.swift; sourceTree = "<group>"; };
		0B998FF02E0A993800D22EE4 /* ClassroomDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassroomDetailView.swift; sourceTree = "<group>"; };
		0B998FF12E0A993800D22EE4 /* ClassroomManagementView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassroomManagementView.swift; sourceTree = "<group>"; };
		0B998FF92E0A99EB00D22EE4 /* ClassroomViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ClassroomViewModel.swift; sourceTree = "<group>"; };
		0B9990DD2E1BA392004D99B9 /* LaTeXKeyboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaTeXKeyboardView.swift; sourceTree = "<group>"; };
		0B9990DE2E1BA392004D99B9 /* LaTeXTextEditor.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaTeXTextEditor.swift; sourceTree = "<group>"; };
		0B9990E12E1BCA00004D99B9 /* AddStudentToClassroomView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddStudentToClassroomView.swift; sourceTree = "<group>"; };
		0B9EF3D42C57F49B0075159A /* FunMA.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = FunMA.app; sourceTree = BUILT_PRODUCTS_DIR; };
		0B9EF3D72C57F49B0075159A /* FunMA.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FunMA.swift; sourceTree = "<group>"; };
		0B9EF3D92C57F49B0075159A /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		0B9EF3DB2C57F49D0075159A /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		0B9EF3DD2C57F49D0075159A /* FunMA.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = FunMA.entitlements; sourceTree = "<group>"; };
		0B9EF3DF2C57F49D0075159A /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		0B9EF3E62C583A100075159A /* DashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashboardView.swift; sourceTree = "<group>"; };
		0B9EF3E92C583B210075159A /* SideBarView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SideBarView.swift; sourceTree = "<group>"; };
		0BA75F892D68C377002EF870 /* InteractiveToolsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = InteractiveToolsView.swift; sourceTree = "<group>"; };
		0BA75F942D68CBD8002EF870 /* FunMA-info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "FunMA-info.plist"; sourceTree = SOURCE_ROOT; };
		0BAF67632E20F4900033B607 /* LaTeXTextRenderer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LaTeXTextRenderer.swift; sourceTree = "<group>"; };
		0BBE259C2C904540007BFBB6 /* CourseModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CourseModel.swift; sourceTree = "<group>"; };
		0BBE25A22C904880007BFBB6 /* CourseViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CourseViewModel.swift; sourceTree = "<group>"; };
		0BC19FA82D80AC8000D3FAC6 /* APIService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIService.swift; sourceTree = "<group>"; };
		0BC19FAF2D80ADCC00D3FAC6 /* APIConfig.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = APIConfig.swift; sourceTree = "<group>"; };
		0BC22BAE2C5BD29300BFF3DB /* VideoView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoView.swift; sourceTree = "<group>"; };
		0BC22BB02C5BE5C300BFF3DB /* QuizBeginView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuizBeginView.swift; sourceTree = "<group>"; };
		0BC89BB72C5D06A300604096 /* QuizView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuizView.swift; sourceTree = "<group>"; };
		0BC9BB3B2DA2B42A00A5EEB0 /* VideoQuizOverlayView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoQuizOverlayView.swift; sourceTree = "<group>"; };
		0BC9BB3D2DA2BADD00A5EEB0 /* VideoTimelineIndicator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoTimelineIndicator.swift; sourceTree = "<group>"; };
		0BCFA63F2D7ECBC3002A9721 /* UserManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserManager.swift; sourceTree = "<group>"; };
		0BCFA6412D7ECEDB002A9721 /* userProgress.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = userProgress.json; sourceTree = "<group>"; };
		0BDA2FD02E1FF83D001EDA06 /* ExerciseResponseView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ExerciseResponseView.swift; sourceTree = "<group>"; };
		0BDEBB292DA2A25F00872B90 /* VideoQuizModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VideoQuizModel.swift; sourceTree = "<group>"; };
		0BE65ACD2E05352900AFA325 /* AnythingLLMService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnythingLLMService.swift; sourceTree = "<group>"; };
		0BE65ACE2E05352900AFA325 /* ChatMessage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChatMessage.swift; sourceTree = "<group>"; };
		0BE65AD32E0546A600AFA325 /* FileUploadManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FileUploadManager.swift; sourceTree = "<group>"; };
		0BE7E6B32D82F03C00774BEE /* EnrolledCoursesView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnrolledCoursesView.swift; sourceTree = "<group>"; };
		0BE7E6B42D82F03C00774BEE /* MarketplaceView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketplaceView.swift; sourceTree = "<group>"; };
		0BE7E6B72D83219200774BEE /* MarketplaceViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MarketplaceViewModel.swift; sourceTree = "<group>"; };
		0BE7E6B92D8346FD00774BEE /* CourseDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CourseDetailView.swift; sourceTree = "<group>"; };
		0BED43882C64BC00004F84C1 /* QuizSummaryView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QuizSummaryView.swift; sourceTree = "<group>"; };
		0BEF37862DEEA3CC00C3F1CE /* TeacherDashboardView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TeacherDashboardView.swift; sourceTree = "<group>"; };
		0BEF37892DEEAB9100C3F1CE /* GameRoomCreationView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameRoomCreationView.swift; sourceTree = "<group>"; };
		0BEF378B2DEEABC600C3F1CE /* GameRoom.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GameRoom.swift; sourceTree = "<group>"; };
		0BF292322E25037E0084426D /* ComingSoonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComingSoonView.swift; sourceTree = "<group>"; };
		0BFF7F392E2A330D00A3DE03 /* CreditAdjustmentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreditAdjustmentView.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		0BDA2F992E1FF38D001EDA06 /* PBXFileSystemSynchronizedBuildFileExceptionSet */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				ConeView.swift,
				CubeView.swift,
				QuestionView.swift,
				ShapeSelectionView.swift,
				SphereView.swift,
				TriangleFoldingView.swift,
				UnfoldGameView.swift,
				VolumeExplorerView.swift,
			);
			target = 0B9EF3D32C57F49B0075159A /* FunMA */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		0B0E8F4F2DEEF74700D31EFF /* Guest */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Guest; sourceTree = "<group>"; };
		0B40BB922D7E0420004CC323 /* LLM */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = LLM; sourceTree = "<group>"; };
		0B7ED9FB2DF1852900AA4FE5 /* Services */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Services; sourceTree = "<group>"; };
		0B91644E2DEFFF12000D6854 /* Student */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Student; sourceTree = "<group>"; };
		0B99905B2E0BE44000D22EE4 /* NativeGame */ = {isa = PBXFileSystemSynchronizedRootGroup; exceptions = (0BDA2F992E1FF38D001EDA06 /* PBXFileSystemSynchronizedBuildFileExceptionSet */, ); explicitFileTypes = {}; explicitFolders = (); path = NativeGame; sourceTree = "<group>"; };
		0BA75F8B2D68C5E5002EF870 /* AR */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = AR; sourceTree = "<group>"; };
		0BAADB7E2D85CDB70072180A /* Auth */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Auth; sourceTree = "<group>"; };
		0BD3C9922D6FB13E00D65704 /* Extensions */ = {isa = PBXFileSystemSynchronizedRootGroup; explicitFileTypes = {}; explicitFolders = (); path = Extensions; sourceTree = "<group>"; };
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		0B9EF3D12C57F49B0075159A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0B842B392E1E9D9300FD9F5E /* PencilKit.framework in Frameworks */,
				0B334EBA2E0AB89500530A76 /* SwiftMath in Frameworks */,
				0B334EB32E0AB80300530A76 /* MarkdownUI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		0B28675F2E26E38E005BD290 /* Developer */ = {
			isa = PBXGroup;
			children = (
				0BFF7F392E2A330D00A3DE03 /* CreditAdjustmentView.swift */,
				0B2867622E273AD9005BD290 /* SchoolCreationView.swift */,
				0B2867602E26E3DC005BD290 /* BatchAccountCreationView.swift */,
			);
			path = Developer;
			sourceTree = "<group>";
		};
		0B842B352E1E3EE000FD9F5E /* Components */ = {
			isa = PBXGroup;
			children = (
				0B89EAC22E2F676900033D21 /* EmptyStateViews.swift */,
				0B89EAC32E2F676900033D21 /* ExerciseCard.swift */,
				0B8E32FD2E2A4F6A00703625 /* DraftAreaComponents.swift */,
				0BAF67632E20F4900033B607 /* LaTeXTextRenderer.swift */,
			);
			path = Components;
			sourceTree = "<group>";
		};
		0B842B372E1E9D9300FD9F5E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				0B842B382E1E9D9300FD9F5E /* PencilKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		0B9EF3CB2C57F49B0075159A = {
			isa = PBXGroup;
			children = (
				0B9EF3D62C57F49B0075159A /* FunMA */,
				0B842B372E1E9D9300FD9F5E /* Frameworks */,
				0B9EF3D52C57F49B0075159A /* Products */,
				0BDA2CF02E1FF042001EDA06 /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		0B9EF3D52C57F49B0075159A /* Products */ = {
			isa = PBXGroup;
			children = (
				0B9EF3D42C57F49B0075159A /* FunMA.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0B9EF3D62C57F49B0075159A /* FunMA */ = {
			isa = PBXGroup;
			children = (
				0B96C3672DF6B69100F90DA9 /* multipleSimulators.config */,
				0B96C3652DF6B67400F90DA9 /* multipleSimulatorsStartup.sh */,
				0B7ED9FB2DF1852900AA4FE5 /* Services */,
				0BA75F942D68CBD8002EF870 /* FunMA-info.plist */,
				0B9EF3D72C57F49B0075159A /* FunMA.swift */,
				0B9EF3D92C57F49B0075159A /* ContentView.swift */,
				0BD3C9922D6FB13E00D65704 /* Extensions */,
				0BBE259E2C904759007BFBB6 /* Data */,
				0BBE259B2C9044DC007BFBB6 /* Models */,
				0BBE25A12C90485E007BFBB6 /* ViewModels */,
				0B9EF3DB2C57F49D0075159A /* Assets.xcassets */,
				0B9EF3DD2C57F49D0075159A /* FunMA.entitlements */,
				0B9EF3DE2C57F49D0075159A /* Preview Content */,
				0B9EF3E82C583A280075159A /* View */,
			);
			path = FunMA;
			sourceTree = "<group>";
		};
		0B9EF3DE2C57F49D0075159A /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				0B9EF3DF2C57F49D0075159A /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		0B9EF3E82C583A280075159A /* View */ = {
			isa = PBXGroup;
			children = (
				0B28675F2E26E38E005BD290 /* Developer */,
				0BF292322E25037E0084426D /* ComingSoonView.swift */,
				0B842B352E1E3EE000FD9F5E /* Components */,
				0B567BB82DFFFB8E0063767D /* DeveloperInteractiveToolsView.swift */,
				0B91644E2DEFFF12000D6854 /* Student */,
				0BEF37872DEEA3CC00C3F1CE /* Teacher */,
				0B0938232DAC130D008BFF34 /* QuizComponents.swift */,
				0BC9BB3D2DA2BADD00A5EEB0 /* VideoTimelineIndicator.swift */,
				0BC9BB3B2DA2B42A00A5EEB0 /* VideoQuizOverlayView.swift */,
				0BAADB7E2D85CDB70072180A /* Auth */,
				0BE7E6B92D8346FD00774BEE /* CourseDetailView.swift */,
				0BE7E6B32D82F03C00774BEE /* EnrolledCoursesView.swift */,
				0BE7E6B42D82F03C00774BEE /* MarketplaceView.swift */,
				0B40BB922D7E0420004CC323 /* LLM */,
				0BA75F8B2D68C5E5002EF870 /* AR */,
				0BA75F892D68C377002EF870 /* InteractiveToolsView.swift */,
				0B9EF3E92C583B210075159A /* SideBarView.swift */,
				0B9EF3E62C583A100075159A /* DashboardView.swift */,
				0B7519842C5A452000A604CA /* CourseView.swift */,
				0BC22BAE2C5BD29300BFF3DB /* VideoView.swift */,
				0BC22BB02C5BE5C300BFF3DB /* QuizBeginView.swift */,
				0BC89BB72C5D06A300604096 /* QuizView.swift */,
				0BED43882C64BC00004F84C1 /* QuizSummaryView.swift */,
				0B509C302C6B2B6B00493EFB /* QuizReviewView.swift */,
				0B0E8F4F2DEEF74700D31EFF /* Guest */,
				0B99905B2E0BE44000D22EE4 /* NativeGame */,
				0B24E1292E0D308F002A18BE /* UserProfileView.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		0BBE259B2C9044DC007BFBB6 /* Models */ = {
			isa = PBXGroup;
			children = (
				0B2867682E273C98005BD290 /* SchoolModel.swift */,
				0B334EB62E0AB85300530A76 /* MarkdownMathRenderer.swift */,
				0B334EAD2E0A7C3E00530A76 /* LaTeXProcessor.swift */,
				0BE65AD32E0546A600AFA325 /* FileUploadManager.swift */,
				0BE65ACD2E05352900AFA325 /* AnythingLLMService.swift */,
				0BE65ACE2E05352900AFA325 /* ChatMessage.swift */,
				0B998FED2E0A991B00D22EE4 /* ClassroomModel.swift */,
				0B998F622E03FD5D00D22EE4 /* CurriculumModel.swift */,
				0B91644C2DEFFEDB000D6854 /* ExerciseModel.swift */,
				0BEF378B2DEEABC600C3F1CE /* GameRoom.swift */,
				0B0938212DABED21008BFF34 /* InteractiveToolsModel.swift */,
				0BDEBB292DA2A25F00872B90 /* VideoQuizModel.swift */,
				0BC19FAF2D80ADCC00D3FAC6 /* APIConfig.swift */,
				0BC19FA82D80AC8000D3FAC6 /* APIService.swift */,
				0BCFA63F2D7ECBC3002A9721 /* UserManager.swift */,
				0B2FD9272D747AC3000EEDB2 /* Message.swift */,
				0B2FD9282D747AC3000EEDB2 /* MessageFormatter.swift */,
				0B2FD91F2D7471BF000EEDB2 /* DeepSeekAPI.swift */,
				0BBE259C2C904540007BFBB6 /* CourseModel.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		0BBE259E2C904759007BFBB6 /* Data */ = {
			isa = PBXGroup;
			children = (
				0B998F642E0403D800D22EE4 /* curriculum.json */,
				0BCFA6412D7ECEDB002A9721 /* userProgress.json */,
				0B40BB932D7E06F5004CC323 /* course.json */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		0BBE25A12C90485E007BFBB6 /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				0B2867652E273C29005BD290 /* SchoolViewModel.swift */,
				0B998FF92E0A99EB00D22EE4 /* ClassroomViewModel.swift */,
				0BE7E6B72D83219200774BEE /* MarketplaceViewModel.swift */,
				0B2FD91D2D74711E000EEDB2 /* ChatViewModel.swift */,
				0BBE25A22C904880007BFBB6 /* CourseViewModel.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		0BDA2CF02E1FF042001EDA06 /* Recovered References */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		0BEF37872DEEA3CC00C3F1CE /* Teacher */ = {
			isa = PBXGroup;
			children = (
				0B89EAC62E2F677A00033D21 /* CreateExerciseView.swift */,
				0B89EAC72E2F677A00033D21 /* ExerciseQuestionsReviewView.swift */,
				0B89EAC82E2F677A00033D21 /* QuestionManagementViews.swift */,
				0BDA2FD02E1FF83D001EDA06 /* ExerciseResponseView.swift */,
				0B9990E12E1BCA00004D99B9 /* AddStudentToClassroomView.swift */,
				0B9990DD2E1BA392004D99B9 /* LaTeXKeyboardView.swift */,
				0B9990DE2E1BA392004D99B9 /* LaTeXTextEditor.swift */,
				0B998FF02E0A993800D22EE4 /* ClassroomDetailView.swift */,
				0B998FF12E0A993800D22EE4 /* ClassroomManagementView.swift */,
				0B998F662E0A652200D22EE4 /* FlippedClassroomResourceView.swift */,
				0B91644A2DEFFEBB000D6854 /* InClassExerciseView.swift */,
				0BEF37892DEEAB9100C3F1CE /* GameRoomCreationView.swift */,
				0BEF37862DEEA3CC00C3F1CE /* TeacherDashboardView.swift */,
			);
			path = Teacher;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		0B9EF3D32C57F49B0075159A /* FunMA */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 0B9EF3E32C57F49D0075159A /* Build configuration list for PBXNativeTarget "FunMA" */;
			buildPhases = (
				0B9EF3D02C57F49B0075159A /* Sources */,
				0B9EF3D12C57F49B0075159A /* Frameworks */,
				0B9EF3D22C57F49B0075159A /* Resources */,
				0B96C3642DF6B43600F90DA9 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				0B0E8F4F2DEEF74700D31EFF /* Guest */,
				0B40BB922D7E0420004CC323 /* LLM */,
				0B7ED9FB2DF1852900AA4FE5 /* Services */,
				0B91644E2DEFFF12000D6854 /* Student */,
				0BA75F8B2D68C5E5002EF870 /* AR */,
				0BAADB7E2D85CDB70072180A /* Auth */,
				0BD3C9922D6FB13E00D65704 /* Extensions */,
			);
			name = FunMA;
			productName = "Luminous Education";
			productReference = 0B9EF3D42C57F49B0075159A /* FunMA.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0B9EF3CC2C57F49B0075159A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1430;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					0B9EF3D32C57F49B0075159A = {
						CreatedOnToolsVersion = 14.3.1;
					};
				};
			};
			buildConfigurationList = 0B9EF3CF2C57F49B0075159A /* Build configuration list for PBXProject "FunMA" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0B9EF3CB2C57F49B0075159A;
			packageReferences = (
				0B334EB12E0AB80300530A76 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */,
				0B334EB82E0AB89500530A76 /* XCRemoteSwiftPackageReference "SwiftMath" */,
			);
			productRefGroup = 0B9EF3D52C57F49B0075159A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				0B9EF3D32C57F49B0075159A /* FunMA */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		0B9EF3D22C57F49B0075159A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0B998F652E0403D800D22EE4 /* curriculum.json in Resources */,
				0B96C3682DF6B69100F90DA9 /* multipleSimulators.config in Resources */,
				0B96C3662DF6B67400F90DA9 /* multipleSimulatorsStartup.sh in Resources */,
				0B40BB952D7E06F5004CC323 /* course.json in Resources */,
				0BCFA6422D7ECEDB002A9721 /* userProgress.json in Resources */,
				0B9EF3E02C57F49D0075159A /* Preview Assets.xcassets in Resources */,
				0B9EF3DC2C57F49D0075159A /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0B96C3642DF6B43600F90DA9 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Type a script or drag a script file from your workspace to insert its path.\ncustom_sim=`xcrun simctl list | grep ‘<Multiple Simulators>’ | awk -F’[()]’ ‘{print $2}’`\nif [ ! -z “${custom_sim}” ] && [ “${TARGET_DEVICE_IDENTIFIER}” = “${custom_sim}” ]; then\n/bin/sh multipleSimulatorsStartup.sh &\nfi\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		0B9EF3D02C57F49B0075159A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				0B2FD91E2D74711E000EEDB2 /* ChatViewModel.swift in Sources */,
				0B89EAC42E2F676900033D21 /* EmptyStateViews.swift in Sources */,
				0B89EAC52E2F676900033D21 /* ExerciseCard.swift in Sources */,
				0B2867612E26E3DC005BD290 /* BatchAccountCreationView.swift in Sources */,
				0BC22BAF2C5BD29300BFF3DB /* VideoView.swift in Sources */,
				0B89EAC92E2F677A00033D21 /* CreateExerciseView.swift in Sources */,
				0B89EACA2E2F677A00033D21 /* QuestionManagementViews.swift in Sources */,
				0B89EACB2E2F677A00033D21 /* ExerciseQuestionsReviewView.swift in Sources */,
				0B0938222DABED21008BFF34 /* InteractiveToolsModel.swift in Sources */,
				0BE7E6BA2D8346FD00774BEE /* CourseDetailView.swift in Sources */,
				0B509C312C6B2B6B00493EFB /* QuizReviewView.swift in Sources */,
				0BEF37882DEEA3CC00C3F1CE /* TeacherDashboardView.swift in Sources */,
				0BC9BB3C2DA2B42A00A5EEB0 /* VideoQuizOverlayView.swift in Sources */,
				0BF292332E25037E0084426D /* ComingSoonView.swift in Sources */,
				0B9EF3DA2C57F49B0075159A /* ContentView.swift in Sources */,
				0B998FEE2E0A991B00D22EE4 /* ClassroomModel.swift in Sources */,
				0BDA2FD12E1FF83D001EDA06 /* ExerciseResponseView.swift in Sources */,
				0BBE259D2C904540007BFBB6 /* CourseModel.swift in Sources */,
				0B334EAE2E0A7C3E00530A76 /* LaTeXProcessor.swift in Sources */,
				0BED43892C64BC00004F84C1 /* QuizSummaryView.swift in Sources */,
				0B9990E22E1BCA00004D99B9 /* AddStudentToClassroomView.swift in Sources */,
				0B998F672E0A652200D22EE4 /* FlippedClassroomResourceView.swift in Sources */,
				0BE7E6B52D82F03C00774BEE /* EnrolledCoursesView.swift in Sources */,
				0BE7E6B62D82F03C00774BEE /* MarketplaceView.swift in Sources */,
				0B9EF3EA2C583B210075159A /* SideBarView.swift in Sources */,
				0BE65ACF2E05352900AFA325 /* ChatMessage.swift in Sources */,
				0BE65AD02E05352900AFA325 /* AnythingLLMService.swift in Sources */,
				0B8E32FE2E2A4F6A00703625 /* DraftAreaComponents.swift in Sources */,
				0BEF378A2DEEAB9100C3F1CE /* GameRoomCreationView.swift in Sources */,
				0B2867662E273C29005BD290 /* SchoolViewModel.swift in Sources */,
				0B7519852C5A452000A604CA /* CourseView.swift in Sources */,
				0B9EF3E72C583A100075159A /* DashboardView.swift in Sources */,
				0B91644D2DEFFEDB000D6854 /* ExerciseModel.swift in Sources */,
				0B24E12A2E0D308F002A18BE /* UserProfileView.swift in Sources */,
				0B2FD9202D7471BF000EEDB2 /* DeepSeekAPI.swift in Sources */,
				0BCFA6402D7ECBC3002A9721 /* UserManager.swift in Sources */,
				0BE7E6B82D83219200774BEE /* MarketplaceViewModel.swift in Sources */,
				0B2FD9292D747AC3000EEDB2 /* Message.swift in Sources */,
				0BC19FB02D80ADCC00D3FAC6 /* APIConfig.swift in Sources */,
				0BEF378C2DEEABC600C3F1CE /* GameRoom.swift in Sources */,
				0B2FD92A2D747AC3000EEDB2 /* MessageFormatter.swift in Sources */,
				0BC19FAE2D80AC8000D3FAC6 /* APIService.swift in Sources */,
				0BAF67642E20F4900033B607 /* LaTeXTextRenderer.swift in Sources */,
				0BC9BB3E2DA2BADD00A5EEB0 /* VideoTimelineIndicator.swift in Sources */,
				0B334EB72E0AB85300530A76 /* MarkdownMathRenderer.swift in Sources */,
				0B2867632E273AD9005BD290 /* SchoolCreationView.swift in Sources */,
				0B998FFA2E0A99EB00D22EE4 /* ClassroomViewModel.swift in Sources */,
				0BFF7F3A2E2A330D00A3DE03 /* CreditAdjustmentView.swift in Sources */,
				0B998F632E03FD5D00D22EE4 /* CurriculumModel.swift in Sources */,
				0BA75F8A2D68C377002EF870 /* InteractiveToolsView.swift in Sources */,
				0B9990DF2E1BA392004D99B9 /* LaTeXTextEditor.swift in Sources */,
				0B9990E02E1BA392004D99B9 /* LaTeXKeyboardView.swift in Sources */,
				0BC89BB82C5D06A300604096 /* QuizView.swift in Sources */,
				0B9EF3D82C57F49B0075159A /* FunMA.swift in Sources */,
				0B91644B2DEFFEBB000D6854 /* InClassExerciseView.swift in Sources */,
				0B567BB92DFFFB8E0063767D /* DeveloperInteractiveToolsView.swift in Sources */,
				0BBE25A32C904880007BFBB6 /* CourseViewModel.swift in Sources */,
				0BDEBB2A2DA2A25F00872B90 /* VideoQuizModel.swift in Sources */,
				0BE65AD42E0546A600AFA325 /* FileUploadManager.swift in Sources */,
				0B0938242DAC130D008BFF34 /* QuizComponents.swift in Sources */,
				0BC22BB12C5BE5C300BFF3DB /* QuizBeginView.swift in Sources */,
				0B998FF22E0A993800D22EE4 /* ClassroomDetailView.swift in Sources */,
				0B2867692E273C98005BD290 /* SchoolModel.swift in Sources */,
				0B998FF32E0A993800D22EE4 /* (null) in Sources */,
				0B998FF42E0A993800D22EE4 /* ClassroomManagementView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		0B9EF3E12C57F49D0075159A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		0B9EF3E22C57F49D0075159A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = NO;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
			};
			name = Release;
		};
		0B9EF3E42C57F49D0075159A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FunMA/FunMA.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = 47YJ78ZN3U;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "FunMA-info.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				INFOPLIST_KEY_NSCameraUsageDescription = "Allow access to the camera for augmented reality features.";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "This app needs access to local network to connect to the game server";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 13.3;
				MARKETING_VERSION = 0.2.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.inspire.FunMA;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		0B9EF3E52C57F49D0075159A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = FunMA/FunMA.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_ASSET_PATHS = "";
				DEVELOPMENT_TEAM = 47YJ78ZN3U;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "FunMA-info.plist";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.education";
				INFOPLIST_KEY_NSCameraUsageDescription = "Allow access to the camera for augmented reality features.";
				INFOPLIST_KEY_NSLocalNetworkUsageDescription = "This app needs access to local network to connect to the game server";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 16.4;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MACOSX_DEPLOYMENT_TARGET = 13.3;
				MARKETING_VERSION = 0.2.3;
				PRODUCT_BUNDLE_IDENTIFIER = com.inspire.FunMA;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator macosx";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		0B9EF3CF2C57F49B0075159A /* Build configuration list for PBXProject "FunMA" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0B9EF3E12C57F49D0075159A /* Debug */,
				0B9EF3E22C57F49D0075159A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		0B9EF3E32C57F49D0075159A /* Build configuration list for PBXNativeTarget "FunMA" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				0B9EF3E42C57F49D0075159A /* Debug */,
				0B9EF3E52C57F49D0075159A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		0B334EB12E0AB80300530A76 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/gonzalezreal/swift-markdown-ui";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.4.1;
			};
		};
		0B334EB82E0AB89500530A76 /* XCRemoteSwiftPackageReference "SwiftMath" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/mgriebling/SwiftMath.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.7.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		0B334EB22E0AB80300530A76 /* MarkdownUI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 0B334EB12E0AB80300530A76 /* XCRemoteSwiftPackageReference "swift-markdown-ui" */;
			productName = MarkdownUI;
		};
		0B334EB92E0AB89500530A76 /* SwiftMath */ = {
			isa = XCSwiftPackageProductDependency;
			package = 0B334EB82E0AB89500530A76 /* XCRemoteSwiftPackageReference "SwiftMath" */;
			productName = SwiftMath;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 0B9EF3CC2C57F49B0075159A /* Project object */;
}
