# WebSocket Communication Flow Between Teacher and Students

```mermaid
flowchart TD
    %% Main App Structure
    App[FunMA App] -->|Initializes| GameService[GameService]
    App -->|Loads| GameRoomManager[GameRoomManager]
    
    %% User Management
    UserManager{UserManager} -->|Authenticates| Teacher((Teacher))
    UserManager -->|Authenticates| Student((Student))
    UserManager -->|Authenticates| Guest((Guest))
    
    %% Teacher Game Room Flow
    Teacher -->|Creates| GameRoomCreation[GameRoomCreationView]
    GameRoomCreation -->|Selects| GradeSelection[Grade Selection]
    GradeSelection -->|Chooses| TopicSelection[Topic Selection]
    TopicSelection -->|Picks| GameSelection[Game Selection]
    GameSelection -->|Launches| GameRoom[Game Room]
    
    %% Student Join Flow
    Student -->|Enters PIN| GuestGameJoin[GuestGameJoinView]
    Guest -->|Enters PIN| GuestGameJoin
    GuestGameJoin -->|Validates| PINValidation[PIN Validation]
    PINValidation -->|Connects| GameRoom
    
    %% WebSocket Communication
    GameRoom -->|Establishes| WebSocketConnection[WebSocket Connection]
    WebSocketConnection -->|Sends| PlayerJoined[Player Joined Message]
    WebSocketConnection -->|Sends| PlayerLeft[Player Left Message]
    WebSocketConnection -->|Sends| GameStarted[Game Started Message]
    WebSocketConnection -->|Sends| GameEnded[Game Ended Message]
    WebSocketConnection -->|Sends| ErrorMessage[Error Message]
    
    %% Real-time Updates
    PlayerJoined -->|Broadcasts| TeacherUpdate[Teacher UI Update]
    PlayerJoined -->|Broadcasts| StudentUpdate[Student UI Update]
    PlayerLeft -->|Broadcasts| TeacherUpdate
    PlayerLeft -->|Broadcasts| StudentUpdate
    GameStarted -->|Broadcasts| TeacherUpdate
    GameStarted -->|Broadcasts| StudentUpdate
    GameEnded -->|Broadcasts| TeacherUpdate
    GameEnded -->|Broadcasts| StudentUpdate
    
    %% Teacher Controls
    GameRoom -->|Teacher Controls| StartGame[Start Game Button]
    GameRoom -->|Teacher Controls| EndGame[End Game Button]
    GameRoom -->|Teacher Controls| CancelGame[Cancel Game Button]
    
    %% Student Actions
    GameRoom -->|Student Actions| JoinGame[Join Game Action]
    GameRoom -->|Student Actions| LeaveGame[Leave Game Action]
    GameRoom -->|Student Actions| GameInteraction[Game Interaction]
    
    %% Message Types
    PlayerJoined -->|Contains| PlayerData[Player Data]
    PlayerData -->|Includes| PlayerID[Player ID]
    PlayerData -->|Includes| Username[Username]
    PlayerData -->|Includes| Role[Role]
    PlayerData -->|Includes| JoinedAt[Joined At]
    
    %% Error Handling
    ErrorMessage -->|Handles| NetworkError[Network Error]
    ErrorMessage -->|Handles| InvalidPIN[Invalid PIN]
    ErrorMessage -->|Handles| RoomFull[Room Full]
    ErrorMessage -->|Handles| GameStarted[Game Already Started]
    
    %% Connection Management
    WebSocketConnection -->|Maintains| PingPong[Ping/Pong Heartbeat]
    WebSocketConnection -->|Handles| Reconnection[Auto Reconnection]
    WebSocketConnection -->|Manages| ConnectionTimeout[Connection Timeout]
    
    %% Data Flow
    GameRoomManager -->|Updates| GameRoom
    GameService -->|Manages| WebSocketConnection
    GameService -->|Handles| MessageEncoding[Message Encoding/Decoding]
    
    %% Security
    PINValidation -->|Validates| SecurityCheck[Security Check]
    SecurityCheck -->|Enforces| RoleAccess[Role-based Access]
    SecurityCheck -->|Limits| ConnectionLimit[Connection Limits]
    
    %% Styling
    classDef view fill:#f9f,stroke:#333,stroke-width:2px
    classDef model fill:#bbf,stroke:#333,stroke-width:2px
    classDef manager fill:#bfb,stroke:#333,stroke-width:2px
    classDef service fill:#fbb,stroke:#333,stroke-width:2px
    classDef message fill:#ffb,stroke:#333,stroke-width:2px
    classDef control fill:#bff,stroke:#333,stroke-width:2px
    classDef error fill:#fbb,stroke:#333,stroke-width:2px
    
    class App,GameRoomCreation,GuestGameJoin,GameRoom,TeacherUpdate,StudentUpdate view
    class Teacher,Student,Guest model
    class UserManager,GameRoomManager manager
    class GameService,WebSocketConnection,MessageEncoding service
    class PlayerJoined,PlayerLeft,GameStarted,GameEnded,ErrorMessage message
    class StartGame,EndGame,CancelGame,JoinGame,LeaveGame control
    class NetworkError,InvalidPIN,RoomFull error
```

## WebSocket Communication Components

### Core Services
- **GameService**: Manages WebSocket connections and message handling
- **GameRoomManager**: Coordinates game room state and user interactions
- **WebSocket Connection**: Real-time bidirectional communication channel

### Message Types
- **PlayerJoined**: Broadcasts when a student joins the game room
- **PlayerLeft**: Notifies when a student leaves
- **GameStarted**: Signals game commencement
- **GameEnded**: Indicates game completion
- **ErrorMessage**: Handles various error scenarios

### User Flows
- **Teacher Flow**: Create → Select → Launch → Control → End
- **Student Flow**: Enter PIN → Validate → Join → Participate → Leave

### Real-time Features
- **Instant Updates**: All participants receive immediate notifications
- **Bidirectional Communication**: Both teachers and students can send/receive
- **Automatic Reconnection**: Handles network interruptions gracefully
- **Heartbeat Monitoring**: Maintains connection health with ping/pong

### Security & Error Handling
- **PIN Validation**: Ensures only authorized users can join
- **Role-based Access**: Teachers have control privileges
- **Connection Limits**: Prevents overcrowding
- **Error Recovery**: Graceful handling of various failure scenarios

## WebSocket Message Types

### Message Structure
```json
{
  "type": "playerJoined|playerLeft|gameStarted|gameEnded|error",
  "data": {
    // Message-specific data
  }
}
```

### Message Types

1. **playerJoined(Player)**
   - Sent when a student joins the game room
   - Contains player information (id, username, role, joinedAt)

2. **playerLeft(String)**
   - Sent when a student leaves the game room
   - Contains the player ID who left

3. **gameStarted**
   - Sent when teacher starts the game
   - No additional data required

4. **gameEnded**
   - Sent when teacher ends the game
   - No additional data required

5. **error(String)**
   - Sent when an error occurs
   - Contains error message

## Connection Flow

### Teacher Side
1. Teacher creates game room → WebSocket connection established
2. Teacher receives real-time updates of:
   - Students joining/leaving
   - Game state changes
   - Error messages

### Student Side
1. Student enters PIN → WebSocket connection established
2. Student receives real-time updates of:
   - Other students joining/leaving
   - Game state changes
   - Error messages

### Real-time Features
- **Instant Updates**: All participants see changes immediately
- **Bidirectional Communication**: Both teacher and students can send/receive messages
- **Automatic Reconnection**: Handles network disconnections gracefully
- **Ping/Pong**: Keeps connections alive with periodic heartbeats

## Error Handling

- **Network Issues**: Automatic reconnection attempts
- **Invalid PIN**: Error message sent to student
- **Game Room Full**: Error message sent to student
- **Game Already Started**: Error message sent to late joiners
- **Connection Timeout**: Automatic cleanup and error notification

## Security Considerations

- **PIN Validation**: Only valid PIN codes allow connection
- **Role-based Access**: Teachers have control privileges, students are participants
- **Message Validation**: All incoming messages are validated before processing
- **Connection Limits**: Maximum number of students per game room 